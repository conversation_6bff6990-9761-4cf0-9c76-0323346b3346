<template>
  <div class="apps-page">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          Applications
        </h1>
        <p class="mt-1 text-sm text-gray-500">
          Create and manage your AI applications
        </p>
      </div>
      
      <button
        class="btn-primary"
        @click="showCreateModal = true"
      >
        <PlusIcon class="w-4 h-4 mr-2" />
        Create Application
      </button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="mb-6 flex items-center space-x-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search applications..."
            class="input pl-10"
          />
        </div>
      </div>
      
      <select v-model="selectedCategory" class="input w-40">
        <option value="all">All Categories</option>
        <option value="chatbot">Chatbot</option>
        <option value="agent">Agent</option>
        <option value="workflow">Workflow</option>
        <option value="text-generation">Text Generation</option>
      </select>
    </div>

    <!-- 应用列表 -->
    <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="i in 6" :key="i" class="animate-pulse">
        <div class="card">
          <div class="card-body">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="apps.length === 0" class="text-center py-12">
      <RectangleStackIcon class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">No applications</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new application.</p>
      <div class="mt-6">
        <button
          @click="showCreateModal = true"
          class="btn-primary"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          Create Application
        </button>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="app in filteredApps"
        :key="app.id"
        class="card hover:shadow-md transition-shadow cursor-pointer"
        @click="$router.push(`/apps/${app.id}`)"
      >
        <div class="card-body">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 mb-1">
                {{ app.name }}
              </h3>
              <p class="text-sm text-gray-500 mb-3">
                {{ app.description || 'No description' }}
              </p>
              
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span class="badge badge-primary">
                  {{ app.type }}
                </span>
                <span>{{ formatDate(app.created_at) }}</span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                @click.stop="editApp(app)"
                class="p-1 text-gray-400 hover:text-gray-600"
              >
                <PencilIcon class="h-4 w-4" />
              </button>
              <button
                @click.stop="deleteApp(app)"
                class="p-1 text-gray-400 hover:text-red-600"
              >
                <TrashIcon class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建应用模态框占位 -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 class="text-lg font-medium mb-4">Create Application</h2>
        <p class="text-gray-500 mb-4">This is a placeholder for the create application modal.</p>
        <div class="flex justify-end space-x-3">
          <button @click="showCreateModal = false" class="btn-secondary">
            Cancel
          </button>
          <button @click="showCreateModal = false" class="btn-primary">
            Create
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  RectangleStackIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

// 模拟数据
const apps = ref([
  {
    id: '1',
    name: 'Customer Support Bot',
    description: 'AI-powered customer support chatbot',
    type: 'chatbot',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Content Generator',
    description: 'Generate marketing content automatically',
    type: 'text-generation',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: '3',
    name: 'Data Analysis Agent',
    description: 'Intelligent data analysis and insights',
    type: 'agent',
    created_at: '2024-01-13T09:15:00Z'
  }
])

const isLoading = ref(false)
const showCreateModal = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('all')

// 过滤应用
const filteredApps = computed(() => {
  let filtered = apps.value

  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(app => app.type === selectedCategory.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(app =>
      app.name.toLowerCase().includes(query) ||
      app.description?.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// 编辑应用
const editApp = (app: any) => {
  console.log('Edit app:', app)
}

// 删除应用
const deleteApp = (app: any) => {
  if (confirm(`Are you sure you want to delete "${app.name}"?`)) {
    const index = apps.value.findIndex(a => a.id === app.id)
    if (index > -1) {
      apps.value.splice(index, 1)
    }
  }
}
</script>
