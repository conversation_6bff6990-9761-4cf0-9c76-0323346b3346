hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@changesets/apply-release-plan@7.0.12':
    '@changesets/apply-release-plan': private
  '@changesets/assemble-release-plan@6.0.9':
    '@changesets/assemble-release-plan': private
  '@changesets/changelog-git@0.2.1':
    '@changesets/changelog-git': private
  '@changesets/config@3.1.1':
    '@changesets/config': private
  '@changesets/errors@0.2.0':
    '@changesets/errors': private
  '@changesets/get-dependents-graph@2.1.3':
    '@changesets/get-dependents-graph': private
  '@changesets/get-release-plan@4.0.13':
    '@changesets/get-release-plan': private
  '@changesets/get-version-range-type@0.4.0':
    '@changesets/get-version-range-type': private
  '@changesets/git@3.0.4':
    '@changesets/git': private
  '@changesets/logger@0.1.1':
    '@changesets/logger': private
  '@changesets/parse@0.4.1':
    '@changesets/parse': private
  '@changesets/pre@2.0.2':
    '@changesets/pre': private
  '@changesets/read@0.6.5':
    '@changesets/read': private
  '@changesets/should-skip-package@0.1.2':
    '@changesets/should-skip-package': private
  '@changesets/types@6.1.0':
    '@changesets/types': private
  '@changesets/write@0.4.0':
    '@changesets/write': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.30.1(jiti@1.21.7))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.30.1':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@headlessui/vue@1.7.23(vue@3.5.17(typescript@5.8.3))':
    '@headlessui/vue': private
  '@heroicons/vue@2.2.0(vue@3.5.17(typescript@5.8.3))':
    '@heroicons/vue': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@intlify/core-base@9.14.4':
    '@intlify/core-base': private
  '@intlify/message-compiler@9.14.4':
    '@intlify/message-compiler': private
  '@intlify/shared@9.14.4':
    '@intlify/shared': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@manypkg/find-root@1.1.0':
    '@manypkg/find-root': private
  '@manypkg/get-packages@1.1.3':
    '@manypkg/get-packages': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rolldown/pluginutils@1.0.0-beta.23':
    '@rolldown/pluginutils': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@tanstack/match-sorter-utils@8.19.4':
    '@tanstack/match-sorter-utils': private
  '@tanstack/query-core@5.81.5':
    '@tanstack/query-core': private
  '@tanstack/virtual-core@3.13.12':
    '@tanstack/virtual-core': private
  '@tanstack/vue-query@5.81.5(vue@3.5.17(typescript@5.8.3))':
    '@tanstack/vue-query': private
  '@tanstack/vue-virtual@3.13.12(vue@3.5.17(typescript@5.8.3))':
    '@tanstack/vue-virtual': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/web-bluetooth@0.0.20':
    '@types/web-bluetooth': private
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@7.18.0(eslint@9.30.1(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@7.18.0(eslint@9.30.1(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': private
  '@vitejs/plugin-vue-jsx@4.2.0(vite@5.4.19(@types/node@20.19.4))(vue@3.5.17(typescript@5.8.3))':
    '@vitejs/plugin-vue-jsx': private
  '@vitejs/plugin-vue@5.2.4(vite@5.4.19(@types/node@20.19.4))(vue@3.5.17(typescript@5.8.3))':
    '@vitejs/plugin-vue': private
  '@vitest/expect@2.1.9':
    '@vitest/expect': private
  '@vitest/mocker@2.1.9(vite@5.4.19(@types/node@20.19.4))':
    '@vitest/mocker': private
  '@vitest/pretty-format@2.1.9':
    '@vitest/pretty-format': private
  '@vitest/runner@2.1.9':
    '@vitest/runner': private
  '@vitest/snapshot@2.1.9':
    '@vitest/snapshot': private
  '@vitest/spy@2.1.9':
    '@vitest/spy': private
  '@vitest/utils@2.1.9':
    '@vitest/utils': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue-flow/background@1.3.2(@vue-flow/core@1.45.0(vue@3.5.17(typescript@5.8.3)))(vue@3.5.17(typescript@5.8.3))':
    '@vue-flow/background': private
  '@vue-flow/controls@1.1.2(@vue-flow/core@1.45.0(vue@3.5.17(typescript@5.8.3)))(vue@3.5.17(typescript@5.8.3))':
    '@vue-flow/controls': private
  '@vue-flow/core@1.45.0(vue@3.5.17(typescript@5.8.3))':
    '@vue-flow/core': private
  '@vue-flow/minimap@1.5.3(@vue-flow/core@1.45.0(vue@3.5.17(typescript@5.8.3)))(vue@3.5.17(typescript@5.8.3))':
    '@vue-flow/minimap': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/language-core@2.2.12(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.17':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.17':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.17':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.17(vue@3.5.17(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  '@vueuse/core@11.3.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/metadata@11.3.0':
    '@vueuse/metadata': private
  '@vueuse/shared@11.3.0(vue@3.5.17(typescript@5.8.3))':
    '@vueuse/shared': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  alien-signals@1.0.13:
    alien-signals: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  assertion-error@2.0.1:
    assertion-error: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  balanced-match@1.0.2:
    balanced-match: private
  better-path-resolve@1.0.0:
    better-path-resolve: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  cac@6.7.14:
    cac: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chai@5.2.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  chardet@0.7.0:
    chardet: private
  check-error@2.1.1:
    check-error: private
  chokidar@3.6.0:
    chokidar: private
  ci-info@3.9.0:
    ci-info: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  commander@13.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-color@3.1.0:
    d3-color: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  dayjs@1.11.13:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  detect-indent@6.1.0:
    detect-indent: private
  didyoumean@1.2.2:
    didyoumean: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.179:
    electron-to-chromium: private
  emoji-regex@10.4.0:
    emoji-regex: private
  enquirer@2.4.1:
    enquirer: private
  entities@4.5.0:
    entities: private
  environment@1.1.0:
    environment: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  execa@8.0.1:
    execa: private
  expect-type@1.2.1:
    expect-type: private
  exsolve@1.0.7:
    exsolve: private
  extendable-error@0.1.7:
    extendable-error: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@7.0.1:
    fs-extra: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-stream@8.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  human-id@4.1.1:
    human-id: private
  human-signals@5.0.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-stream@3.0.0:
    is-stream: private
  is-subdir@1.2.0:
    is-subdir: private
  is-windows@1.0.2:
    is-windows: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@4.0.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  ky@1.8.1:
    ky: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@8.3.3:
    listr2: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash@4.17.21:
    lodash: private
  log-update@6.1.0:
    log-update: private
  loupe@3.1.4:
    loupe: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mitt@3.0.1:
    mitt: private
  mlly@1.7.4:
    mlly: private
  monaco-editor@0.45.0:
    monaco-editor: private
  mri@1.2.0:
    mri: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  onetime@6.0.0:
    onetime: private
  optionator@0.9.4:
    optionator: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  outdent@0.5.0:
    outdent: private
  p-filter@2.1.0:
    p-filter: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@2.1.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@0.2.11:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@2.0.1:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  pinia@2.3.1(typescript@5.8.3)(vue@3.5.17(typescript@5.8.3)):
    pinia: private
  pirates@4.0.7:
    pirates: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  read-cache@1.0.0:
    read-cache: private
  read-yaml-file@1.1.0:
    read-yaml-file: private
  readdirp@3.6.0:
    readdirp: private
  remove-accents@0.5.0:
    remove-accents: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.44.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scule@1.3.0:
    scule: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  slash@3.0.0:
    slash: private
  slice-ansi@5.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  spawndamnit@3.0.1:
    spawndamnit: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stackback@0.0.2:
    stackback: private
  state-local@1.0.7:
    state-local: private
  std-env@3.9.0:
    std-env: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.1:
    strip-literal: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tailwindcss@3.4.17:
    tailwindcss: private
  term-size@2.2.1:
    term-size: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@1.2.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici-types@6.21.0:
    undici-types: private
  unimport@3.14.6(rollup@4.44.1):
    unimport: private
  universalify@0.1.2:
    universalify: private
  unplugin-auto-import@0.17.8(@vueuse/core@11.3.0(vue@3.5.17(typescript@5.8.3)))(rollup@4.44.1):
    unplugin-auto-import: private
  unplugin-vue-components@0.26.0(@babel/parser@7.28.0)(rollup@4.44.1)(vue@3.5.17(typescript@5.8.3)):
    unplugin-vue-components: private
  unplugin@1.16.1:
    unplugin: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@10.0.0:
    uuid: private
  vite-node@2.1.9(@types/node@20.19.4):
    vite-node: private
  vite@5.4.19(@types/node@20.19.4):
    vite: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.17(typescript@5.8.3)):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@9.30.1(jiti@1.21.7)):
    vue-eslint-parser: private
  vue-i18n@9.14.4(vue@3.5.17(typescript@5.8.3)):
    vue-i18n: private
  vue-router@4.5.1(vue@3.5.17(typescript@5.8.3)):
    vue-router: private
  vue-tsc@2.2.12(typescript@5.8.3):
    vue-tsc: private
  vue@3.5.17(typescript@5.8.3):
    vue: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yallist@3.1.1:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Thu, 03 Jul 2025 07:42:21 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-musl@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - fsevents@2.3.3
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: F:\.pnpm-store\v10
virtualStoreDir: F:\AI\dify-vue\node_modules\.pnpm
virtualStoreDirMaxLength: 60
