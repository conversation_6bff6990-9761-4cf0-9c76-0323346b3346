import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 布局组件
const DefaultLayout = () => import('@/layouts/DefaultLayout.vue')
const AuthLayout = () => import('@/layouts/AuthLayout.vue')

// 页面组件 - 懒加载
const Dashboard = () => import('@/pages/dashboard/index.vue')
const Apps = () => import('@/pages/apps/index.vue')
const AppDetail = () => import('@/pages/apps/[id]/index.vue')
const AppEdit = () => import('@/pages/apps/[id]/edit.vue')
const Workflow = () => import('@/pages/workflow/index.vue')
const WorkflowEditor = () => import('@/pages/workflow/[id]/editor.vue')
const Datasets = () => import('@/pages/datasets/index.vue')
const DatasetDetail = () => import('@/pages/datasets/[id]/index.vue')
const Models = () => import('@/pages/models/index.vue')
const Tools = () => import('@/pages/tools/index.vue')
const Monitoring = () => import('@/pages/monitoring/index.vue')
const Annotation = () => import('@/pages/annotation/index.vue')
const Settings = () => import('@/pages/settings/index.vue')
const Login = () => import('@/pages/auth/login.vue')
const Register = () => import('@/pages/auth/register.vue')
const ForgotPassword = () => import('@/pages/auth/forgot-password.vue')
const TestLogin = () => import('@/pages/test-login.vue')
const TestAuth = () => import('@/pages/test-auth.vue')
const TailwindTest = () => import('@/pages/tailwind-test.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: {
          title: 'Login',
          requiresAuth: false
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: Register,
        meta: {
          title: 'Register',
          requiresAuth: false
        }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: {
          title: 'Forgot Password',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/test-login',
    name: 'TestLogin',
    component: TestLogin,
    meta: {
      title: 'Test Login',
      requiresAuth: false
    }
  },
  {
    path: '/test-auth',
    name: 'TestAuth',
    component: TestAuth,
    meta: {
      title: 'Test Auth',
      requiresAuth: false
    }
  },
  {
    path: '/tailwind-test',
    name: 'TailwindTest',
    component: TailwindTest,
    meta: {
      title: 'Tailwind Test',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: DefaultLayout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/apps'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard',
          icon: 'HomeIcon'
        }
      },
      {
        path: 'apps',
        name: 'Apps',
        component: Apps,
        meta: {
          title: 'Applications',
          icon: 'RectangleStackIcon'
        }
      },
      {
        path: 'apps/:id',
        name: 'AppDetail',
        component: AppDetail,
        meta: {
          title: 'Application Detail',
          breadcrumb: [
            { name: 'Applications', path: '/apps' },
            { name: 'Detail' }
          ]
        }
      },
      {
        path: 'apps/:id/edit',
        name: 'AppEdit',
        component: AppEdit,
        meta: {
          title: 'Edit Application',
          breadcrumb: [
            { name: 'Applications', path: '/apps' },
            { name: 'Edit' }
          ]
        }
      },
      {
        path: 'workflow',
        name: 'Workflow',
        component: Workflow,
        meta: {
          title: 'Workflows',
          icon: 'ShareIcon'
        }
      },
      {
        path: 'workflow/:id/editor',
        name: 'WorkflowEditor',
        component: WorkflowEditor,
        meta: {
          title: 'Workflow Editor',
          breadcrumb: [
            { name: 'Workflows', path: '/workflow' },
            { name: 'Editor' }
          ]
        }
      },
      {
        path: 'datasets',
        name: 'Datasets',
        component: Datasets,
        meta: {
          title: 'Knowledge Base',
          icon: 'BookOpenIcon'
        }
      },
      {
        path: 'datasets/:id',
        name: 'DatasetDetail',
        component: DatasetDetail,
        meta: {
          title: 'Dataset Detail',
          breadcrumb: [
            { name: 'Knowledge Base', path: '/datasets' },
            { name: 'Detail' }
          ]
        }
      },
      {
        path: 'models',
        name: 'Models',
        component: Models,
        meta: {
          title: 'Models',
          icon: 'CpuChipIcon'
        }
      },
      {
        path: 'tools',
        name: 'Tools',
        component: Tools,
        meta: {
          title: 'Tools',
          icon: 'WrenchScrewdriverIcon'
        }
      },
      {
        path: 'monitoring',
        name: 'Monitoring',
        component: Monitoring,
        meta: {
          title: 'Monitoring',
          icon: 'ChartBarIcon'
        }
      },
      {
        path: 'annotation',
        name: 'Annotation',
        component: Annotation,
        meta: {
          title: 'Annotation',
          icon: 'TagIcon'
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: {
          title: 'Settings',
          icon: 'CogIcon'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/error/404.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Dify Admin`
  }

  // 检查认证状态
  const isAuthenticated = localStorage.getItem('dify_auth_token')

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/auth/login')
  } else if (to.path.startsWith('/auth') && isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
