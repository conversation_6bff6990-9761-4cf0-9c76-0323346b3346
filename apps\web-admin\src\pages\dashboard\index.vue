<template>
  <div class="dashboard">
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
      <p class="mt-1 text-sm text-gray-500">
        Welcome back! Here's what's happening with your AI applications.
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <RectangleStackIcon class="h-8 w-8 text-primary-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Applications</p>
              <p class="text-2xl font-semibold text-gray-900">12</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ChatBubbleLeftRightIcon class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Active Conversations</p>
              <p class="text-2xl font-semibold text-gray-900">1,234</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <BookOpenIcon class="h-8 w-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Knowledge Bases</p>
              <p class="text-2xl font-semibold text-gray-900">8</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CpuChipIcon class="h-8 w-8 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">API Calls Today</p>
              <p class="text-2xl font-semibold text-gray-900">5,678</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">Recent Applications</h3>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div v-for="app in recentApps" :key="app.id" class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    <RectangleStackIcon class="h-4 w-4 text-primary-600" />
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ app.name }}</p>
                  <p class="text-xs text-gray-500">{{ app.type }}</p>
                </div>
              </div>
              <div class="text-xs text-gray-500">
                {{ formatDate(app.created_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">System Status</h3>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">API Status</span>
              <span class="badge badge-success">Operational</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Database</span>
              <span class="badge badge-success">Healthy</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Vector Database</span>
              <span class="badge badge-success">Connected</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Model Services</span>
              <span class="badge badge-warning">Degraded</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  RectangleStackIcon,
  ChatBubbleLeftRightIcon,
  BookOpenIcon,
  CpuChipIcon
} from '@heroicons/vue/24/outline'

const recentApps = ref([
  {
    id: '1',
    name: 'Customer Support Bot',
    type: 'Chatbot',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Content Generator',
    type: 'Text Generation',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: '3',
    name: 'Data Analysis Agent',
    type: 'Agent',
    created_at: '2024-01-13T09:15:00Z'
  }
])

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>
