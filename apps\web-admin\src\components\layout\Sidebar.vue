<template>
  <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
       :class="{ '-translate-x-full': !isOpen, 'translate-x-0': isOpen }">

    <!-- Logo区域 -->
    <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
      <div class="flex items-center">
        <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-sm">D</span>
        </div>
        <span class="ml-2 text-xl font-semibold text-gray-900">Dify</span>
      </div>

      <!-- 移动端关闭按钮 -->
      <button
        @click="$emit('close')"
        class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
      >
        <XMarkIcon class="h-6 w-6" />
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="mt-6 px-3">
      <div class="space-y-1">
        <RouterLink
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.path"
          class="nav-link"
          :class="isActiveRoute(item.path) ? 'nav-link-active' : 'nav-link-inactive'"
        >
          <component :is="item.icon" class="mr-3 h-5 w-5 flex-shrink-0" />
          {{ item.name }}

          <!-- 徽章 -->
          <span v-if="item.badge" class="ml-auto badge badge-primary">
            {{ item.badge }}
          </span>
        </RouterLink>
      </div>

      <!-- 分组菜单 -->
      <div class="mt-8">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          Management
        </h3>
        <div class="mt-2 space-y-1">
          <RouterLink
            v-for="item in managementItems"
            :key="item.name"
            :to="item.path"
            class="nav-link"
            :class="isActiveRoute(item.path) ? 'nav-link-active' : 'nav-link-inactive'"
          >
            <component :is="item.icon" class="mr-3 h-5 w-5 flex-shrink-0" />
            {{ item.name }}
          </RouterLink>
        </div>
      </div>
    </nav>

    <!-- 底部用户信息 -->
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
      <div class="flex items-center">
        <img
          src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
          alt="User avatar"
          class="h-8 w-8 rounded-full"
        />
        <div class="ml-3 flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 truncate">
            John Doe
          </p>
          <p class="text-xs text-gray-500 truncate">
            <EMAIL>
          </p>
        </div>

        <!-- 用户菜单 -->
        <div class="relative">
          <button
            @click="showUserMenu = !showUserMenu"
            class="p-1 rounded-full text-gray-400 hover:text-gray-500"
          >
            <EllipsisVerticalIcon class="h-5 w-5" />
          </button>

          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <div
              v-if="showUserMenu"
              class="absolute bottom-full right-0 mb-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
            >
              <div class="py-1">
                <a href="#" @click="showUserMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Profile
                </a>
                <a href="#" @click="showUserMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Settings
                </a>
                <a href="#" @click="showUserMenu = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Sign out
                </a>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  XMarkIcon,
  EllipsisVerticalIcon,
  HomeIcon,
  RectangleStackIcon,
  ShareIcon,
  BookOpenIcon,
  CpuChipIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  TagIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

interface Props {
  isOpen: boolean
}

defineProps<Props>()
defineEmits<{
  close: []
}>()

const route = useRoute()
const showUserMenu = ref(false)

// 主导航菜单
const navigationItems = [
  { name: 'Dashboard', path: '/dashboard', icon: HomeIcon },
  { name: 'Applications', path: '/apps', icon: RectangleStackIcon },
  { name: 'Workflows', path: '/workflow', icon: ShareIcon },
  { name: 'Knowledge Base', path: '/datasets', icon: BookOpenIcon },
  { name: 'Models', path: '/models', icon: CpuChipIcon },
  { name: 'Tools', path: '/tools', icon: WrenchScrewdriverIcon }
]

// 管理菜单
const managementItems = [
  { name: 'Monitoring', path: '/monitoring', icon: ChartBarIcon },
  { name: 'Annotation', path: '/annotation', icon: TagIcon },
  { name: 'Settings', path: '/settings', icon: CogIcon }
]

// 判断当前路由是否激活
const isActiveRoute = (path: string) => {
  return route.path.startsWith(path)
}
</script>
