#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules/resolve/bin/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules/resolve/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules/resolve/bin/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules/resolve/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/resolve@1.22.10/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../resolve@1.22.10/node_modules/resolve/bin/resolve" "$@"
else
  exec node  "$basedir/../../../../../resolve@1.22.10/node_modules/resolve/bin/resolve" "$@"
fi
