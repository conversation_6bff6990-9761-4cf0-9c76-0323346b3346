<!-- 
  Dify 应用管理页面示例
  展示如何将 React 组件迁移到 Vue 3 Composition API
-->
<template>
  <div class="apps-page">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          {{ t('apps.title') }}
        </h1>
        <p class="mt-1 text-sm text-gray-500">
          {{ t('apps.description') }}
        </p>
      </div>
      
      <button
        @click="showCreateModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        <PlusIcon class="w-4 h-4 mr-2" />
        {{ t('apps.create') }}
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="mb-6 flex items-center space-x-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="t('apps.search')"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>
      
      <Listbox v-model="selectedCategory">
        <div class="relative">
          <ListboxButton class="relative w-40 cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500">
            <span class="block truncate">{{ selectedCategory.name }}</span>
            <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon class="h-5 w-5 text-gray-400" />
            </span>
          </ListboxButton>
          
          <transition
            leave-active-class="transition duration-100 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
          >
            <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <ListboxOption
                v-for="category in categories"
                :key="category.id"
                v-slot="{ active, selected }"
                :value="category"
                as="template"
              >
                <li :class="[
                  active ? 'bg-primary-100 text-primary-900' : 'text-gray-900',
                  'relative cursor-default select-none py-2 pl-10 pr-4'
                ]">
                  <span :class="[selected ? 'font-medium' : 'font-normal', 'block truncate']">
                    {{ category.name }}
                  </span>
                  <span v-if="selected" class="absolute inset-y-0 left-0 flex items-center pl-3 text-primary-600">
                    <CheckIcon class="h-5 w-5" />
                  </span>
                </li>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </div>
      </Listbox>
    </div>

    <!-- 应用列表 -->
    <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="i in 6" :key="i" class="animate-pulse">
        <div class="bg-gray-200 rounded-lg h-48"></div>
      </div>
    </div>

    <div v-else-if="filteredApps.length === 0" class="text-center py-12">
      <div class="mx-auto h-12 w-12 text-gray-400">
        <DocumentIcon class="h-12 w-12" />
      </div>
      <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('apps.empty.title') }}</h3>
      <p class="mt-1 text-sm text-gray-500">{{ t('apps.empty.description') }}</p>
      <div class="mt-6">
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          {{ t('apps.create') }}
        </button>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <AppCard
        v-for="app in filteredApps"
        :key="app.id"
        :app="app"
        @edit="handleEditApp"
        @delete="handleDeleteApp"
        @duplicate="handleDuplicateApp"
      />
    </div>

    <!-- 创建应用模态框 -->
    <CreateAppModal
      :show="showCreateModal"
      @close="showCreateModal = false"
      @created="handleAppCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ChevronUpDownIcon,
  CheckIcon,
  DocumentIcon,
} from '@heroicons/vue/24/outline'

import AppCard from '@/components/apps/AppCard.vue'
import CreateAppModal from '@/components/apps/CreateAppModal.vue'
import { useAppsStore } from '@/stores/apps'
import { api } from '@dify/api'
import type { App, AppCategory } from '@dify/types'

// 国际化
const { t } = useI18n()

// 状态管理
const appsStore = useAppsStore()
const queryClient = useQueryClient()

// 响应式状态
const searchQuery = ref('')
const showCreateModal = ref(false)
const selectedCategory = ref<AppCategory>({ id: 'all', name: t('apps.categories.all') })

// 分类选项
const categories = ref<AppCategory[]>([
  { id: 'all', name: t('apps.categories.all') },
  { id: 'chatbot', name: t('apps.categories.chatbot') },
  { id: 'agent', name: t('apps.categories.agent') },
  { id: 'workflow', name: t('apps.categories.workflow') },
])

// 数据查询
const {
  data: apps,
  isLoading,
  error,
  refetch
} = useQuery({
  queryKey: ['apps'],
  queryFn: () => api.apps.list(),
  staleTime: 5 * 60 * 1000, // 5分钟
})

// 计算属性 - 过滤后的应用列表
const filteredApps = computed(() => {
  if (!apps.value) return []
  
  let filtered = apps.value
  
  // 按分类筛选
  if (selectedCategory.value.id !== 'all') {
    filtered = filtered.filter(app => app.category === selectedCategory.value.id)
  }
  
  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(app => 
      app.name.toLowerCase().includes(query) ||
      app.description?.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

// 删除应用
const deleteAppMutation = useMutation({
  mutationFn: (appId: string) => api.apps.delete(appId),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['apps'] })
  }
})

// 事件处理
const handleEditApp = (app: App) => {
  // 跳转到应用编辑页面
  // router.push(`/apps/${app.id}/edit`)
  console.log('Edit app:', app)
}

const handleDeleteApp = async (app: App) => {
  if (confirm(t('apps.delete.confirm', { name: app.name }))) {
    try {
      await deleteAppMutation.mutateAsync(app.id)
    } catch (error) {
      console.error('Delete app failed:', error)
    }
  }
}

const handleDuplicateApp = (app: App) => {
  // 复制应用逻辑
  console.log('Duplicate app:', app)
}

const handleAppCreated = (app: App) => {
  showCreateModal.value = false
  queryClient.invalidateQueries({ queryKey: ['apps'] })
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.apps-page {
  @apply p-6 max-w-7xl mx-auto;
}

/* 自定义动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
