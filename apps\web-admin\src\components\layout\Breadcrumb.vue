<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li>
        <div>
          <RouterLink to="/" class="text-gray-400 hover:text-gray-500">
            <HomeIcon class="flex-shrink-0 h-5 w-5" />
            <span class="sr-only">Home</span>
          </RouterLink>
        </div>
      </li>
      
      <li v-for="(item, index) in items" :key="index">
        <div class="flex items-center">
          <ChevronRightIcon class="flex-shrink-0 h-5 w-5 text-gray-400" />
          
          <RouterLink
            v-if="item.path && index < items.length - 1"
            :to="item.path"
            class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            {{ item.name }}
          </RouterLink>
          
          <span
            v-else
            class="ml-4 text-sm font-medium text-gray-900"
            aria-current="page"
          >
            {{ item.name }}
          </span>
        </div>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">
import { HomeIcon, ChevronRightIcon } from '@heroicons/vue/24/outline'

interface BreadcrumbItem {
  name: string
  path?: string
}

interface Props {
  items: BreadcrumbItem[]
}

defineProps<Props>()
</script>
