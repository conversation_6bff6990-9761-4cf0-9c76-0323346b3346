{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@tanstack+virtual-core@3.13.12/node_modules/@tanstack/virtual-core/src/utils.ts", "../../../../../node_modules/.pnpm/@tanstack+virtual-core@3.13.12/node_modules/@tanstack/virtual-core/src/index.ts", "../../../../../node_modules/.pnpm/@tanstack+vue-virtual@3.13.12_vue@3.5.17_typescript@5.8.3_/node_modules/@tanstack/vue-virtual/src/index.ts", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-controllable.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/micro-task.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/disposables.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-disposables.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-frame-debounce.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-id.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/dom.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/match.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/env.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/owner.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/focus-management.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/platform.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-document-event.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-window-event.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-outside-click.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-resolve-button-type.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-tracked-pointer.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-tree-walker.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/render.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/internal/hidden.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/internal/open-closed.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/keyboard.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/mouse.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/document-ready.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/active-element-history.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/calculate-active-index.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/form.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/combobox/combobox.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-event-listener.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-tab-direction.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/focus-trap/focus-trap.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-store.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/store.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/document-overflow/adjust-scrollbar-padding.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/document-overflow/handle-ios-locking.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/document-overflow/prevent-scroll.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/document-overflow/overflow-store.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/document-overflow/use-document-overflow.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-inert.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-root-containers.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/internal/portal-force-root.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/internal/stack-context.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/description/description.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/portal/portal.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/dialog/dialog.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/disclosure/disclosure.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/get-text-value.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/hooks/use-text-value.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/listbox/listbox.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/menu/menu.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/popover/popover.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/label/label.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/radio-group/radio-group.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/switch/switch.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/internal/focus-sentinel.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/tabs/tabs.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/utils/once.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/transitions/utils/transition.js", "../../../../../node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.17_typescript@5.8.3_/node_modules/@headlessui/vue/dist/components/transitions/transition.js"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) < 1.01\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n", "import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nconst getRect = (element: HTMLElement): Rect => {\n  const { offsetWidth, offsetHeight } = element\n  return { width: offsetWidth, height: offsetHeight }\n}\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(getRect(element as unknown as HTMLElement))\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(getRect(element as unknown as HTMLElement))\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n\n  return (element as unknown as HTMLElement)[\n    instance.options.horizontal ? 'offsetWidth' : 'offsetHeight'\n  ]\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const maxOffset = this.getTotalSize() + this.options.scrollMargin - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    let attempts = 0\n    const maxAttempts = 10\n\n    const tryScroll = (currentAlign: ScrollAlignment) => {\n      if (!this.targetWindow) return\n\n      const offsetInfo = this.getOffsetForIndex(index, currentAlign)\n      if (!offsetInfo) {\n        console.warn('Failed to get offset for index:', index)\n        return\n      }\n      const [offset, align] = offsetInfo\n      this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n      this.targetWindow.requestAnimationFrame(() => {\n        const currentOffset = this.getScrollOffset()\n        const afterInfo = this.getOffsetForIndex(index, align)\n        if (!afterInfo) {\n          console.warn('Failed to get offset for index:', index)\n          return\n        }\n\n        if (!approxEqual(afterInfo[0], currentOffset)) {\n          scheduleRetry(align)\n        }\n      })\n    }\n\n    const scheduleRetry = (align: ScrollAlignment) => {\n      if (!this.targetWindow) return\n\n      attempts++\n      if (attempts < maxAttempts) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('Schedule retry', attempts, maxAttempts)\n        }\n        this.targetWindow.requestAnimationFrame(() => tryScroll(align))\n      } else {\n        console.warn(\n          `Failed to scroll to index ${index} after ${maxAttempts} attempts.`,\n        )\n      }\n    }\n\n    tryScroll(initialAlign)\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex >= 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  // handle case when item count is less than or equal to lanes\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex,\n    }\n  }\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex >= 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n", "import {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport {\n  computed,\n  onScopeDispose,\n  shallowRef,\n  triggerRef,\n  unref,\n  watch,\n} from 'vue'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\nimport type { Ref } from 'vue'\n\nexport * from '@tanstack/virtual-core'\n\ntype MaybeRef<T> = T | Ref<T>\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<VirtualizerOptions<TScrollElement, TItemElement>>,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  const virtualizer = new Virtualizer(unref(options))\n  const state = shallowRef(virtualizer)\n\n  const cleanup = virtualizer._didMount()\n\n  watch(\n    () => unref(options).getScrollElement(),\n    (el) => {\n      if (el) {\n        virtualizer._willUpdate()\n      }\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  watch(\n    () => unref(options),\n    (options) => {\n      virtualizer.setOptions({\n        ...options,\n        onChange: (instance, sync) => {\n          triggerRef(state)\n          options.onChange?.(instance, sync)\n        },\n      })\n\n      virtualizer._willUpdate()\n      triggerRef(state)\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  onScopeDispose(cleanup)\n\n  return state\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<TScrollElement, TItemElement>,\n      'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n    >\n  >,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  return useVirtualizerBase<TScrollElement, TItemElement>(\n    computed(() => ({\n      observeElementRect: observeElementRect,\n      observeElementOffset: observeElementOffset,\n      scrollToFn: elementScroll,\n      ...unref(options),\n    })),\n  )\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<Window, TItemElement>,\n      | 'observeElementRect'\n      | 'observeElementOffset'\n      | 'scrollToFn'\n      | 'getScrollElement'\n    >\n  >,\n): Ref<Virtualizer<Window, TItemElement>> {\n  return useVirtualizerBase<Window, TItemElement>(\n    computed(() => ({\n      getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n      observeElementRect: observeWindowRect,\n      observeElementOffset: observeWindowOffset,\n      scrollToFn: windowScroll,\n      initialOffset: () =>\n        typeof document !== 'undefined' ? window.scrollY : 0,\n      ...unref(options),\n    })),\n  )\n}\n", "import{computed as p,ref as s}from\"vue\";function d(u,e,r){let i=s(r==null?void 0:r.value),f=p(()=>u.value!==void 0);return[p(()=>f.value?u.value:i.value),function(t){return f.value||(i.value=t),e==null?void 0:e(t)}]}export{d as useControllable};\n", "function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n", "import{microTask as n}from'./micro-task.js';function o(){let a=[],s={addEventListener(e,t,r,i){return e.addEventListener(t,r,i),s.add(()=>e.removeEventListener(t,r,i))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);s.add(()=>cancelAnimationFrame(t))},nextFrame(...e){s.requestAnimationFrame(()=>{s.requestAnimationFrame(...e)})},setTimeout(...e){let t=setTimeout(...e);s.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return n(()=>{t.current&&e[0]()}),s.add(()=>{t.current=!1})},style(e,t,r){let i=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:i})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return a.push(e),()=>{let t=a.indexOf(e);if(t>=0)for(let r of a.splice(t,1))r()}},dispose(){for(let e of a.splice(0))e()}};return s}export{o as disposables};\n", "import{onUnmounted as s}from\"vue\";import{disposables as e}from'../utils/disposables.js';function i(){let o=e();return s(()=>o.dispose()),o}export{i as useDisposables};\n", "import{useDisposables as r}from'./use-disposables.js';function t(){let e=r();return o=>{e.dispose(),e.nextFrame(o)}}export{t as useFrameDebounce};\n", "var r;import*as e from\"vue\";let n=Symbol(\"headlessui.useid\"),o=0;const i=(r=e.useId)!=null?r:function(){return e.inject(n,()=>`${++o}`)()};function s(t){e.provide(n,t)}export{s as provideUseId,i as useId};\n", "function o(e){var l;if(e==null||e.value==null)return null;let n=(l=e.value.$el)!=null?l:e.value;return n instanceof Node?n:null}export{o as dom};\n", "function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n", "var i=Object.defineProperty;var d=(t,e,r)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var n=(t,e,r)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,r),r);class s{constructor(){n(this,\"current\",this.detect());n(this,\"currentId\",0)}set(e){this.current!==e&&(this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}}let c=new s;export{c as env};\n", "import{dom as o}from'./dom.js';import{env as t}from'./env.js';function i(r){if(t.isServer)return null;if(r instanceof Node)return r.ownerDocument;if(r!=null&&r.hasOwnProperty(\"value\")){let n=o(r);if(n)return n.ownerDocument}return document}export{i as getOwnerDocument};\n", "import{nextTick as b}from\"vue\";import{match as M}from'./match.js';import{getOwnerDocument as f}from'./owner.js';let c=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var N=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n))(N||{}),T=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(T||{}),F=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(F||{});function E(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(c)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(h||{});function w(e,r=0){var t;return e===((t=f(e))==null?void 0:t.body)?!1:M(r,{[0](){return e.matches(c)},[1](){let l=e;for(;l!==null;){if(l.matches(c))return!0;l=l.parentElement}return!1}})}function _(e){let r=f(e);b(()=>{r&&!w(r.activeElement,0)&&S(e)})}var y=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(y||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function S(e){e==null||e.focus({preventScroll:!0})}let H=[\"textarea\",\"input\"].join(\",\");function I(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,H))!=null?t:!1}function O(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),i=r(l);if(o===null||i===null)return 0;let n=o.compareDocumentPosition(i);return n&Node.DOCUMENT_POSITION_FOLLOWING?-1:n&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function v(e,r){return P(E(),r,{relativeTo:e})}function P(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){var m;let i=(m=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?m:document,n=Array.isArray(e)?t?O(e):e:E(e);o.length>0&&n.length>1&&(n=n.filter(s=>!o.includes(s))),l=l!=null?l:i.activeElement;let x=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),p=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,n.indexOf(l))-1;if(r&4)return Math.max(0,n.indexOf(l))+1;if(r&8)return n.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),L=r&32?{preventScroll:!0}:{},a=0,d=n.length,u;do{if(a>=d||a+d<=0)return 0;let s=p+a;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}u=n[s],u==null||u.focus(L),a+=x}while(u!==i.activeElement);return r&6&&I(u)&&u.select(),2}export{N as Focus,T as FocusResult,h as FocusableMode,S as focusElement,v as focusFrom,P as focusIn,E as getFocusableElements,w as isFocusableElement,_ as restoreFocusIfNecessary,O as sortByDomNode};\n", "function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n", "import{watchEffect as r}from\"vue\";import{env as m}from'../utils/env.js';function u(e,t,n){m.isServer||r(o=>{document.addEventListener(e,t,n),o(()=>document.removeEventListener(e,t,n))})}export{u as useDocumentEvent};\n", "import{watchEffect as i}from\"vue\";import{env as r}from'../utils/env.js';function w(e,n,t){r.isServer||i(o=>{window.addEventListener(e,n,t),o(()=>window.removeEventListener(e,n,t))})}export{w as useWindowEvent};\n", "import{computed as s,ref as E}from\"vue\";import{dom as p}from'../utils/dom.js';import{FocusableMode as d,isFocusableElement as C}from'../utils/focus-management.js';import{isMobile as T}from'../utils/platform.js';import{useDocumentEvent as i}from'./use-document-event.js';import{useWindowEvent as M}from'./use-window-event.js';function w(f,m,l=s(()=>!0)){function a(e,r){if(!l.value||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t))return;let c=function o(n){return typeof n==\"function\"?o(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let o of c){if(o===null)continue;let n=o instanceof HTMLElement?o:p(o);if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!C(t,d.Loose)&&t.tabIndex!==-1&&e.preventDefault(),m(e,t)}let u=E(null);i(\"pointerdown\",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i(\"mousedown\",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i(\"click\",e=>{T()||u.value&&(a(e,()=>u.value),u.value=null)},!0),i(\"touchend\",e=>a(e,()=>e.target instanceof HTMLElement?e.target:null),!0),M(\"blur\",e=>a(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{w as useOutsideClick};\n", "import{onMounted as i,ref as f,watchEffect as l}from\"vue\";import{dom as o}from'../utils/dom.js';function r(t,e){if(t)return t;let n=e!=null?e:\"button\";if(typeof n==\"string\"&&n.toLowerCase()===\"button\")return\"button\"}function s(t,e){let n=f(r(t.value.type,t.value.as));return i(()=>{n.value=r(t.value.type,t.value.as)}),l(()=>{var u;n.value||o(e)&&o(e)instanceof HTMLButtonElement&&!((u=o(e))!=null&&u.hasAttribute(\"type\"))&&(n.value=\"button\")}),n}export{s as useResolveButtonType};\n", "import{ref as o}from\"vue\";function r(e){return[e.screenX,e.screenY]}function u(){let e=o([-1,-1]);return{wasMoved(n){let t=r(n);return e.value[0]===t[0]&&e.value[1]===t[1]?!1:(e.value=t,!0)},update(n){e.value=r(n)}}}export{u as useTrackedPointer};\n", "import{watchEffect as p}from\"vue\";import{getOwnerDocument as u}from'../utils/owner.js';function i({container:e,accept:t,walk:d,enabled:o}){p(()=>{let r=e.value;if(!r||o!==void 0&&!o.value)return;let l=u(e);if(!l)return;let c=Object.assign(f=>t(f),{acceptNode:t}),n=l.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,c,!1);for(;n.nextNode();)d(n.currentNode)})}export{i as useTreeWalker};\n", "import{cloneVNode as O,Fragment as x,h as k}from\"vue\";import{match as w}from'./match.js';var N=(o=>(o[o.None=0]=\"None\",o[o.RenderStrategy=1]=\"RenderStrategy\",o[o.Static=2]=\"Static\",o))(N||{}),S=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(S||{});function A({visible:r=!0,features:t=0,ourProps:e,theirProps:o,...i}){var a;let n=j(o,e),l=Object.assign(i,{props:n});if(r||t&2&&n.static)return y(l);if(t&1){let d=(a=n.unmount)==null||a?0:1;return w(d,{[0](){return null},[1](){return y({...i,props:{...n,hidden:!0,style:{display:\"none\"}}})}})}return y(l)}function y({props:r,attrs:t,slots:e,slot:o,name:i}){var m,h;let{as:n,...l}=T(r,[\"unmount\",\"static\"]),a=(m=e.default)==null?void 0:m.call(e,o),d={};if(o){let u=!1,c=[];for(let[p,f]of Object.entries(o))typeof f==\"boolean\"&&(u=!0),f===!0&&c.push(p);u&&(d[\"data-headlessui-state\"]=c.join(\" \"))}if(n===\"template\"){if(a=b(a!=null?a:[]),Object.keys(l).length>0||Object.keys(t).length>0){let[u,...c]=a!=null?a:[];if(!v(u)||c.length>0)throw new Error(['Passing props on \"template\"!',\"\",`The current component <${i} /> is rendering a \"template\".`,\"However we need to passthrough the following props:\",Object.keys(l).concat(Object.keys(t)).map(s=>s.trim()).filter((s,g,R)=>R.indexOf(s)===g).sort((s,g)=>s.localeCompare(g)).map(s=>`  - ${s}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"template\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(s=>`  - ${s}`).join(`\n`)].join(`\n`));let p=j((h=u.props)!=null?h:{},l,d),f=O(u,p,!0);for(let s in p)s.startsWith(\"on\")&&(f.props||(f.props={}),f.props[s]=p[s]);return f}return Array.isArray(a)&&a.length===1?a[0]:a}return k(n,Object.assign({},l,d),{default:()=>a})}function b(r){return r.flatMap(t=>t.type===x?b(t.children):[t])}function j(...r){var o;if(r.length===0)return{};if(r.length===1)return r[0];let t={},e={};for(let i of r)for(let n in i)n.startsWith(\"on\")&&typeof i[n]==\"function\"?((o=e[n])!=null||(e[n]=[]),e[n].push(i[n])):t[n]=i[n];if(t.disabled||t[\"aria-disabled\"])return Object.assign(t,Object.fromEntries(Object.keys(e).map(i=>[i,void 0])));for(let i in e)Object.assign(t,{[i](n,...l){let a=e[i];for(let d of a){if(n instanceof Event&&n.defaultPrevented)return;d(n,...l)}}});return t}function E(r){let t=Object.assign({},r);for(let e in t)t[e]===void 0&&delete t[e];return t}function T(r,t=[]){let e=Object.assign({},r);for(let o of t)o in e&&delete e[o];return e}function v(r){return r==null?!1:typeof r.type==\"string\"||typeof r.type==\"object\"||typeof r.type==\"function\"}export{N as Features,S as RenderStrategy,E as compact,T as omit,A as render};\n", "import{defineComponent as a}from\"vue\";import{render as p}from'../utils/render.js';var u=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(u||{});let f=a({name:\"Hidden\",props:{as:{type:[Object,String],default:\"div\"},features:{type:Number,default:1}},setup(t,{slots:n,attrs:i}){return()=>{var r;let{features:e,...d}=t,o={\"aria-hidden\":(e&2)===2?!0:(r=d[\"aria-hidden\"])!=null?r:void 0,hidden:(e&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(e&4)===4&&(e&2)!==2&&{display:\"none\"}}};return p({ourProps:o,theirProps:d,slot:{},attrs:i,slots:n,name:\"Hidden\"})}}});export{u as Features,f as Hidden};\n", "import{inject as p,provide as r}from\"vue\";let n=Symbol(\"Context\");var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function s(){return l()!==null}function l(){return p(n,null)}function t(o){r(n,o)}export{i as State,s as hasOpenClosed,l as useOpenClosed,t as useOpenClosedProvider};\n", "var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n", "var g=(f=>(f[f.Left=0]=\"Left\",f[f.Right=2]=\"Right\",f))(g||{});export{g as MouseButton};\n", "function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n", "import{onDocumentReady as d}from'./document-ready.js';let t=[];d(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&t[0]!==n.target&&(t.unshift(n.target),t=t.filter(r=>r!=null&&r.isConnected),t.splice(10))}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{t as history};\n", "function u(l){throw new Error(\"Unexpected object: \"+l)}var c=(i=>(i[i.First=0]=\"First\",i[i.Previous=1]=\"Previous\",i[i.Next=2]=\"Next\",i[i.Last=3]=\"Last\",i[i.Specific=4]=\"Specific\",i[i.Nothing=5]=\"Nothing\",i))(c||{});function f(l,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),s=r!=null?r:-1;switch(l.focus){case 0:{for(let e=0;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 1:{s===-1&&(s=t.length);for(let e=s-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 2:{for(let e=s+1;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 3:{for(let e=t.length-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 4:{for(let e=0;e<t.length;++e)if(n.resolveId(t[e],e,t)===l.id)return e;return r}case 5:return null;default:u(l)}}export{c as Focus,f as calculateActiveIndex};\n", "function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n", "import{useVirtualizer as re}from\"@tanstack/vue-virtual\";import{cloneVNode as de,computed as m,defineComponent as H,Fragment as se,h as z,inject as ee,nextTick as N,onMounted as X,onUnmounted as fe,provide as te,reactive as ve,ref as k,toRaw as L,watch as J,watchEffect as Y}from\"vue\";import{useControllable as pe}from'../../hooks/use-controllable.js';import{useFrameDebounce as be}from'../../hooks/use-frame-debounce.js';import{useId as W}from'../../hooks/use-id.js';import{useOutsideClick as ce}from'../../hooks/use-outside-click.js';import{useResolveButtonType as me}from'../../hooks/use-resolve-button-type.js';import{useTrackedPointer as xe}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as ge}from'../../hooks/use-tree-walker.js';import{Features as Se,Hidden as Oe}from'../../internal/hidden.js';import{State as G,useOpenClosed as Ce,useOpenClosedProvider as Re}from'../../internal/open-closed.js';import{Keys as F}from'../../keyboard.js';import{MouseButton as ye}from'../../mouse.js';import{history as oe}from'../../utils/active-element-history.js';import{calculateActiveIndex as le,Focus as P}from'../../utils/calculate-active-index.js';import{disposables as ae}from'../../utils/disposables.js';import{dom as x}from'../../utils/dom.js';import{sortByDomNode as Te}from'../../utils/focus-management.js';import{objectToFormEntries as Ie}from'../../utils/form.js';import{match as _}from'../../utils/match.js';import{getOwnerDocument as he}from'../../utils/owner.js';import{isMobile as we}from'../../utils/platform.js';import{compact as Pe,Features as Q,omit as Z,render as U}from'../../utils/render.js';function De(a,h){return a===h}var Ee=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(Ee||{}),Ve=(r=>(r[r.Single=0]=\"Single\",r[r.Multi=1]=\"Multi\",r))(Ve||{}),ke=(y=>(y[y.Pointer=0]=\"Pointer\",y[y.Focus=1]=\"Focus\",y[y.Other=2]=\"Other\",y))(ke||{});let ne=Symbol(\"ComboboxContext\");function K(a){let h=ee(ne,null);if(h===null){let r=new Error(`<${a} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,K),r}return h}let ie=Symbol(\"VirtualContext\"),Ae=H({name:\"VirtualProvider\",setup(a,{slots:h}){let r=K(\"VirtualProvider\"),y=m(()=>{let c=x(r.optionsRef);if(!c)return{start:0,end:0};let f=window.getComputedStyle(c);return{start:parseFloat(f.paddingBlockStart||f.paddingTop),end:parseFloat(f.paddingBlockEnd||f.paddingBottom)}}),o=re(m(()=>({scrollPaddingStart:y.value.start,scrollPaddingEnd:y.value.end,count:r.virtual.value.options.length,estimateSize(){return 40},getScrollElement(){return x(r.optionsRef)},overscan:12}))),u=m(()=>{var c;return(c=r.virtual.value)==null?void 0:c.options}),e=k(0);return J([u],()=>{e.value+=1}),te(ie,r.virtual.value?o:null),()=>[z(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${o.value.getTotalSize()}px`},ref:c=>{if(c){if(typeof process!=\"undefined\"&&process.env.JEST_WORKER_ID!==void 0||r.activationTrigger.value===0)return;r.activeOptionIndex.value!==null&&r.virtual.value.options.length>r.activeOptionIndex.value&&o.value.scrollToIndex(r.activeOptionIndex.value)}}},o.value.getVirtualItems().map(c=>de(h.default({option:r.virtual.value.options[c.index],open:r.comboboxState.value===0})[0],{key:`${e.value}-${c.index}`,\"data-index\":c.index,\"aria-setsize\":r.virtual.value.options.length,\"aria-posinset\":c.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${c.start}px)`,overflowAnchor:\"none\"}})))]}}),lt=H({name:\"Combobox\",emits:{\"update:modelValue\":a=>!0},props:{as:{type:[Object,String],default:\"template\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(a,{slots:h,attrs:r,emit:y}){let o=k(1),u=k(null),e=k(null),c=k(null),f=k(null),S=k({static:!1,hold:!1}),v=k([]),d=k(null),D=k(2),E=k(!1);function w(t=n=>n){let n=d.value!==null?v.value[d.value]:null,s=t(v.value.slice()),b=s.length>0&&s[0].dataRef.order.value!==null?s.sort((C,A)=>C.dataRef.order.value-A.dataRef.order.value):Te(s,C=>x(C.dataRef.domRef)),O=n?b.indexOf(n):null;return O===-1&&(O=null),{options:b,activeOptionIndex:O}}let M=m(()=>a.multiple?1:0),$=m(()=>a.nullable),[B,p]=pe(m(()=>a.modelValue),t=>y(\"update:modelValue\",t),m(()=>a.defaultValue)),R=m(()=>B.value===void 0?_(M.value,{[1]:[],[0]:void 0}):B.value),V=null,i=null;function I(t){return _(M.value,{[0](){return p==null?void 0:p(t)},[1]:()=>{let n=L(l.value.value).slice(),s=L(t),b=n.findIndex(O=>l.compare(s,L(O)));return b===-1?n.push(s):n.splice(b,1),p==null?void 0:p(n)}})}let T=m(()=>{});J([T],([t],[n])=>{if(l.virtual.value&&t&&n&&d.value!==null){let s=t.indexOf(n[d.value]);s!==-1?d.value=s:d.value=null}});let l={comboboxState:o,value:R,mode:M,compare(t,n){if(typeof a.by==\"string\"){let s=a.by;return(t==null?void 0:t[s])===(n==null?void 0:n[s])}return a.by===null?De(t,n):a.by(t,n)},calculateIndex(t){return l.virtual.value?a.by===null?l.virtual.value.options.indexOf(t):l.virtual.value.options.findIndex(n=>l.compare(n,t)):v.value.findIndex(n=>l.compare(n.dataRef.value,t))},defaultValue:m(()=>a.defaultValue),nullable:$,immediate:m(()=>!1),virtual:m(()=>null),inputRef:e,labelRef:u,buttonRef:c,optionsRef:f,disabled:m(()=>a.disabled),options:v,change(t){p(t)},activeOptionIndex:m(()=>{if(E.value&&d.value===null&&(l.virtual.value?l.virtual.value.options.length>0:v.value.length>0)){if(l.virtual.value){let n=l.virtual.value.options.findIndex(s=>{var b;return!((b=l.virtual.value)!=null&&b.disabled(s))});if(n!==-1)return n}let t=v.value.findIndex(n=>!n.dataRef.disabled);if(t!==-1)return t}return d.value}),activationTrigger:D,optionsPropsRef:S,closeCombobox(){E.value=!1,!a.disabled&&o.value!==1&&(o.value=1,d.value=null)},openCombobox(){if(E.value=!0,!a.disabled&&o.value!==0){if(l.value.value){let t=l.calculateIndex(l.value.value);t!==-1&&(d.value=t)}o.value=0}},setActivationTrigger(t){D.value=t},goToOption(t,n,s){E.value=!1,V!==null&&cancelAnimationFrame(V),V=requestAnimationFrame(()=>{if(a.disabled||f.value&&!S.value.static&&o.value===1)return;if(l.virtual.value){d.value=t===P.Specific?n:le({focus:t},{resolveItems:()=>l.virtual.value.options,resolveActiveIndex:()=>{var C,A;return(A=(C=l.activeOptionIndex.value)!=null?C:l.virtual.value.options.findIndex(j=>{var q;return!((q=l.virtual.value)!=null&&q.disabled(j))}))!=null?A:null},resolveDisabled:C=>l.virtual.value.disabled(C),resolveId(){throw new Error(\"Function not implemented.\")}}),D.value=s!=null?s:2;return}let b=w();if(b.activeOptionIndex===null){let C=b.options.findIndex(A=>!A.dataRef.disabled);C!==-1&&(b.activeOptionIndex=C)}let O=t===P.Specific?n:le({focus:t},{resolveItems:()=>b.options,resolveActiveIndex:()=>b.activeOptionIndex,resolveId:C=>C.id,resolveDisabled:C=>C.dataRef.disabled});d.value=O,D.value=s!=null?s:2,v.value=b.options})},selectOption(t){let n=v.value.find(b=>b.id===t);if(!n)return;let{dataRef:s}=n;I(s.value)},selectActiveOption(){if(l.activeOptionIndex.value!==null){if(l.virtual.value)I(l.virtual.value.options[l.activeOptionIndex.value]);else{let{dataRef:t}=v.value[l.activeOptionIndex.value];I(t.value)}l.goToOption(P.Specific,l.activeOptionIndex.value)}},registerOption(t,n){let s=ve({id:t,dataRef:n});if(l.virtual.value){v.value.push(s);return}i&&cancelAnimationFrame(i);let b=w(O=>(O.push(s),O));d.value===null&&l.isSelected(n.value.value)&&(b.activeOptionIndex=b.options.indexOf(s)),v.value=b.options,d.value=b.activeOptionIndex,D.value=2,b.options.some(O=>!x(O.dataRef.domRef))&&(i=requestAnimationFrame(()=>{let O=w();v.value=O.options,d.value=O.activeOptionIndex}))},unregisterOption(t,n){if(V!==null&&cancelAnimationFrame(V),n&&(E.value=!0),l.virtual.value){v.value=v.value.filter(b=>b.id!==t);return}let s=w(b=>{let O=b.findIndex(C=>C.id===t);return O!==-1&&b.splice(O,1),b});v.value=s.options,d.value=s.activeOptionIndex,D.value=2},isSelected(t){return _(M.value,{[0]:()=>l.compare(L(l.value.value),L(t)),[1]:()=>L(l.value.value).some(n=>l.compare(L(n),L(t)))})},isActive(t){return d.value===l.calculateIndex(t)}};ce([e,c,f],()=>l.closeCombobox(),m(()=>o.value===0)),te(ne,l),Re(m(()=>_(o.value,{[0]:G.Open,[1]:G.Closed})));let g=m(()=>{var t;return(t=x(e))==null?void 0:t.closest(\"form\")});return X(()=>{J([g],()=>{if(!g.value||a.defaultValue===void 0)return;function t(){l.change(a.defaultValue)}return g.value.addEventListener(\"reset\",t),()=>{var n;(n=g.value)==null||n.removeEventListener(\"reset\",t)}},{immediate:!0})}),()=>{var C,A,j;let{name:t,disabled:n,form:s,...b}=a,O={open:o.value===0,disabled:n,activeIndex:l.activeOptionIndex.value,activeOption:l.activeOptionIndex.value===null?null:l.virtual.value?l.virtual.value.options[(C=l.activeOptionIndex.value)!=null?C:0]:(j=(A=l.options.value[l.activeOptionIndex.value])==null?void 0:A.dataRef.value)!=null?j:null,value:R.value};return z(se,[...t!=null&&R.value!=null?Ie({[t]:R.value}).map(([q,ue])=>z(Oe,Pe({features:Se.Hidden,key:q,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:s,disabled:n,name:q,value:ue}))):[],U({theirProps:{...r,...Z(b,[\"by\",\"defaultValue\",\"immediate\",\"modelValue\",\"multiple\",\"nullable\",\"onUpdate:modelValue\",\"virtual\"])},ourProps:{},slot:O,slots:h,attrs:r,name:\"Combobox\"})])}}}),at=H({name:\"ComboboxLabel\",props:{as:{type:[Object,String],default:\"label\"},id:{type:String,default:null}},setup(a,{attrs:h,slots:r}){var e;let y=(e=a.id)!=null?e:`headlessui-combobox-label-${W()}`,o=K(\"ComboboxLabel\");function u(){var c;(c=x(o.inputRef))==null||c.focus({preventScroll:!0})}return()=>{let c={open:o.comboboxState.value===0,disabled:o.disabled.value},{...f}=a,S={id:y,ref:o.labelRef,onClick:u};return U({ourProps:S,theirProps:f,slot:c,attrs:h,slots:r,name:\"ComboboxLabel\"})}}}),nt=H({name:\"ComboboxButton\",props:{as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(a,{attrs:h,slots:r,expose:y}){var S;let o=(S=a.id)!=null?S:`headlessui-combobox-button-${W()}`,u=K(\"ComboboxButton\");y({el:u.buttonRef,$el:u.buttonRef});function e(v){u.disabled.value||(u.comboboxState.value===0?u.closeCombobox():(v.preventDefault(),u.openCombobox()),N(()=>{var d;return(d=x(u.inputRef))==null?void 0:d.focus({preventScroll:!0})}))}function c(v){switch(v.key){case F.ArrowDown:v.preventDefault(),v.stopPropagation(),u.comboboxState.value===1&&u.openCombobox(),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return;case F.ArrowUp:v.preventDefault(),v.stopPropagation(),u.comboboxState.value===1&&(u.openCombobox(),N(()=>{u.value.value||u.goToOption(P.Last)})),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return;case F.Escape:if(u.comboboxState.value!==0)return;v.preventDefault(),u.optionsRef.value&&!u.optionsPropsRef.value.static&&v.stopPropagation(),u.closeCombobox(),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return}}let f=me(m(()=>({as:a.as,type:h.type})),u.buttonRef);return()=>{var E,w;let v={open:u.comboboxState.value===0,disabled:u.disabled.value,value:u.value.value},{...d}=a,D={ref:u.buttonRef,id:o,type:f.value,tabindex:\"-1\",\"aria-haspopup\":\"listbox\",\"aria-controls\":(E=x(u.optionsRef))==null?void 0:E.id,\"aria-expanded\":u.comboboxState.value===0,\"aria-labelledby\":u.labelRef.value?[(w=x(u.labelRef))==null?void 0:w.id,o].join(\" \"):void 0,disabled:u.disabled.value===!0?!0:void 0,onKeydown:c,onClick:e};return U({ourProps:D,theirProps:d,slot:v,attrs:h,slots:r,name:\"ComboboxButton\"})}}}),it=H({name:\"ComboboxInput\",props:{as:{type:[Object,String],default:\"input\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:a=>!0},setup(a,{emit:h,attrs:r,slots:y,expose:o}){var V;let u=(V=a.id)!=null?V:`headlessui-combobox-input-${W()}`,e=K(\"ComboboxInput\"),c=m(()=>he(x(e.inputRef))),f={value:!1};o({el:e.inputRef,$el:e.inputRef});function S(){e.change(null);let i=x(e.optionsRef);i&&(i.scrollTop=0),e.goToOption(P.Nothing)}let v=m(()=>{var I;let i=e.value.value;return x(e.inputRef)?typeof a.displayValue!=\"undefined\"&&i!==void 0?(I=a.displayValue(i))!=null?I:\"\":typeof i==\"string\"?i:\"\":\"\"});X(()=>{J([v,e.comboboxState,c],([i,I],[T,l])=>{if(f.value)return;let g=x(e.inputRef);g&&((l===0&&I===1||i!==T)&&(g.value=i),requestAnimationFrame(()=>{var s;if(f.value||!g||((s=c.value)==null?void 0:s.activeElement)!==g)return;let{selectionStart:t,selectionEnd:n}=g;Math.abs((n!=null?n:0)-(t!=null?t:0))===0&&t===0&&g.setSelectionRange(g.value.length,g.value.length)}))},{immediate:!0}),J([e.comboboxState],([i],[I])=>{if(i===0&&I===1){if(f.value)return;let T=x(e.inputRef);if(!T)return;let l=T.value,{selectionStart:g,selectionEnd:t,selectionDirection:n}=T;T.value=\"\",T.value=l,n!==null?T.setSelectionRange(g,t,n):T.setSelectionRange(g,t)}})});let d=k(!1);function D(){d.value=!0}function E(){ae().nextFrame(()=>{d.value=!1})}let w=be();function M(i){switch(f.value=!0,w(()=>{f.value=!1}),i.key){case F.Enter:if(f.value=!1,e.comboboxState.value!==0||d.value)return;if(i.preventDefault(),i.stopPropagation(),e.activeOptionIndex.value===null){e.closeCombobox();return}e.selectActiveOption(),e.mode.value===0&&e.closeCombobox();break;case F.ArrowDown:return f.value=!1,i.preventDefault(),i.stopPropagation(),_(e.comboboxState.value,{[0]:()=>e.goToOption(P.Next),[1]:()=>e.openCombobox()});case F.ArrowUp:return f.value=!1,i.preventDefault(),i.stopPropagation(),_(e.comboboxState.value,{[0]:()=>e.goToOption(P.Previous),[1]:()=>{e.openCombobox(),N(()=>{e.value.value||e.goToOption(P.Last)})}});case F.Home:if(i.shiftKey)break;return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.First);case F.PageUp:return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.First);case F.End:if(i.shiftKey)break;return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.Last);case F.PageDown:return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.Last);case F.Escape:if(f.value=!1,e.comboboxState.value!==0)return;i.preventDefault(),e.optionsRef.value&&!e.optionsPropsRef.value.static&&i.stopPropagation(),e.nullable.value&&e.mode.value===0&&e.value.value===null&&S(),e.closeCombobox();break;case F.Tab:if(f.value=!1,e.comboboxState.value!==0)return;e.mode.value===0&&e.activationTrigger.value!==1&&e.selectActiveOption(),e.closeCombobox();break}}function $(i){h(\"change\",i),e.nullable.value&&e.mode.value===0&&i.target.value===\"\"&&S(),e.openCombobox()}function B(i){var T,l,g;let I=(T=i.relatedTarget)!=null?T:oe.find(t=>t!==i.currentTarget);if(f.value=!1,!((l=x(e.optionsRef))!=null&&l.contains(I))&&!((g=x(e.buttonRef))!=null&&g.contains(I))&&e.comboboxState.value===0)return i.preventDefault(),e.mode.value===0&&(e.nullable.value&&e.value.value===null?S():e.activationTrigger.value!==1&&e.selectActiveOption()),e.closeCombobox()}function p(i){var T,l,g;let I=(T=i.relatedTarget)!=null?T:oe.find(t=>t!==i.currentTarget);(l=x(e.buttonRef))!=null&&l.contains(I)||(g=x(e.optionsRef))!=null&&g.contains(I)||e.disabled.value||e.immediate.value&&e.comboboxState.value!==0&&(e.openCombobox(),ae().nextFrame(()=>{e.setActivationTrigger(1)}))}let R=m(()=>{var i,I,T,l;return(l=(T=(I=a.defaultValue)!=null?I:e.defaultValue.value!==void 0?(i=a.displayValue)==null?void 0:i.call(a,e.defaultValue.value):null)!=null?T:e.defaultValue.value)!=null?l:\"\"});return()=>{var t,n,s,b,O,C,A;let i={open:e.comboboxState.value===0},{displayValue:I,onChange:T,...l}=a,g={\"aria-controls\":(t=e.optionsRef.value)==null?void 0:t.id,\"aria-expanded\":e.comboboxState.value===0,\"aria-activedescendant\":e.activeOptionIndex.value===null?void 0:e.virtual.value?(n=e.options.value.find(j=>!e.virtual.value.disabled(j.dataRef.value)&&e.compare(j.dataRef.value,e.virtual.value.options[e.activeOptionIndex.value])))==null?void 0:n.id:(s=e.options.value[e.activeOptionIndex.value])==null?void 0:s.id,\"aria-labelledby\":(C=(b=x(e.labelRef))==null?void 0:b.id)!=null?C:(O=x(e.buttonRef))==null?void 0:O.id,\"aria-autocomplete\":\"list\",id:u,onCompositionstart:D,onCompositionend:E,onKeydown:M,onInput:$,onFocus:p,onBlur:B,role:\"combobox\",type:(A=r.type)!=null?A:\"text\",tabIndex:0,ref:e.inputRef,defaultValue:R.value,disabled:e.disabled.value===!0?!0:void 0};return U({ourProps:g,theirProps:l,slot:i,attrs:r,slots:y,features:Q.RenderStrategy|Q.Static,name:\"ComboboxInput\"})}}}),ut=H({name:\"ComboboxOptions\",props:{as:{type:[Object,String],default:\"ul\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(a,{attrs:h,slots:r,expose:y}){let o=K(\"ComboboxOptions\"),u=`headlessui-combobox-options-${W()}`;y({el:o.optionsRef,$el:o.optionsRef}),Y(()=>{o.optionsPropsRef.value.static=a.static}),Y(()=>{o.optionsPropsRef.value.hold=a.hold});let e=Ce(),c=m(()=>e!==null?(e.value&G.Open)===G.Open:o.comboboxState.value===0);ge({container:m(()=>x(o.optionsRef)),enabled:m(()=>o.comboboxState.value===0),accept(S){return S.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:S.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(S){S.setAttribute(\"role\",\"none\")}});function f(S){S.preventDefault()}return()=>{var D,E,w;let S={open:o.comboboxState.value===0},v={\"aria-labelledby\":(w=(D=x(o.labelRef))==null?void 0:D.id)!=null?w:(E=x(o.buttonRef))==null?void 0:E.id,id:u,ref:o.optionsRef,role:\"listbox\",\"aria-multiselectable\":o.mode.value===1?!0:void 0,onMousedown:f},d=Z(a,[\"hold\"]);return U({ourProps:v,theirProps:d,slot:S,attrs:h,slots:o.virtual.value&&o.comboboxState.value===0?{...r,default:()=>[z(Ae,{},r.default)]}:r,features:Q.RenderStrategy|Q.Static,visible:c.value,name:\"ComboboxOptions\"})}}}),rt=H({name:\"ComboboxOption\",props:{as:{type:[Object,String],default:\"li\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(a,{slots:h,attrs:r,expose:y}){let o=K(\"ComboboxOption\"),u=`headlessui-combobox-option-${W()}`,e=k(null),c=m(()=>a.disabled);y({el:e,$el:e});let f=m(()=>{var p;return o.virtual.value?o.activeOptionIndex.value===o.calculateIndex(a.value):o.activeOptionIndex.value===null?!1:((p=o.options.value[o.activeOptionIndex.value])==null?void 0:p.id)===u}),S=m(()=>o.isSelected(a.value)),v=ee(ie,null),d=m(()=>({disabled:a.disabled,value:a.value,domRef:e,order:m(()=>a.order)}));X(()=>o.registerOption(u,d)),fe(()=>o.unregisterOption(u,f.value)),Y(()=>{let p=x(e);p&&(v==null||v.value.measureElement(p))}),Y(()=>{o.comboboxState.value===0&&f.value&&(o.virtual.value||o.activationTrigger.value!==0&&N(()=>{var p,R;return(R=(p=x(e))==null?void 0:p.scrollIntoView)==null?void 0:R.call(p,{block:\"nearest\"})}))});function D(p){p.preventDefault(),p.button===ye.Left&&(c.value||(o.selectOption(u),we()||requestAnimationFrame(()=>{var R;return(R=x(o.inputRef))==null?void 0:R.focus({preventScroll:!0})}),o.mode.value===0&&o.closeCombobox()))}function E(){var R;if(a.disabled||(R=o.virtual.value)!=null&&R.disabled(a.value))return o.goToOption(P.Nothing);let p=o.calculateIndex(a.value);o.goToOption(P.Specific,p)}let w=xe();function M(p){w.update(p)}function $(p){var V;if(!w.wasMoved(p)||a.disabled||(V=o.virtual.value)!=null&&V.disabled(a.value)||f.value)return;let R=o.calculateIndex(a.value);o.goToOption(P.Specific,R,0)}function B(p){var R;w.wasMoved(p)&&(a.disabled||(R=o.virtual.value)!=null&&R.disabled(a.value)||f.value&&(o.optionsPropsRef.value.hold||o.goToOption(P.Nothing)))}return()=>{let{disabled:p}=a,R={active:f.value,selected:S.value,disabled:p},V={id:u,ref:e,role:\"option\",tabIndex:p===!0?void 0:-1,\"aria-disabled\":p===!0?!0:void 0,\"aria-selected\":S.value,disabled:void 0,onMousedown:D,onFocus:E,onPointerenter:M,onMouseenter:M,onPointermove:$,onMousemove:$,onPointerleave:B,onMouseleave:B},i=Z(a,[\"order\",\"value\"]);return U({ourProps:V,theirProps:i,slot:R,attrs:r,slots:h,name:\"ComboboxOption\"})}}});export{lt as Combobox,nt as ComboboxButton,it as ComboboxInput,at as ComboboxLabel,rt as ComboboxOption,ut as ComboboxOptions};\n", "import{watchEffect as i}from\"vue\";import{env as a}from'../utils/env.js';function E(n,e,o,r){a.isServer||i(t=>{n=n!=null?n:window,n.addEventListener(e,o,r),t(()=>n.removeEventListener(e,o,r))})}export{E as useEventListener};\n", "import{ref as a}from\"vue\";import{useWindowEvent as t}from'./use-window-event.js';var d=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(d||{});function n(){let o=a(0);return t(\"keydown\",e=>{e.key===\"Tab\"&&(o.value=e.shiftKey?1:0)}),o}export{d as Direction,n as useTabDirection};\n", "import{computed as L,defineComponent as I,Fragment as j,h as R,onMounted as M,onUnmounted as h,ref as E,watch as g,watchEffect as K}from\"vue\";import{useEventListener as U}from'../../hooks/use-event-listener.js';import{Direction as y,useTabDirection as _}from'../../hooks/use-tab-direction.js';import{Features as k,Hidden as D}from'../../internal/hidden.js';import{history as C}from'../../utils/active-element-history.js';import{dom as c}from'../../utils/dom.js';import{Focus as v,focusElement as p,focusIn as b,FocusResult as q}from'../../utils/focus-management.js';import{match as P}from'../../utils/match.js';import{microTask as S}from'../../utils/micro-task.js';import{getOwnerDocument as x}from'../../utils/owner.js';import{render as G}from'../../utils/render.js';function B(t){if(!t)return new Set;if(typeof t==\"function\")return new Set(t());let n=new Set;for(let r of t.value){let l=c(r);l instanceof HTMLElement&&n.add(l)}return n}var A=(e=>(e[e.None=1]=\"None\",e[e.InitialFocus=2]=\"InitialFocus\",e[e.TabLock=4]=\"TabLock\",e[e.FocusLock=8]=\"FocusLock\",e[e.RestoreFocus=16]=\"RestoreFocus\",e[e.All=30]=\"All\",e))(A||{});let ue=Object.assign(I({name:\"FocusTrap\",props:{as:{type:[Object,String],default:\"div\"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:E(new Set)}},inheritAttrs:!1,setup(t,{attrs:n,slots:r,expose:l}){let o=E(null);l({el:o,$el:o});let i=L(()=>x(o)),e=E(!1);M(()=>e.value=!0),h(()=>e.value=!1),$({ownerDocument:i},L(()=>e.value&&Boolean(t.features&16)));let m=z({ownerDocument:i,container:o,initialFocus:L(()=>t.initialFocus)},L(()=>e.value&&Boolean(t.features&2)));J({ownerDocument:i,container:o,containers:t.containers,previousActiveElement:m},L(()=>e.value&&Boolean(t.features&8)));let f=_();function a(u){let T=c(o);if(!T)return;(w=>w())(()=>{P(f.value,{[y.Forwards]:()=>{b(T,v.First,{skipElements:[u.relatedTarget]})},[y.Backwards]:()=>{b(T,v.Last,{skipElements:[u.relatedTarget]})}})})}let s=E(!1);function F(u){u.key===\"Tab\"&&(s.value=!0,requestAnimationFrame(()=>{s.value=!1}))}function H(u){if(!e.value)return;let T=B(t.containers);c(o)instanceof HTMLElement&&T.add(c(o));let d=u.relatedTarget;d instanceof HTMLElement&&d.dataset.headlessuiFocusGuard!==\"true\"&&(N(T,d)||(s.value?b(c(o),P(f.value,{[y.Forwards]:()=>v.Next,[y.Backwards]:()=>v.Previous})|v.WrapAround,{relativeTo:u.target}):u.target instanceof HTMLElement&&p(u.target)))}return()=>{let u={},T={ref:o,onKeydown:F,onFocusout:H},{features:d,initialFocus:w,containers:Q,...O}=t;return R(j,[Boolean(d&4)&&R(D,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:a,features:k.Focusable}),G({ourProps:T,theirProps:{...n,...O},slot:u,attrs:n,slots:r,name:\"FocusTrap\"}),Boolean(d&4)&&R(D,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:a,features:k.Focusable})])}}}),{features:A});function W(t){let n=E(C.slice());return g([t],([r],[l])=>{l===!0&&r===!1?S(()=>{n.value.splice(0)}):l===!1&&r===!0&&(n.value=C.slice())},{flush:\"post\"}),()=>{var r;return(r=n.value.find(l=>l!=null&&l.isConnected))!=null?r:null}}function $({ownerDocument:t},n){let r=W(n);M(()=>{K(()=>{var l,o;n.value||((l=t.value)==null?void 0:l.activeElement)===((o=t.value)==null?void 0:o.body)&&p(r())},{flush:\"post\"})}),h(()=>{n.value&&p(r())})}function z({ownerDocument:t,container:n,initialFocus:r},l){let o=E(null),i=E(!1);return M(()=>i.value=!0),h(()=>i.value=!1),M(()=>{g([n,r,l],(e,m)=>{if(e.every((a,s)=>(m==null?void 0:m[s])===a)||!l.value)return;let f=c(n);f&&S(()=>{var F,H;if(!i.value)return;let a=c(r),s=(F=t.value)==null?void 0:F.activeElement;if(a){if(a===s){o.value=s;return}}else if(f.contains(s)){o.value=s;return}a?p(a):b(f,v.First|v.NoScroll)===q.Error&&console.warn(\"There are no focusable elements inside the <FocusTrap />\"),o.value=(H=t.value)==null?void 0:H.activeElement})},{immediate:!0,flush:\"post\"})}),o}function J({ownerDocument:t,container:n,containers:r,previousActiveElement:l},o){var i;U((i=t.value)==null?void 0:i.defaultView,\"focus\",e=>{if(!o.value)return;let m=B(r);c(n)instanceof HTMLElement&&m.add(c(n));let f=l.value;if(!f)return;let a=e.target;a&&a instanceof HTMLElement?N(m,a)?(l.value=a,p(a)):(e.preventDefault(),e.stopPropagation(),p(f)):p(l.value)},!0)}function N(t,n){for(let r of t)if(r.contains(n))return!0;return!1}export{ue as FocusTrap};\n", "import{onUnmounted as o,shallowRef as n}from\"vue\";function m(t){let e=n(t.getSnapshot());return o(t.subscribe(()=>{e.value=t.getSnapshot()})),e}export{m as useStore};\n", "function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n", "function c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}export{c as adjustScrollbarPadding};\n", "import{disposables as m}from'../../utils/disposables.js';import{isIOS as u}from'../../utils/platform.js';function w(){return u()?{before({doc:r,d:n,meta:c}){function a(o){return c.containers.flatMap(l=>l()).some(l=>l.contains(o))}n.microTask(()=>{var s;if(window.getComputedStyle(r.documentElement).scrollBehavior!==\"auto\"){let t=m();t.style(r.documentElement,\"scrollBehavior\",\"auto\"),n.add(()=>n.microTask(()=>t.dispose()))}let o=(s=window.scrollY)!=null?s:window.pageYOffset,l=null;n.addEventListener(r,\"click\",t=>{if(t.target instanceof HTMLElement)try{let e=t.target.closest(\"a\");if(!e)return;let{hash:f}=new URL(e.href),i=r.querySelector(f);i&&!a(i)&&(l=i)}catch{}},!0),n.addEventListener(r,\"touchstart\",t=>{if(t.target instanceof HTMLElement)if(a(t.target)){let e=t.target;for(;e.parentElement&&a(e.parentElement);)e=e.parentElement;n.style(e,\"overscrollBehavior\",\"contain\")}else n.style(t.target,\"touchAction\",\"none\")}),n.addEventListener(r,\"touchmove\",t=>{if(t.target instanceof HTMLElement){if(t.target.tagName===\"INPUT\")return;if(a(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),n.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:\"nearest\"}),l=null)})})}}:{}}export{w as handleIOSLocking};\n", "function l(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{l as preventScroll};\n", "import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n", "import{computed as p,watch as s}from\"vue\";import{useStore as v}from'../../hooks/use-store.js';import{overflows as u}from'./overflow-store.js';function d(t,a,n){let i=v(u),l=p(()=>{let e=t.value?i.value.get(t.value):void 0;return e?e.count>0:!1});return s([t,a],([e,m],[r],o)=>{if(!e||!m)return;u.dispatch(\"PUSH\",e,n);let f=!1;o(()=>{f||(u.dispatch(\"POP\",r!=null?r:e,n),f=!0)})},{immediate:!0}),l}export{d as useDocumentOverflowLockedEffect};\n", "import{ref as m,watchEffect as s}from\"vue\";import{dom as h}from'../utils/dom.js';let i=new Map,t=new Map;function E(d,f=m(!0)){s(o=>{var a;if(!f.value)return;let e=h(d);if(!e)return;o(function(){var u;if(!e)return;let r=(u=t.get(e))!=null?u:1;if(r===1?t.delete(e):t.set(e,r-1),r!==1)return;let n=i.get(e);n&&(n[\"aria-hidden\"]===null?e.removeAttribute(\"aria-hidden\"):e.setAttribute(\"aria-hidden\",n[\"aria-hidden\"]),e.inert=n.inert,i.delete(e))});let l=(a=t.get(e))!=null?a:0;t.set(e,l+1),l===0&&(i.set(e,{\"aria-hidden\":e.getAttribute(\"aria-hidden\"),inert:e.inert}),e.setAttribute(\"aria-hidden\",\"true\"),e.inert=!0)})}export{E as useInert};\n", "import{h as m,ref as s}from\"vue\";import{Features as d,Hidden as c}from'../internal/hidden.js';import{dom as T}from'../utils/dom.js';import{getOwnerDocument as E}from'../utils/owner.js';function N({defaultContainers:o=[],portals:i,mainTreeNodeRef:H}={}){let t=s(null),r=E(t);function u(){var l,f,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):\"value\"in e&&e.value instanceof HTMLElement&&n.push(e.value));if(i!=null&&i.value)for(let e of i.value)n.push(e);for(let e of(l=r==null?void 0:r.querySelectorAll(\"html > *, body > *\"))!=null?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!==\"headlessui-portal-root\"&&(e.contains(T(t))||e.contains((a=(f=T(t))==null?void 0:f.getRootNode())==null?void 0:a.host)||n.some(M=>e.contains(M))||n.push(e));return n}return{resolveContainers:u,contains(n){return u().some(l=>l.contains(n))},mainTreeNodeRef:t,MainTreeNode(){return H!=null?null:m(c,{features:d.Hidden,ref:t})}}}function v(){let o=s(null);return{mainTreeNodeRef:o,MainTreeNode(){return m(c,{features:d.Hidden,ref:o})}}}export{v as useMainTreeNode,N as useRootContainers};\n", "import{defineComponent as l,inject as a,provide as c}from\"vue\";import{render as p}from'../utils/render.js';let e=Symbol(\"ForcePortalRootContext\");function s(){return a(e,!1)}let u=l({name:\"ForcePortalRoot\",props:{as:{type:[Object,String],default:\"template\"},force:{type:Boolean,default:!1}},setup(o,{slots:t,attrs:r}){return c(e,o.force),()=>{let{force:f,...n}=o;return p({theirProps:n,ourProps:{},slot:{},slots:t,attrs:r,name:\"ForcePortalRoot\"})}}});export{u as ForcePortalRoot,s as usePortalRoot};\n", "import{inject as f,onMounted as m,onUnmounted as l,provide as c,watch as p}from\"vue\";let u=Symbol(\"StackContext\");var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function y(){return f(u,()=>{})}function R({type:o,enabled:r,element:e,onUpdate:i}){let a=y();function t(...n){i==null||i(...n),a(...n)}m(()=>{p(r,(n,d)=>{n?t(0,o,e):d===!0&&t(1,o,e)},{immediate:!0,flush:\"sync\"})}),l(()=>{r.value&&t(1,o,e)}),c(u,t)}export{s as StackMessage,y as useStackContext,R as useStackProvider};\n", "import{computed as x,defineComponent as y,inject as R,onMounted as v,onUnmounted as D,provide as j,ref as p,unref as C}from\"vue\";import{useId as h}from'../../hooks/use-id.js';import{render as b}from'../../utils/render.js';let u=Symbol(\"DescriptionContext\");function w(){let t=R(u,null);if(t===null)throw new Error(\"Missing parent\");return t}function k({slot:t=p({}),name:o=\"Description\",props:s={}}={}){let e=p([]);function r(n){return e.value.push(n),()=>{let i=e.value.indexOf(n);i!==-1&&e.value.splice(i,1)}}return j(u,{register:r,slot:t,name:o,props:s}),x(()=>e.value.length>0?e.value.join(\" \"):void 0)}let K=y({name:\"Description\",props:{as:{type:[Object,String],default:\"p\"},id:{type:String,default:null}},setup(t,{attrs:o,slots:s}){var n;let e=(n=t.id)!=null?n:`headlessui-description-${h()}`,r=w();return v(()=>D(r.register(e))),()=>{let{name:i=\"Description\",slot:l=p({}),props:d={}}=r,{...c}=t,f={...Object.entries(d).reduce((a,[g,m])=>Object.assign(a,{[g]:C(m)}),{}),id:e};return b({ourProps:f,theirProps:c,slot:l.value,attrs:o,slots:s,name:i})}}});export{K as Description,k as useDescriptions};\n", "import{computed as w,defineComponent as m,getCurrentInstance as j,h as I,inject as s,onMounted as R,onUnmounted as y,provide as E,reactive as G,ref as p,Teleport as O,watch as D,watchEffect as K}from\"vue\";import{usePortalRoot as S}from'../../internal/portal-force-root.js';import{dom as B}from'../../utils/dom.js';import{getOwnerDocument as C}from'../../utils/owner.js';import{render as h}from'../../utils/render.js';function x(e){let t=C(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let l=t.getElementById(\"headlessui-portal-root\");if(l)return l;let r=t.createElement(\"div\");return r.setAttribute(\"id\",\"headlessui-portal-root\"),t.body.appendChild(r)}const f=new WeakMap;function U(e){var t;return(t=f.get(e))!=null?t:0}function M(e,t){let l=t(U(e));return l<=0?f.delete(e):f.set(e,l),l}let $=m({name:\"Portal\",props:{as:{type:[Object,String],default:\"div\"}},setup(e,{slots:t,attrs:l}){let r=p(null),i=w(()=>C(r)),o=S(),u=s(H,null),n=p(o===!0||u==null?x(r.value):u.resolveTarget());n.value&&M(n.value,a=>a+1);let c=p(!1);R(()=>{c.value=!0}),K(()=>{o||u!=null&&(n.value=u.resolveTarget())});let v=s(d,null),g=!1,b=j();return D(r,()=>{if(g||!v)return;let a=B(r);a&&(y(v.register(a),b),g=!0)}),y(()=>{var P,T;let a=(P=i.value)==null?void 0:P.getElementById(\"headlessui-portal-root\");!a||n.value!==a||M(n.value,L=>L-1)||n.value.children.length>0||(T=n.value.parentElement)==null||T.removeChild(n.value)}),()=>{if(!c.value||n.value===null)return null;let a={ref:r,\"data-headlessui-portal\":\"\"};return I(O,{to:n.value},h({ourProps:a,theirProps:e,slot:{},attrs:l,slots:t,name:\"Portal\"}))}}}),d=Symbol(\"PortalParentContext\");function q(){let e=s(d,null),t=p([]);function l(o){return t.value.push(o),e&&e.register(o),()=>r(o)}function r(o){let u=t.value.indexOf(o);u!==-1&&t.value.splice(u,1),e&&e.unregister(o)}let i={register:l,unregister:r,portals:t};return[t,m({name:\"PortalWrapper\",setup(o,{slots:u}){return E(d,i),()=>{var n;return(n=u.default)==null?void 0:n.call(u)}}})]}let H=Symbol(\"PortalGroupContext\"),z=m({name:\"PortalGroup\",props:{as:{type:[Object,String],default:\"template\"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:l}){let r=G({resolveTarget(){return e.target}});return E(H,r),()=>{let{target:i,...o}=e;return h({theirProps:o,ourProps:{},slot:{},attrs:t,slots:l,name:\"PortalGroup\"})}}});export{$ as Portal,z as PortalGroup,q as useNestedPortals};\n", "import{computed as o,defineComponent as O,h as v,inject as Y,nextTick as se,onMounted as $,onUnmounted as pe,provide as de,ref as y,watchEffect as fe}from\"vue\";import{FocusTrap as P}from'../../components/focus-trap/focus-trap.js';import{useDocumentOverflowLockedEffect as ge}from'../../hooks/document-overflow/use-document-overflow.js';import{useEventListener as ce}from'../../hooks/use-event-listener.js';import{useId as b}from'../../hooks/use-id.js';import{useInert as _}from'../../hooks/use-inert.js';import{useOutsideClick as ve}from'../../hooks/use-outside-click.js';import{useRootContainers as me}from'../../hooks/use-root-containers.js';import{State as I,useOpenClosed as De}from'../../internal/open-closed.js';import{ForcePortalRoot as F}from'../../internal/portal-force-root.js';import{StackMessage as z,useStackProvider as ye}from'../../internal/stack-context.js';import{Keys as Se}from'../../keyboard.js';import{dom as j}from'../../utils/dom.js';import{match as G}from'../../utils/match.js';import{getOwnerDocument as he}from'../../utils/owner.js';import{Features as V,render as C}from'../../utils/render.js';import{Description as Oe,useDescriptions as Pe}from'../description/description.js';import{Portal as J,PortalGroup as be,useNestedPortals as Ce}from'../portal/portal.js';var Te=(l=>(l[l.Open=0]=\"Open\",l[l.Closed=1]=\"Closed\",l))(Te||{});let H=Symbol(\"DialogContext\");function T(t){let i=Y(H,null);if(i===null){let l=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,T),l}return i}let A=\"DC8F892D-2EBD-447C-A4C8-A03058436FF4\",Ye=O({name:\"Dialog\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:A},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:\"dialog\"}},emits:{close:t=>!0},setup(t,{emit:i,attrs:l,slots:p,expose:s}){var q,W;let n=(q=t.id)!=null?q:`headlessui-dialog-${b()}`,u=y(!1);$(()=>{u.value=!0});let r=!1,g=o(()=>t.role===\"dialog\"||t.role===\"alertdialog\"?t.role:(r||(r=!0,console.warn(`Invalid role [${g}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")),D=y(0),S=De(),R=o(()=>t.open===A&&S!==null?(S.value&I.Open)===I.Open:t.open),m=y(null),E=o(()=>he(m));if(s({el:m,$el:m}),!(t.open!==A||S!==null))throw new Error(\"You forgot to provide an `open` prop to the `Dialog`.\");if(typeof R.value!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${R.value===A?void 0:t.open}`);let c=o(()=>u.value&&R.value?0:1),k=o(()=>c.value===0),w=o(()=>D.value>1),N=Y(H,null)!==null,[Q,X]=Ce(),{resolveContainers:B,mainTreeNodeRef:K,MainTreeNode:Z}=me({portals:Q,defaultContainers:[o(()=>{var e;return(e=h.panelRef.value)!=null?e:m.value})]}),ee=o(()=>w.value?\"parent\":\"leaf\"),U=o(()=>S!==null?(S.value&I.Closing)===I.Closing:!1),te=o(()=>N||U.value?!1:k.value),le=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll(\"body > *\"))!=null?a:[]).find(f=>f.id===\"headlessui-portal-root\"?!1:f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(le,te);let ae=o(()=>w.value?!0:k.value),oe=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll(\"[data-headlessui-portal]\"))!=null?a:[]).find(f=>f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(oe,ae),ye({type:\"Dialog\",enabled:o(()=>c.value===0),element:m,onUpdate:(e,a)=>{if(a===\"Dialog\")return G(e,{[z.Add]:()=>D.value+=1,[z.Remove]:()=>D.value-=1})}});let re=Pe({name:\"DialogDescription\",slot:o(()=>({open:R.value}))}),M=y(null),h={titleId:M,panelRef:y(null),dialogState:c,setTitleId(e){M.value!==e&&(M.value=e)},close(){i(\"close\",!1)}};de(H,h);let ne=o(()=>!(!k.value||w.value));ve(B,(e,a)=>{e.preventDefault(),h.close(),se(()=>a==null?void 0:a.focus())},ne);let ie=o(()=>!(w.value||c.value!==0));ce((W=E.value)==null?void 0:W.defaultView,\"keydown\",e=>{ie.value&&(e.defaultPrevented||e.key===Se.Escape&&(e.preventDefault(),e.stopPropagation(),h.close()))});let ue=o(()=>!(U.value||c.value!==0||N));return ge(E,ue,e=>{var a;return{containers:[...(a=e.containers)!=null?a:[],B]}}),fe(e=>{if(c.value!==0)return;let a=j(m);if(!a)return;let d=new ResizeObserver(f=>{for(let L of f){let x=L.target.getBoundingClientRect();x.x===0&&x.y===0&&x.width===0&&x.height===0&&h.close()}});d.observe(a),e(()=>d.disconnect())}),()=>{let{open:e,initialFocus:a,...d}=t,f={...l,ref:m,id:n,role:g.value,\"aria-modal\":c.value===0?!0:void 0,\"aria-labelledby\":M.value,\"aria-describedby\":re.value},L={open:c.value===0};return v(F,{force:!0},()=>[v(J,()=>v(be,{target:m.value},()=>v(F,{force:!1},()=>v(P,{initialFocus:a,containers:B,features:k.value?G(ee.value,{parent:P.features.RestoreFocus,leaf:P.features.All&~P.features.FocusLock}):P.features.None},()=>v(X,{},()=>C({ourProps:f,theirProps:{...d,...l},slot:L,attrs:l,slots:p,visible:c.value===0,features:V.RenderStrategy|V.Static,name:\"Dialog\"})))))),v(Z)])}}}),_e=O({name:\"DialogOverlay\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var u;let p=(u=t.id)!=null?u:`headlessui-dialog-overlay-${b()}`,s=T(\"DialogOverlay\");function n(r){r.target===r.currentTarget&&(r.preventDefault(),r.stopPropagation(),s.close())}return()=>{let{...r}=t;return C({ourProps:{id:p,\"aria-hidden\":!0,onClick:n},theirProps:r,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:\"DialogOverlay\"})}}}),ze=O({name:\"DialogBackdrop\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-backdrop-${b()}`,n=T(\"DialogBackdrop\"),u=y(null);return p({el:u,$el:u}),$(()=>{if(n.panelRef.value===null)throw new Error(\"A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.\")}),()=>{let{...g}=t,D={id:s,ref:u,\"aria-hidden\":!0};return v(F,{force:!0},()=>v(J,()=>C({ourProps:D,theirProps:{...i,...g},slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:\"DialogBackdrop\"})))}}}),Ge=O({name:\"DialogPanel\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-panel-${b()}`,n=T(\"DialogPanel\");p({el:n.panelRef,$el:n.panelRef});function u(g){g.stopPropagation()}return()=>{let{...g}=t,D={id:s,ref:n.panelRef,onClick:u};return C({ourProps:D,theirProps:g,slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:\"DialogPanel\"})}}}),Ve=O({name:\"DialogTitle\",props:{as:{type:[Object,String],default:\"h2\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var n;let p=(n=t.id)!=null?n:`headlessui-dialog-title-${b()}`,s=T(\"DialogTitle\");return $(()=>{s.setTitleId(p),pe(()=>s.setTitleId(null))}),()=>{let{...u}=t;return C({ourProps:{id:p},theirProps:u,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:\"DialogTitle\"})}}}),Je=Oe;export{Ye as Dialog,ze as DialogBackdrop,Je as DialogDescription,_e as DialogOverlay,Ge as DialogPanel,Ve as DialogTitle};\n", "import{computed as m,defineComponent as b,inject as I,onMounted as P,onUnmounted as h,provide as R,ref as d,watchEffect as w}from\"vue\";import{useId as E}from'../../hooks/use-id.js';import{useResolveButtonType as H}from'../../hooks/use-resolve-button-type.js';import{State as y,useOpenClosed as L,useOpenClosedProvider as j}from'../../internal/open-closed.js';import{Keys as f}from'../../keyboard.js';import{dom as p}from'../../utils/dom.js';import{match as x}from'../../utils/match.js';import{Features as B,render as g}from'../../utils/render.js';var $=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))($||{});let T=Symbol(\"DisclosureContext\");function O(t){let r=I(T,null);if(r===null){let o=new Error(`<${t} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return r}let k=Symbol(\"DisclosurePanelContext\");function U(){return I(k,null)}let N=b({name:\"Disclosure\",props:{as:{type:[Object,String],default:\"template\"},defaultOpen:{type:[Boolean],default:!1}},setup(t,{slots:r,attrs:o}){let s=d(t.defaultOpen?0:1),e=d(null),i=d(null),n={buttonId:d(`headlessui-disclosure-button-${E()}`),panelId:d(`headlessui-disclosure-panel-${E()}`),disclosureState:s,panel:e,button:i,toggleDisclosure(){s.value=x(s.value,{[0]:1,[1]:0})},closeDisclosure(){s.value!==1&&(s.value=1)},close(l){n.closeDisclosure();let a=(()=>l?l instanceof HTMLElement?l:l.value instanceof HTMLElement?p(l):p(n.button):p(n.button))();a==null||a.focus()}};return R(T,n),j(m(()=>x(s.value,{[0]:y.Open,[1]:y.Closed}))),()=>{let{defaultOpen:l,...a}=t,c={open:s.value===0,close:n.close};return g({theirProps:a,ourProps:{},slot:c,slots:r,attrs:o,name:\"Disclosure\"})}}}),Q=b({name:\"DisclosureButton\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O(\"DisclosureButton\"),i=U(),n=m(()=>i===null?!1:i.value===e.panelId.value);P(()=>{n.value||t.id!==null&&(e.buttonId.value=t.id)}),h(()=>{n.value||(e.buttonId.value=null)});let l=d(null);s({el:l,$el:l}),n.value||w(()=>{e.button.value=l.value});let a=H(m(()=>({as:t.as,type:r.type})),l);function c(){var u;t.disabled||(n.value?(e.toggleDisclosure(),(u=p(e.button))==null||u.focus()):e.toggleDisclosure())}function D(u){var S;if(!t.disabled)if(n.value)switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure(),(S=p(e.button))==null||S.focus();break}else switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure();break}}function v(u){switch(u.key){case f.Space:u.preventDefault();break}}return()=>{var C;let u={open:e.disclosureState.value===0},{id:S,...K}=t,M=n.value?{ref:l,type:a.value,onClick:c,onKeydown:D}:{id:(C=e.buttonId.value)!=null?C:S,ref:l,type:a.value,\"aria-expanded\":e.disclosureState.value===0,\"aria-controls\":e.disclosureState.value===0||p(e.panel)?e.panelId.value:void 0,disabled:t.disabled?!0:void 0,onClick:c,onKeydown:D,onKeyup:v};return g({ourProps:M,theirProps:K,slot:u,attrs:r,slots:o,name:\"DisclosureButton\"})}}}),V=b({name:\"DisclosurePanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O(\"DisclosurePanel\");P(()=>{t.id!==null&&(e.panelId.value=t.id)}),h(()=>{e.panelId.value=null}),s({el:e.panel,$el:e.panel}),R(k,e.panelId);let i=L(),n=m(()=>i!==null?(i.value&y.Open)===y.Open:e.disclosureState.value===0);return()=>{var v;let l={open:e.disclosureState.value===0,close:e.close},{id:a,...c}=t,D={id:(v=e.panelId.value)!=null?v:a,ref:e.panel};return g({ourProps:D,theirProps:c,slot:l,attrs:r,slots:o,features:B.RenderStrategy|B.Static,visible:n.value,name:\"DisclosurePanel\"})}}});export{N as Disclosure,Q as DisclosureButton,V as DisclosurePanel};\n", "let a=/([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;function o(e){var r,i;let n=(r=e.innerText)!=null?r:\"\",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]'))f.remove(),u=!0;let l=u?(i=t.innerText)!=null?i:\"\":n;return a.test(l)&&(l=l.replace(a,\"\")),l}function g(e){let n=e.getAttribute(\"aria-label\");if(typeof n==\"string\")return n.trim();let t=e.getAttribute(\"aria-labelledby\");if(t){let u=t.split(\" \").map(l=>{let r=document.getElementById(l);if(r){let i=r.getAttribute(\"aria-label\");return typeof i==\"string\"?i.trim():o(r).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(\", \")}return o(e).trim()}export{g as getTextValue};\n", "import{ref as n}from\"vue\";import{dom as o}from'../utils/dom.js';import{getTextValue as i}from'../utils/get-text-value.js';function p(a){let t=n(\"\"),r=n(\"\");return()=>{let e=o(a);if(!e)return\"\";let l=e.innerText;if(t.value===l)return r.value;let u=i(e).trim().toLowerCase();return t.value=l,r.value=u,u}}export{p as useTextValue};\n", "import{computed as x,defineComponent as E,Fragment as z,h as N,inject as _,nextTick as V,onMounted as K,onUnmounted as q,provide as W,ref as T,toRaw as R,watch as H,watchEffect as G}from\"vue\";import{useControllable as J}from'../../hooks/use-controllable.js';import{useId as F}from'../../hooks/use-id.js';import{useOutsideClick as X}from'../../hooks/use-outside-click.js';import{useResolveButtonType as Y}from'../../hooks/use-resolve-button-type.js';import{useTextValue as Z}from'../../hooks/use-text-value.js';import{useTrackedPointer as ee}from'../../hooks/use-tracked-pointer.js';import{Features as te,Hidden as oe}from'../../internal/hidden.js';import{State as B,useOpenClosed as ae,useOpenClosedProvider as ie}from'../../internal/open-closed.js';import{Keys as c}from'../../keyboard.js';import{calculateActiveIndex as ne,Focus as g}from'../../utils/calculate-active-index.js';import{dom as S}from'../../utils/dom.js';import{FocusableMode as le,isFocusableElement as ue,sortByDomNode as re}from'../../utils/focus-management.js';import{objectToFormEntries as se}from'../../utils/form.js';import{match as P}from'../../utils/match.js';import{compact as de,Features as U,omit as fe,render as j}from'../../utils/render.js';function pe(o,b){return o===b}var ce=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(ce||{}),ve=(r=>(r[r.Single=0]=\"Single\",r[r.Multi=1]=\"Multi\",r))(ve||{}),be=(r=>(r[r.Pointer=0]=\"Pointer\",r[r.Other=1]=\"Other\",r))(be||{});function me(o){requestAnimationFrame(()=>requestAnimationFrame(o))}let $=Symbol(\"ListboxContext\");function A(o){let b=_($,null);if(b===null){let r=new Error(`<${o} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,A),r}return b}let Ie=E({name:\"Listbox\",emits:{\"update:modelValue\":o=>!0},props:{as:{type:[Object,String],default:\"template\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>pe},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(o,{slots:b,attrs:r,emit:w}){let n=T(1),e=T(null),f=T(null),v=T(null),s=T([]),m=T(\"\"),p=T(null),a=T(1);function u(t=i=>i){let i=p.value!==null?s.value[p.value]:null,l=re(t(s.value.slice()),O=>S(O.dataRef.domRef)),d=i?l.indexOf(i):null;return d===-1&&(d=null),{options:l,activeOptionIndex:d}}let D=x(()=>o.multiple?1:0),[y,L]=J(x(()=>o.modelValue),t=>w(\"update:modelValue\",t),x(()=>o.defaultValue)),M=x(()=>y.value===void 0?P(D.value,{[1]:[],[0]:void 0}):y.value),k={listboxState:n,value:M,mode:D,compare(t,i){if(typeof o.by==\"string\"){let l=o.by;return(t==null?void 0:t[l])===(i==null?void 0:i[l])}return o.by(t,i)},orientation:x(()=>o.horizontal?\"horizontal\":\"vertical\"),labelRef:e,buttonRef:f,optionsRef:v,disabled:x(()=>o.disabled),options:s,searchQuery:m,activeOptionIndex:p,activationTrigger:a,closeListbox(){o.disabled||n.value!==1&&(n.value=1,p.value=null)},openListbox(){o.disabled||n.value!==0&&(n.value=0)},goToOption(t,i,l){if(o.disabled||n.value===1)return;let d=u(),O=ne(t===g.Specific?{focus:g.Specific,id:i}:{focus:t},{resolveItems:()=>d.options,resolveActiveIndex:()=>d.activeOptionIndex,resolveId:h=>h.id,resolveDisabled:h=>h.dataRef.disabled});m.value=\"\",p.value=O,a.value=l!=null?l:1,s.value=d.options},search(t){if(o.disabled||n.value===1)return;let l=m.value!==\"\"?0:1;m.value+=t.toLowerCase();let O=(p.value!==null?s.value.slice(p.value+l).concat(s.value.slice(0,p.value+l)):s.value).find(I=>I.dataRef.textValue.startsWith(m.value)&&!I.dataRef.disabled),h=O?s.value.indexOf(O):-1;h===-1||h===p.value||(p.value=h,a.value=1)},clearSearch(){o.disabled||n.value!==1&&m.value!==\"\"&&(m.value=\"\")},registerOption(t,i){let l=u(d=>[...d,{id:t,dataRef:i}]);s.value=l.options,p.value=l.activeOptionIndex},unregisterOption(t){let i=u(l=>{let d=l.findIndex(O=>O.id===t);return d!==-1&&l.splice(d,1),l});s.value=i.options,p.value=i.activeOptionIndex,a.value=1},theirOnChange(t){o.disabled||L(t)},select(t){o.disabled||L(P(D.value,{[0]:()=>t,[1]:()=>{let i=R(k.value.value).slice(),l=R(t),d=i.findIndex(O=>k.compare(l,R(O)));return d===-1?i.push(l):i.splice(d,1),i}}))}};X([f,v],(t,i)=>{var l;k.closeListbox(),ue(i,le.Loose)||(t.preventDefault(),(l=S(f))==null||l.focus())},x(()=>n.value===0)),W($,k),ie(x(()=>P(n.value,{[0]:B.Open,[1]:B.Closed})));let C=x(()=>{var t;return(t=S(f))==null?void 0:t.closest(\"form\")});return K(()=>{H([C],()=>{if(!C.value||o.defaultValue===void 0)return;function t(){k.theirOnChange(o.defaultValue)}return C.value.addEventListener(\"reset\",t),()=>{var i;(i=C.value)==null||i.removeEventListener(\"reset\",t)}},{immediate:!0})}),()=>{let{name:t,modelValue:i,disabled:l,form:d,...O}=o,h={open:n.value===0,disabled:l,value:M.value};return N(z,[...t!=null&&M.value!=null?se({[t]:M.value}).map(([I,Q])=>N(oe,de({features:te.Hidden,key:I,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:d,disabled:l,name:I,value:Q}))):[],j({ourProps:{},theirProps:{...r,...fe(O,[\"defaultValue\",\"onUpdate:modelValue\",\"horizontal\",\"multiple\",\"by\"])},slot:h,slots:b,attrs:r,name:\"Listbox\"})])}}}),Ee=E({name:\"ListboxLabel\",props:{as:{type:[Object,String],default:\"label\"},id:{type:String,default:null}},setup(o,{attrs:b,slots:r}){var f;let w=(f=o.id)!=null?f:`headlessui-listbox-label-${F()}`,n=A(\"ListboxLabel\");function e(){var v;(v=S(n.buttonRef))==null||v.focus({preventScroll:!0})}return()=>{let v={open:n.listboxState.value===0,disabled:n.disabled.value},{...s}=o,m={id:w,ref:n.labelRef,onClick:e};return j({ourProps:m,theirProps:s,slot:v,attrs:b,slots:r,name:\"ListboxLabel\"})}}}),je=E({name:\"ListboxButton\",props:{as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(o,{attrs:b,slots:r,expose:w}){var p;let n=(p=o.id)!=null?p:`headlessui-listbox-button-${F()}`,e=A(\"ListboxButton\");w({el:e.buttonRef,$el:e.buttonRef});function f(a){switch(a.key){case c.Space:case c.Enter:case c.ArrowDown:a.preventDefault(),e.openListbox(),V(()=>{var u;(u=S(e.optionsRef))==null||u.focus({preventScroll:!0}),e.value.value||e.goToOption(g.First)});break;case c.ArrowUp:a.preventDefault(),e.openListbox(),V(()=>{var u;(u=S(e.optionsRef))==null||u.focus({preventScroll:!0}),e.value.value||e.goToOption(g.Last)});break}}function v(a){switch(a.key){case c.Space:a.preventDefault();break}}function s(a){e.disabled.value||(e.listboxState.value===0?(e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})})):(a.preventDefault(),e.openListbox(),me(()=>{var u;return(u=S(e.optionsRef))==null?void 0:u.focus({preventScroll:!0})})))}let m=Y(x(()=>({as:o.as,type:b.type})),e.buttonRef);return()=>{var y,L;let a={open:e.listboxState.value===0,disabled:e.disabled.value,value:e.value.value},{...u}=o,D={ref:e.buttonRef,id:n,type:m.value,\"aria-haspopup\":\"listbox\",\"aria-controls\":(y=S(e.optionsRef))==null?void 0:y.id,\"aria-expanded\":e.listboxState.value===0,\"aria-labelledby\":e.labelRef.value?[(L=S(e.labelRef))==null?void 0:L.id,n].join(\" \"):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:f,onKeyup:v,onClick:s};return j({ourProps:D,theirProps:u,slot:a,attrs:b,slots:r,name:\"ListboxButton\"})}}}),Ae=E({name:\"ListboxOptions\",props:{as:{type:[Object,String],default:\"ul\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(o,{attrs:b,slots:r,expose:w}){var p;let n=(p=o.id)!=null?p:`headlessui-listbox-options-${F()}`,e=A(\"ListboxOptions\"),f=T(null);w({el:e.optionsRef,$el:e.optionsRef});function v(a){switch(f.value&&clearTimeout(f.value),a.key){case c.Space:if(e.searchQuery.value!==\"\")return a.preventDefault(),a.stopPropagation(),e.search(a.key);case c.Enter:if(a.preventDefault(),a.stopPropagation(),e.activeOptionIndex.value!==null){let u=e.options.value[e.activeOptionIndex.value];e.select(u.dataRef.value)}e.mode.value===0&&(e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})}));break;case P(e.orientation.value,{vertical:c.ArrowDown,horizontal:c.ArrowRight}):return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Next);case P(e.orientation.value,{vertical:c.ArrowUp,horizontal:c.ArrowLeft}):return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Previous);case c.Home:case c.PageUp:return a.preventDefault(),a.stopPropagation(),e.goToOption(g.First);case c.End:case c.PageDown:return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Last);case c.Escape:a.preventDefault(),a.stopPropagation(),e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})});break;case c.Tab:a.preventDefault(),a.stopPropagation();break;default:a.key.length===1&&(e.search(a.key),f.value=setTimeout(()=>e.clearSearch(),350));break}}let s=ae(),m=x(()=>s!==null?(s.value&B.Open)===B.Open:e.listboxState.value===0);return()=>{var y,L;let a={open:e.listboxState.value===0},{...u}=o,D={\"aria-activedescendant\":e.activeOptionIndex.value===null||(y=e.options.value[e.activeOptionIndex.value])==null?void 0:y.id,\"aria-multiselectable\":e.mode.value===1?!0:void 0,\"aria-labelledby\":(L=S(e.buttonRef))==null?void 0:L.id,\"aria-orientation\":e.orientation.value,id:n,onKeydown:v,role:\"listbox\",tabIndex:0,ref:e.optionsRef};return j({ourProps:D,theirProps:u,slot:a,attrs:b,slots:r,features:U.RenderStrategy|U.Static,visible:m.value,name:\"ListboxOptions\"})}}}),Fe=E({name:\"ListboxOption\",props:{as:{type:[Object,String],default:\"li\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(o,{slots:b,attrs:r,expose:w}){var C;let n=(C=o.id)!=null?C:`headlessui-listbox-option-${F()}`,e=A(\"ListboxOption\"),f=T(null);w({el:f,$el:f});let v=x(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===n:!1),s=x(()=>P(e.mode.value,{[0]:()=>e.compare(R(e.value.value),R(o.value)),[1]:()=>R(e.value.value).some(t=>e.compare(R(t),R(o.value)))})),m=x(()=>P(e.mode.value,{[1]:()=>{var i;let t=R(e.value.value);return((i=e.options.value.find(l=>t.some(d=>e.compare(R(d),R(l.dataRef.value)))))==null?void 0:i.id)===n},[0]:()=>s.value})),p=Z(f),a=x(()=>({disabled:o.disabled,value:o.value,get textValue(){return p()},domRef:f}));K(()=>e.registerOption(n,a)),q(()=>e.unregisterOption(n)),K(()=>{H([e.listboxState,s],()=>{e.listboxState.value===0&&s.value&&P(e.mode.value,{[1]:()=>{m.value&&e.goToOption(g.Specific,n)},[0]:()=>{e.goToOption(g.Specific,n)}})},{immediate:!0})}),G(()=>{e.listboxState.value===0&&v.value&&e.activationTrigger.value!==0&&V(()=>{var t,i;return(i=(t=S(f))==null?void 0:t.scrollIntoView)==null?void 0:i.call(t,{block:\"nearest\"})})});function u(t){if(o.disabled)return t.preventDefault();e.select(o.value),e.mode.value===0&&(e.closeListbox(),V(()=>{var i;return(i=S(e.buttonRef))==null?void 0:i.focus({preventScroll:!0})}))}function D(){if(o.disabled)return e.goToOption(g.Nothing);e.goToOption(g.Specific,n)}let y=ee();function L(t){y.update(t)}function M(t){y.wasMoved(t)&&(o.disabled||v.value||e.goToOption(g.Specific,n,0))}function k(t){y.wasMoved(t)&&(o.disabled||v.value&&e.goToOption(g.Nothing))}return()=>{let{disabled:t}=o,i={active:v.value,selected:s.value,disabled:t},{value:l,disabled:d,...O}=o,h={id:n,ref:f,role:\"option\",tabIndex:t===!0?void 0:-1,\"aria-disabled\":t===!0?!0:void 0,\"aria-selected\":s.value,disabled:void 0,onClick:u,onFocus:D,onPointerenter:L,onMouseenter:L,onPointermove:M,onMousemove:M,onPointerleave:k,onMouseleave:k};return j({ourProps:h,theirProps:O,slot:i,attrs:r,slots:b,name:\"ListboxOption\"})}}});export{Ie as Listbox,je as ListboxButton,Ee as ListboxLabel,Fe as ListboxOption,Ae as ListboxOptions};\n", "import{computed as y,defineComponent as T,inject as K,nextTick as x,onMounted as N,onUnmounted as j,provide as L,ref as R,watchEffect as B}from\"vue\";import{useId as w}from'../../hooks/use-id.js';import{useOutsideClick as U}from'../../hooks/use-outside-click.js';import{useResolveButtonType as $}from'../../hooks/use-resolve-button-type.js';import{useTextValue as V}from'../../hooks/use-text-value.js';import{useTrackedPointer as H}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as Q}from'../../hooks/use-tree-walker.js';import{State as D,useOpenClosed as _,useOpenClosedProvider as q}from'../../internal/open-closed.js';import{Keys as c}from'../../keyboard.js';import{calculateActiveIndex as W,Focus as S}from'../../utils/calculate-active-index.js';import{dom as m}from'../../utils/dom.js';import{Focus as E,FocusableMode as J,focusFrom as z,isFocusableElement as G,restoreFocusIfNecessary as k,sortByDomNode as X}from'../../utils/focus-management.js';import{match as Y}from'../../utils/match.js';import{Features as F,render as P}from'../../utils/render.js';var Z=(i=>(i[i.Open=0]=\"Open\",i[i.Closed=1]=\"Closed\",i))(Z||{}),ee=(i=>(i[i.Pointer=0]=\"Pointer\",i[i.Other=1]=\"Other\",i))(ee||{});function te(o){requestAnimationFrame(()=>requestAnimationFrame(o))}let A=Symbol(\"MenuContext\");function O(o){let M=K(A,null);if(M===null){let i=new Error(`<${o} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(i,O),i}return M}let ge=T({name:\"Menu\",props:{as:{type:[Object,String],default:\"template\"}},setup(o,{slots:M,attrs:i}){let I=R(1),p=R(null),e=R(null),r=R([]),f=R(\"\"),d=R(null),g=R(1);function b(t=a=>a){let a=d.value!==null?r.value[d.value]:null,n=X(t(r.value.slice()),v=>m(v.dataRef.domRef)),s=a?n.indexOf(a):null;return s===-1&&(s=null),{items:n,activeItemIndex:s}}let l={menuState:I,buttonRef:p,itemsRef:e,items:r,searchQuery:f,activeItemIndex:d,activationTrigger:g,closeMenu:()=>{I.value=1,d.value=null},openMenu:()=>I.value=0,goToItem(t,a,n){let s=b(),v=W(t===S.Specific?{focus:S.Specific,id:a}:{focus:t},{resolveItems:()=>s.items,resolveActiveIndex:()=>s.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.disabled});f.value=\"\",d.value=v,g.value=n!=null?n:1,r.value=s.items},search(t){let n=f.value!==\"\"?0:1;f.value+=t.toLowerCase();let v=(d.value!==null?r.value.slice(d.value+n).concat(r.value.slice(0,d.value+n)):r.value).find(h=>h.dataRef.textValue.startsWith(f.value)&&!h.dataRef.disabled),u=v?r.value.indexOf(v):-1;u===-1||u===d.value||(d.value=u,g.value=1)},clearSearch(){f.value=\"\"},registerItem(t,a){let n=b(s=>[...s,{id:t,dataRef:a}]);r.value=n.items,d.value=n.activeItemIndex,g.value=1},unregisterItem(t){let a=b(n=>{let s=n.findIndex(v=>v.id===t);return s!==-1&&n.splice(s,1),n});r.value=a.items,d.value=a.activeItemIndex,g.value=1}};return U([p,e],(t,a)=>{var n;l.closeMenu(),G(a,J.Loose)||(t.preventDefault(),(n=m(p))==null||n.focus())},y(()=>I.value===0)),L(A,l),q(y(()=>Y(I.value,{[0]:D.Open,[1]:D.Closed}))),()=>{let t={open:I.value===0,close:l.closeMenu};return P({ourProps:{},theirProps:o,slot:t,slots:M,attrs:i,name:\"Menu\"})}}}),Se=T({name:\"MenuButton\",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(o,{attrs:M,slots:i,expose:I}){var b;let p=(b=o.id)!=null?b:`headlessui-menu-button-${w()}`,e=O(\"MenuButton\");I({el:e.buttonRef,$el:e.buttonRef});function r(l){switch(l.key){case c.Space:case c.Enter:case c.ArrowDown:l.preventDefault(),l.stopPropagation(),e.openMenu(),x(()=>{var t;(t=m(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(S.First)});break;case c.ArrowUp:l.preventDefault(),l.stopPropagation(),e.openMenu(),x(()=>{var t;(t=m(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(S.Last)});break}}function f(l){switch(l.key){case c.Space:l.preventDefault();break}}function d(l){o.disabled||(e.menuState.value===0?(e.closeMenu(),x(()=>{var t;return(t=m(e.buttonRef))==null?void 0:t.focus({preventScroll:!0})})):(l.preventDefault(),e.openMenu(),te(()=>{var t;return(t=m(e.itemsRef))==null?void 0:t.focus({preventScroll:!0})})))}let g=$(y(()=>({as:o.as,type:M.type})),e.buttonRef);return()=>{var n;let l={open:e.menuState.value===0},{...t}=o,a={ref:e.buttonRef,id:p,type:g.value,\"aria-haspopup\":\"menu\",\"aria-controls\":(n=m(e.itemsRef))==null?void 0:n.id,\"aria-expanded\":e.menuState.value===0,onKeydown:r,onKeyup:f,onClick:d};return P({ourProps:a,theirProps:t,slot:l,attrs:M,slots:i,name:\"MenuButton\"})}}}),Me=T({name:\"MenuItems\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(o,{attrs:M,slots:i,expose:I}){var l;let p=(l=o.id)!=null?l:`headlessui-menu-items-${w()}`,e=O(\"MenuItems\"),r=R(null);I({el:e.itemsRef,$el:e.itemsRef}),Q({container:y(()=>m(e.itemsRef)),enabled:y(()=>e.menuState.value===0),accept(t){return t.getAttribute(\"role\")===\"menuitem\"?NodeFilter.FILTER_REJECT:t.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute(\"role\",\"none\")}});function f(t){var a;switch(r.value&&clearTimeout(r.value),t.key){case c.Space:if(e.searchQuery.value!==\"\")return t.preventDefault(),t.stopPropagation(),e.search(t.key);case c.Enter:if(t.preventDefault(),t.stopPropagation(),e.activeItemIndex.value!==null){let s=e.items.value[e.activeItemIndex.value];(a=m(s.dataRef.domRef))==null||a.click()}e.closeMenu(),k(m(e.buttonRef));break;case c.ArrowDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Next);case c.ArrowUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Previous);case c.Home:case c.PageUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.First);case c.End:case c.PageDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Last);case c.Escape:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>{var n;return(n=m(e.buttonRef))==null?void 0:n.focus({preventScroll:!0})});break;case c.Tab:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>z(m(e.buttonRef),t.shiftKey?E.Previous:E.Next));break;default:t.key.length===1&&(e.search(t.key),r.value=setTimeout(()=>e.clearSearch(),350));break}}function d(t){switch(t.key){case c.Space:t.preventDefault();break}}let g=_(),b=y(()=>g!==null?(g.value&D.Open)===D.Open:e.menuState.value===0);return()=>{var s,v;let t={open:e.menuState.value===0},{...a}=o,n={\"aria-activedescendant\":e.activeItemIndex.value===null||(s=e.items.value[e.activeItemIndex.value])==null?void 0:s.id,\"aria-labelledby\":(v=m(e.buttonRef))==null?void 0:v.id,id:p,onKeydown:f,onKeyup:d,role:\"menu\",tabIndex:0,ref:e.itemsRef};return P({ourProps:n,theirProps:a,slot:t,attrs:M,slots:i,features:F.RenderStrategy|F.Static,visible:b.value,name:\"MenuItems\"})}}}),be=T({name:\"MenuItem\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"template\"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(o,{slots:M,attrs:i,expose:I}){var v;let p=(v=o.id)!=null?v:`headlessui-menu-item-${w()}`,e=O(\"MenuItem\"),r=R(null);I({el:r,$el:r});let f=y(()=>e.activeItemIndex.value!==null?e.items.value[e.activeItemIndex.value].id===p:!1),d=V(r),g=y(()=>({disabled:o.disabled,get textValue(){return d()},domRef:r}));N(()=>e.registerItem(p,g)),j(()=>e.unregisterItem(p)),B(()=>{e.menuState.value===0&&f.value&&e.activationTrigger.value!==0&&x(()=>{var u,h;return(h=(u=m(r))==null?void 0:u.scrollIntoView)==null?void 0:h.call(u,{block:\"nearest\"})})});function b(u){if(o.disabled)return u.preventDefault();e.closeMenu(),k(m(e.buttonRef))}function l(){if(o.disabled)return e.goToItem(S.Nothing);e.goToItem(S.Specific,p)}let t=H();function a(u){t.update(u)}function n(u){t.wasMoved(u)&&(o.disabled||f.value||e.goToItem(S.Specific,p,0))}function s(u){t.wasMoved(u)&&(o.disabled||f.value&&e.goToItem(S.Nothing))}return()=>{let{disabled:u,...h}=o,C={active:f.value,disabled:u,close:e.closeMenu};return P({ourProps:{id:p,ref:r,role:\"menuitem\",tabIndex:u===!0?void 0:-1,\"aria-disabled\":u===!0?!0:void 0,onClick:b,onFocus:l,onPointerenter:a,onMouseenter:a,onPointermove:n,onMousemove:n,onPointerleave:s,onMouseleave:s},theirProps:{...i,...h},slot:C,attrs:i,slots:M,name:\"MenuItem\"})}}});export{ge as Menu,Se as MenuButton,be as MenuItem,Me as MenuItems};\n", "import{computed as O,defineComponent as j,Fragment as W,h as T,inject as q,onMounted as ee,onUnmounted as te,provide as z,ref as R,shallowRef as ie,watchEffect as J}from\"vue\";import{useNestedPortals as se}from'../../components/portal/portal.js';import{useEventListener as pe}from'../../hooks/use-event-listener.js';import{useId as H}from'../../hooks/use-id.js';import{useOutsideClick as fe}from'../../hooks/use-outside-click.js';import{useResolveButtonType as ve}from'../../hooks/use-resolve-button-type.js';import{useMainTreeNode as ce,useRootContainers as de}from'../../hooks/use-root-containers.js';import{Direction as M,useTabDirection as oe}from'../../hooks/use-tab-direction.js';import{Features as Q,Hidden as X}from'../../internal/hidden.js';import{State as N,useOpenClosed as ne,useOpenClosedProvider as Pe}from'../../internal/open-closed.js';import{Keys as k}from'../../keyboard.js';import{dom as n}from'../../utils/dom.js';import{Focus as D,FocusableMode as me,focusIn as B,FocusResult as Y,getFocusableElements as Z,isFocusableElement as be}from'../../utils/focus-management.js';import{match as K}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as V}from'../../utils/owner.js';import{Features as _,render as A}from'../../utils/render.js';var Se=(s=>(s[s.Open=0]=\"Open\",s[s.Closed=1]=\"Closed\",s))(Se||{});let re=Symbol(\"PopoverContext\");function U(d){let P=q(re,null);if(P===null){let s=new Error(`<${d} /> is missing a parent <${ye.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(s,U),s}return P}let le=Symbol(\"PopoverGroupContext\");function ae(){return q(le,null)}let ue=Symbol(\"PopoverPanelContext\");function ge(){return q(ue,null)}let ye=j({name:\"Popover\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"}},setup(d,{slots:P,attrs:s,expose:h}){var u;let f=R(null);h({el:f,$el:f});let t=R(1),o=R(null),y=R(null),v=R(null),m=R(null),b=O(()=>V(f)),E=O(()=>{var L,$;if(!n(o)||!n(m))return!1;for(let x of document.querySelectorAll(\"body > *\"))if(Number(x==null?void 0:x.contains(n(o)))^Number(x==null?void 0:x.contains(n(m))))return!0;let e=Z(),r=e.indexOf(n(o)),l=(r+e.length-1)%e.length,g=(r+1)%e.length,G=e[l],C=e[g];return!((L=n(m))!=null&&L.contains(G))&&!(($=n(m))!=null&&$.contains(C))}),a={popoverState:t,buttonId:R(null),panelId:R(null),panel:m,button:o,isPortalled:E,beforePanelSentinel:y,afterPanelSentinel:v,togglePopover(){t.value=K(t.value,{[0]:1,[1]:0})},closePopover(){t.value!==1&&(t.value=1)},close(e){a.closePopover();let r=(()=>e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?n(e):n(a.button):n(a.button))();r==null||r.focus()}};z(re,a),Pe(O(()=>K(t.value,{[0]:N.Open,[1]:N.Closed})));let S={buttonId:a.buttonId,panelId:a.panelId,close(){a.closePopover()}},c=ae(),I=c==null?void 0:c.registerPopover,[F,w]=se(),i=de({mainTreeNodeRef:c==null?void 0:c.mainTreeNodeRef,portals:F,defaultContainers:[o,m]});function p(){var e,r,l,g;return(g=c==null?void 0:c.isFocusWithinPopoverGroup())!=null?g:((e=b.value)==null?void 0:e.activeElement)&&(((r=n(o))==null?void 0:r.contains(b.value.activeElement))||((l=n(m))==null?void 0:l.contains(b.value.activeElement)))}return J(()=>I==null?void 0:I(S)),pe((u=b.value)==null?void 0:u.defaultView,\"focus\",e=>{var r,l;e.target!==window&&e.target instanceof HTMLElement&&t.value===0&&(p()||o&&m&&(i.contains(e.target)||(r=n(a.beforePanelSentinel))!=null&&r.contains(e.target)||(l=n(a.afterPanelSentinel))!=null&&l.contains(e.target)||a.closePopover()))},!0),fe(i.resolveContainers,(e,r)=>{var l;a.closePopover(),be(r,me.Loose)||(e.preventDefault(),(l=n(o))==null||l.focus())},O(()=>t.value===0)),()=>{let e={open:t.value===0,close:a.close};return T(W,[T(w,{},()=>A({theirProps:{...d,...s},ourProps:{ref:f},slot:e,slots:P,attrs:s,name:\"Popover\"})),T(i.MainTreeNode)])}}}),Ge=j({name:\"PopoverButton\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(d,{attrs:P,slots:s,expose:h}){var u;let f=(u=d.id)!=null?u:`headlessui-popover-button-${H()}`,t=U(\"PopoverButton\"),o=O(()=>V(t.button));h({el:t.button,$el:t.button}),ee(()=>{t.buttonId.value=f}),te(()=>{t.buttonId.value=null});let y=ae(),v=y==null?void 0:y.closeOthers,m=ge(),b=O(()=>m===null?!1:m.value===t.panelId.value),E=R(null),a=`headlessui-focus-sentinel-${H()}`;b.value||J(()=>{t.button.value=n(E)});let S=ve(O(()=>({as:d.as,type:P.type})),E);function c(e){var r,l,g,G,C;if(b.value){if(t.popoverState.value===1)return;switch(e.key){case k.Space:case k.Enter:e.preventDefault(),(l=(r=e.target).click)==null||l.call(r),t.closePopover(),(g=n(t.button))==null||g.focus();break}}else switch(e.key){case k.Space:case k.Enter:e.preventDefault(),e.stopPropagation(),t.popoverState.value===1&&(v==null||v(t.buttonId.value)),t.togglePopover();break;case k.Escape:if(t.popoverState.value!==0)return v==null?void 0:v(t.buttonId.value);if(!n(t.button)||(G=o.value)!=null&&G.activeElement&&!((C=n(t.button))!=null&&C.contains(o.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),t.closePopover();break}}function I(e){b.value||e.key===k.Space&&e.preventDefault()}function F(e){var r,l;d.disabled||(b.value?(t.closePopover(),(r=n(t.button))==null||r.focus()):(e.preventDefault(),e.stopPropagation(),t.popoverState.value===1&&(v==null||v(t.buttonId.value)),t.togglePopover(),(l=n(t.button))==null||l.focus()))}function w(e){e.preventDefault(),e.stopPropagation()}let i=oe();function p(){let e=n(t.panel);if(!e)return;function r(){K(i.value,{[M.Forwards]:()=>B(e,D.First),[M.Backwards]:()=>B(e,D.Last)})===Y.Error&&B(Z().filter(g=>g.dataset.headlessuiFocusGuard!==\"true\"),K(i.value,{[M.Forwards]:D.Next,[M.Backwards]:D.Previous}),{relativeTo:n(t.button)})}r()}return()=>{let e=t.popoverState.value===0,r={open:e},{...l}=d,g=b.value?{ref:E,type:S.value,onKeydown:c,onClick:F}:{ref:E,id:f,type:S.value,\"aria-expanded\":t.popoverState.value===0,\"aria-controls\":n(t.panel)?t.panelId.value:void 0,disabled:d.disabled?!0:void 0,onKeydown:c,onKeyup:I,onClick:F,onMousedown:w};return T(W,[A({ourProps:g,theirProps:{...P,...l},slot:r,attrs:P,slots:s,name:\"PopoverButton\"}),e&&!b.value&&t.isPortalled.value&&T(X,{id:a,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:p})])}}}),$e=j({name:\"PopoverOverlay\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(d,{attrs:P,slots:s}){let h=U(\"PopoverOverlay\"),f=`headlessui-popover-overlay-${H()}`,t=ne(),o=O(()=>t!==null?(t.value&N.Open)===N.Open:h.popoverState.value===0);function y(){h.closePopover()}return()=>{let v={open:h.popoverState.value===0};return A({ourProps:{id:f,\"aria-hidden\":!0,onClick:y},theirProps:d,slot:v,attrs:P,slots:s,features:_.RenderStrategy|_.Static,visible:o.value,name:\"PopoverOverlay\"})}}}),je=j({name:\"PopoverPanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(d,{attrs:P,slots:s,expose:h}){var w;let f=(w=d.id)!=null?w:`headlessui-popover-panel-${H()}`,{focus:t}=d,o=U(\"PopoverPanel\"),y=O(()=>V(o.panel)),v=`headlessui-focus-sentinel-before-${H()}`,m=`headlessui-focus-sentinel-after-${H()}`;h({el:o.panel,$el:o.panel}),ee(()=>{o.panelId.value=f}),te(()=>{o.panelId.value=null}),z(ue,o.panelId),J(()=>{var p,u;if(!t||o.popoverState.value!==0||!o.panel)return;let i=(p=y.value)==null?void 0:p.activeElement;(u=n(o.panel))!=null&&u.contains(i)||B(n(o.panel),D.First)});let b=ne(),E=O(()=>b!==null?(b.value&N.Open)===N.Open:o.popoverState.value===0);function a(i){var p,u;switch(i.key){case k.Escape:if(o.popoverState.value!==0||!n(o.panel)||y.value&&!((p=n(o.panel))!=null&&p.contains(y.value.activeElement)))return;i.preventDefault(),i.stopPropagation(),o.closePopover(),(u=n(o.button))==null||u.focus();break}}function S(i){var u,e,r,l,g;let p=i.relatedTarget;p&&n(o.panel)&&((u=n(o.panel))!=null&&u.contains(p)||(o.closePopover(),((r=(e=n(o.beforePanelSentinel))==null?void 0:e.contains)!=null&&r.call(e,p)||(g=(l=n(o.afterPanelSentinel))==null?void 0:l.contains)!=null&&g.call(l,p))&&p.focus({preventScroll:!0})))}let c=oe();function I(){let i=n(o.panel);if(!i)return;function p(){K(c.value,{[M.Forwards]:()=>{var e;B(i,D.First)===Y.Error&&((e=n(o.afterPanelSentinel))==null||e.focus())},[M.Backwards]:()=>{var u;(u=n(o.button))==null||u.focus({preventScroll:!0})}})}p()}function F(){let i=n(o.panel);if(!i)return;function p(){K(c.value,{[M.Forwards]:()=>{let u=n(o.button),e=n(o.panel);if(!u)return;let r=Z(),l=r.indexOf(u),g=r.slice(0,l+1),C=[...r.slice(l+1),...g];for(let L of C.slice())if(L.dataset.headlessuiFocusGuard===\"true\"||e!=null&&e.contains(L)){let $=C.indexOf(L);$!==-1&&C.splice($,1)}B(C,D.First,{sorted:!1})},[M.Backwards]:()=>{var e;B(i,D.Previous)===Y.Error&&((e=n(o.button))==null||e.focus())}})}p()}return()=>{let i={open:o.popoverState.value===0,close:o.close},{focus:p,...u}=d,e={ref:o.panel,id:f,onKeydown:a,onFocusout:t&&o.popoverState.value===0?S:void 0,tabIndex:-1};return A({ourProps:e,theirProps:{...P,...u},attrs:P,slot:i,slots:{...s,default:(...r)=>{var l;return[T(W,[E.value&&o.isPortalled.value&&T(X,{id:v,ref:o.beforePanelSentinel,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:I}),(l=s.default)==null?void 0:l.call(s,...r),E.value&&o.isPortalled.value&&T(X,{id:m,ref:o.afterPanelSentinel,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:F})])]}},features:_.RenderStrategy|_.Static,visible:E.value,name:\"PopoverPanel\"})}}}),Ae=j({name:\"PopoverGroup\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"}},setup(d,{attrs:P,slots:s,expose:h}){let f=R(null),t=ie([]),o=O(()=>V(f)),y=ce();h({el:f,$el:f});function v(a){let S=t.value.indexOf(a);S!==-1&&t.value.splice(S,1)}function m(a){return t.value.push(a),()=>{v(a)}}function b(){var c;let a=o.value;if(!a)return!1;let S=a.activeElement;return(c=n(f))!=null&&c.contains(S)?!0:t.value.some(I=>{var F,w;return((F=a.getElementById(I.buttonId.value))==null?void 0:F.contains(S))||((w=a.getElementById(I.panelId.value))==null?void 0:w.contains(S))})}function E(a){for(let S of t.value)S.buttonId.value!==a&&S.close()}return z(le,{registerPopover:m,unregisterPopover:v,isFocusWithinPopoverGroup:b,closeOthers:E,mainTreeNodeRef:y.mainTreeNodeRef}),()=>T(W,[A({ourProps:{ref:f},theirProps:{...d,...P},slot:{},attrs:P,slots:s,name:\"PopoverGroup\"}),T(y.MainTreeNode)])}});export{ye as Popover,Ge as PopoverButton,Ae as PopoverGroup,$e as PopoverOverlay,je as PopoverPanel};\n", "import{computed as v,defineComponent as x,inject as L,onMounted as k,onUnmounted as C,provide as j,ref as y,unref as h}from\"vue\";import{useId as w}from'../../hooks/use-id.js';import{render as R}from'../../utils/render.js';let a=Symbol(\"LabelContext\");function d(){let t=L(a,null);if(t===null){let n=new Error(\"You used a <Label /> component, but it is not inside a parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(n,d),n}return t}function E({slot:t={},name:n=\"Label\",props:i={}}={}){let e=y([]);function o(r){return e.value.push(r),()=>{let l=e.value.indexOf(r);l!==-1&&e.value.splice(l,1)}}return j(a,{register:o,slot:t,name:n,props:i}),v(()=>e.value.length>0?e.value.join(\" \"):void 0)}let K=x({name:\"Label\",props:{as:{type:[Object,String],default:\"label\"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{slots:n,attrs:i}){var r;let e=(r=t.id)!=null?r:`headlessui-label-${w()}`,o=d();return k(()=>C(o.register(e))),()=>{let{name:l=\"Label\",slot:p={},props:c={}}=o,{passive:f,...s}=t,u={...Object.entries(c).reduce((b,[g,m])=>Object.assign(b,{[g]:h(m)}),{}),id:e};return f&&(delete u.onClick,delete u.htmlFor,delete s.onClick),R({ourProps:u,theirProps:s,slot:p,attrs:i,slots:n,name:l})}}});export{K as Label,E as useLabels};\n", "import{computed as o,defineComponent as F,Fragment as _,h as C,inject as $,onMounted as D,onUnmounted as U,provide as W,ref as k,toRaw as y,watch as J}from\"vue\";import{useControllable as q}from'../../hooks/use-controllable.js';import{useId as x}from'../../hooks/use-id.js';import{useTreeWalker as Q}from'../../hooks/use-tree-walker.js';import{Features as X,Hidden as Y}from'../../internal/hidden.js';import{Keys as h}from'../../keyboard.js';import{dom as E}from'../../utils/dom.js';import{Focus as w,focusIn as I,FocusResult as P,sortByDomNode as Z}from'../../utils/focus-management.js';import{attemptSubmit as z,objectToFormEntries as ee}from'../../utils/form.js';import{getOwnerDocument as A}from'../../utils/owner.js';import{compact as te,omit as ae,render as B}from'../../utils/render.js';import{Description as ne,useDescriptions as V}from'../description/description.js';import{Label as re,useLabels as j}from'../label/label.js';function le(t,m){return t===m}let H=Symbol(\"RadioGroupContext\");function N(t){let m=$(H,null);if(m===null){let u=new Error(`<${t} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,N),u}return m}let he=F({name:\"RadioGroup\",emits:{\"update:modelValue\":t=>!0},props:{as:{type:[Object,String],default:\"div\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>le},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{emit:m,attrs:u,slots:S,expose:g}){var O;let d=(O=t.id)!=null?O:`headlessui-radiogroup-${x()}`,p=k(null),l=k([]),R=j({name:\"RadioGroupLabel\"}),T=V({name:\"RadioGroupDescription\"});g({el:p,$el:p});let[f,G]=q(o(()=>t.modelValue),e=>m(\"update:modelValue\",e),o(()=>t.defaultValue)),s={options:l,value:f,disabled:o(()=>t.disabled),firstOption:o(()=>l.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:o(()=>l.value.some(e=>s.compare(y(e.propsRef.value),y(t.modelValue)))),compare(e,a){if(typeof t.by==\"string\"){let n=t.by;return(e==null?void 0:e[n])===(a==null?void 0:a[n])}return t.by(e,a)},change(e){var n;if(t.disabled||s.compare(y(f.value),y(e)))return!1;let a=(n=l.value.find(i=>s.compare(y(i.propsRef.value),y(e))))==null?void 0:n.propsRef;return a!=null&&a.disabled?!1:(G(e),!0)},registerOption(e){l.value.push(e),l.value=Z(l.value,a=>a.element)},unregisterOption(e){let a=l.value.findIndex(n=>n.id===e);a!==-1&&l.value.splice(a,1)}};W(H,s),Q({container:o(()=>E(p)),accept(e){return e.getAttribute(\"role\")===\"radio\"?NodeFilter.FILTER_REJECT:e.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute(\"role\",\"none\")}});function v(e){if(!p.value||!p.value.contains(e.target))return;let a=l.value.filter(n=>n.propsRef.disabled===!1).map(n=>n.element);switch(e.key){case h.Enter:z(e.currentTarget);break;case h.ArrowLeft:case h.ArrowUp:if(e.preventDefault(),e.stopPropagation(),I(a,w.Previous|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(p))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.ArrowRight:case h.ArrowDown:if(e.preventDefault(),e.stopPropagation(),I(a,w.Next|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(r.element))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.Space:{e.preventDefault(),e.stopPropagation();let n=l.value.find(i=>{var r;return i.element===((r=A(i.element))==null?void 0:r.activeElement)});n&&s.change(n.propsRef.value)}break}}let b=o(()=>{var e;return(e=E(p))==null?void 0:e.closest(\"form\")});return D(()=>{J([b],()=>{if(!b.value||t.defaultValue===void 0)return;function e(){s.change(t.defaultValue)}return b.value.addEventListener(\"reset\",e),()=>{var a;(a=b.value)==null||a.removeEventListener(\"reset\",e)}},{immediate:!0})}),()=>{let{disabled:e,name:a,form:n,...i}=t,r={ref:p,id:d,role:\"radiogroup\",\"aria-labelledby\":R.value,\"aria-describedby\":T.value,onKeydown:v};return C(_,[...a!=null&&f.value!=null?ee({[a]:f.value}).map(([c,L])=>C(Y,te({features:X.Hidden,key:c,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:n,disabled:e,name:c,value:L}))):[],B({ourProps:r,theirProps:{...u,...ae(i,[\"modelValue\",\"defaultValue\",\"by\"])},slot:{},attrs:u,slots:S,name:\"RadioGroup\"})])}}});var ie=(u=>(u[u.Empty=1]=\"Empty\",u[u.Active=2]=\"Active\",u))(ie||{});let Oe=F({name:\"RadioGroupOption\",props:{as:{type:[Object,String],default:\"div\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(t,{attrs:m,slots:u,expose:S}){var i;let g=(i=t.id)!=null?i:`headlessui-radiogroup-option-${x()}`,d=N(\"RadioGroupOption\"),p=j({name:\"RadioGroupLabel\"}),l=V({name:\"RadioGroupDescription\"}),R=k(null),T=o(()=>({value:t.value,disabled:t.disabled})),f=k(1);S({el:R,$el:R});let G=o(()=>E(R));D(()=>d.registerOption({id:g,element:G,propsRef:T})),U(()=>d.unregisterOption(g));let s=o(()=>{var r;return((r=d.firstOption.value)==null?void 0:r.id)===g}),v=o(()=>d.disabled.value||t.disabled),b=o(()=>d.compare(y(d.value.value),y(t.value))),O=o(()=>v.value?-1:b.value||!d.containsCheckedOption.value&&s.value?0:-1);function e(){var r;d.change(t.value)&&(f.value|=2,(r=E(R))==null||r.focus())}function a(){f.value|=2}function n(){f.value&=-3}return()=>{let{value:r,disabled:c,...L}=t,K={checked:b.value,disabled:v.value,active:Boolean(f.value&2)},M={id:g,ref:R,role:\"radio\",\"aria-checked\":b.value?\"true\":\"false\",\"aria-labelledby\":p.value,\"aria-describedby\":l.value,\"aria-disabled\":v.value?!0:void 0,tabIndex:O.value,onClick:v.value?void 0:e,onFocus:v.value?void 0:a,onBlur:v.value?void 0:n};return B({ourProps:M,theirProps:L,slot:K,attrs:m,slots:u,name:\"RadioGroupOption\"})}}}),ke=re,Ee=ne;export{he as RadioGroup,Ee as RadioGroupDescription,ke as RadioGroupLabel,Oe as RadioGroupOption};\n", "import{computed as u,defineComponent as v,Fragment as H,h as S,inject as M,onMounted as I,provide as P,ref as w,watch as j}from\"vue\";import{useControllable as G}from'../../hooks/use-controllable.js';import{useId as V}from'../../hooks/use-id.js';import{useResolveButtonType as F}from'../../hooks/use-resolve-button-type.js';import{Features as O,Hidden as A}from'../../internal/hidden.js';import{Keys as g}from'../../keyboard.js';import{dom as N}from'../../utils/dom.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as U,omit as _,render as k}from'../../utils/render.js';import{Description as q,useDescriptions as z}from'../description/description.js';import{Label as J,useLabels as Q}from'../label/label.js';let C=Symbol(\"GroupContext\"),oe=v({name:\"SwitchGroup\",props:{as:{type:[Object,String],default:\"template\"}},setup(l,{slots:c,attrs:i}){let r=w(null),f=Q({name:\"SwitchLabel\",props:{htmlFor:u(()=>{var t;return(t=r.value)==null?void 0:t.id}),onClick(t){r.value&&(t.currentTarget.tagName===\"LABEL\"&&t.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),p=z({name:\"SwitchDescription\"});return P(C,{switchRef:r,labelledby:f,describedby:p}),()=>k({theirProps:l,ourProps:{},slot:{},slots:c,attrs:i,name:\"SwitchGroup\"})}}),ue=v({name:\"Switch\",emits:{\"update:modelValue\":l=>!0},props:{as:{type:[Object,String],default:\"button\"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(l,{emit:c,attrs:i,slots:r,expose:f}){var h;let p=(h=l.id)!=null?h:`headlessui-switch-${V()}`,n=M(C,null),[t,s]=G(u(()=>l.modelValue),e=>c(\"update:modelValue\",e),u(()=>l.defaultChecked));function m(){s(!t.value)}let E=w(null),o=n===null?E:n.switchRef,L=F(u(()=>({as:l.as,type:i.type})),o);f({el:o,$el:o});function D(e){e.preventDefault(),m()}function R(e){e.key===g.Space?(e.preventDefault(),m()):e.key===g.Enter&&$(e.currentTarget)}function x(e){e.preventDefault()}let d=u(()=>{var e,a;return(a=(e=N(o))==null?void 0:e.closest)==null?void 0:a.call(e,\"form\")});return I(()=>{j([d],()=>{if(!d.value||l.defaultChecked===void 0)return;function e(){s(l.defaultChecked)}return d.value.addEventListener(\"reset\",e),()=>{var a;(a=d.value)==null||a.removeEventListener(\"reset\",e)}},{immediate:!0})}),()=>{let{name:e,value:a,form:K,tabIndex:y,...b}=l,T={checked:t.value},B={id:p,ref:o,role:\"switch\",type:L.value,tabIndex:y===-1?0:y,\"aria-checked\":t.value,\"aria-labelledby\":n==null?void 0:n.labelledby.value,\"aria-describedby\":n==null?void 0:n.describedby.value,onClick:D,onKeyup:R,onKeypress:x};return S(H,[e!=null&&t.value!=null?S(A,U({features:O.Hidden,as:\"input\",type:\"checkbox\",hidden:!0,readOnly:!0,checked:t.value,form:K,disabled:b.disabled,name:e,value:a})):null,k({ourProps:B,theirProps:{...i,..._(b,[\"modelValue\",\"defaultChecked\"])},slot:T,attrs:i,slots:r,name:\"Switch\"})])}}}),de=J,ce=q;export{ue as Switch,ce as SwitchDescription,oe as SwitchGroup,de as SwitchLabel};\n", "import{defineComponent as i,h as m,ref as f}from\"vue\";import{Features as l,Hidden as F}from'./hidden.js';let d=i({props:{onFocus:{type:Function,required:!0}},setup(t){let n=f(!0);return()=>n.value?m(F,{as:\"button\",type:\"button\",features:l.Focusable,onFocus(o){o.preventDefault();let e,a=50;function r(){var u;if(a--<=0){e&&cancelAnimationFrame(e);return}if((u=t.onFocus)!=null&&u.call(t)){n.value=!1,cancelAnimationFrame(e);return}e=requestAnimationFrame(r)}e=requestAnimationFrame(r)}}):null}});export{d as FocusSentinel};\n", "import{computed as v,defineComponent as L,Fragment as z,h as A,inject as j,onMounted as F,onUnmounted as K,provide as N,ref as P,watch as _,watchEffect as J}from\"vue\";import{useId as $}from'../../hooks/use-id.js';import{useResolveButtonType as Q}from'../../hooks/use-resolve-button-type.js';import{FocusSentinel as V}from'../../internal/focus-sentinel.js';import{Hidden as X}from'../../internal/hidden.js';import{Keys as S}from'../../keyboard.js';import{dom as f}from'../../utils/dom.js';import{Focus as g,focusIn as D,FocusResult as B,sortByDomNode as k}from'../../utils/focus-management.js';import{match as H}from'../../utils/match.js';import{microTask as Y}from'../../utils/micro-task.js';import{getOwnerDocument as Z}from'../../utils/owner.js';import{Features as q,omit as ee,render as M}from'../../utils/render.js';var te=(s=>(s[s.Forwards=0]=\"Forwards\",s[s.Backwards=1]=\"Backwards\",s))(te||{}),le=(d=>(d[d.Less=-1]=\"Less\",d[d.Equal=0]=\"Equal\",d[d.Greater=1]=\"Greater\",d))(le||{});let U=Symbol(\"TabsContext\");function C(a){let b=j(U,null);if(b===null){let s=new Error(`<${a} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(s,C),s}return b}let G=Symbol(\"TabsSSRContext\"),me=L({name:\"TabGroup\",emits:{change:a=>!0},props:{as:{type:[Object,String],default:\"template\"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(a,{slots:b,attrs:s,emit:d}){var E;let i=P((E=a.selectedIndex)!=null?E:a.defaultIndex),l=P([]),r=P([]),p=v(()=>a.selectedIndex!==null),R=v(()=>p.value?a.selectedIndex:i.value);function y(t){var c;let n=k(u.tabs.value,f),o=k(u.panels.value,f),e=n.filter(I=>{var m;return!((m=f(I))!=null&&m.hasAttribute(\"disabled\"))});if(t<0||t>n.length-1){let I=H(i.value===null?0:Math.sign(t-i.value),{[-1]:()=>1,[0]:()=>H(Math.sign(t),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0}),m=H(I,{[0]:()=>n.indexOf(e[0]),[1]:()=>n.indexOf(e[e.length-1])});m!==-1&&(i.value=m),u.tabs.value=n,u.panels.value=o}else{let I=n.slice(0,t),h=[...n.slice(t),...I].find(W=>e.includes(W));if(!h)return;let O=(c=n.indexOf(h))!=null?c:u.selectedIndex.value;O===-1&&(O=u.selectedIndex.value),i.value=O,u.tabs.value=n,u.panels.value=o}}let u={selectedIndex:v(()=>{var t,n;return(n=(t=i.value)!=null?t:a.defaultIndex)!=null?n:null}),orientation:v(()=>a.vertical?\"vertical\":\"horizontal\"),activation:v(()=>a.manual?\"manual\":\"auto\"),tabs:l,panels:r,setSelectedIndex(t){R.value!==t&&d(\"change\",t),p.value||y(t)},registerTab(t){var o;if(l.value.includes(t))return;let n=l.value[i.value];if(l.value.push(t),l.value=k(l.value,f),!p.value){let e=(o=l.value.indexOf(n))!=null?o:i.value;e!==-1&&(i.value=e)}},unregisterTab(t){let n=l.value.indexOf(t);n!==-1&&l.value.splice(n,1)},registerPanel(t){r.value.includes(t)||(r.value.push(t),r.value=k(r.value,f))},unregisterPanel(t){let n=r.value.indexOf(t);n!==-1&&r.value.splice(n,1)}};N(U,u);let T=P({tabs:[],panels:[]}),x=P(!1);F(()=>{x.value=!0}),N(G,v(()=>x.value?null:T.value));let w=v(()=>a.selectedIndex);return F(()=>{_([w],()=>{var t;return y((t=a.selectedIndex)!=null?t:a.defaultIndex)},{immediate:!0})}),J(()=>{if(!p.value||R.value==null||u.tabs.value.length<=0)return;let t=k(u.tabs.value,f);t.some((o,e)=>f(u.tabs.value[e])!==f(o))&&u.setSelectedIndex(t.findIndex(o=>f(o)===f(u.tabs.value[R.value])))}),()=>{let t={selectedIndex:i.value};return A(z,[l.value.length<=0&&A(V,{onFocus:()=>{for(let n of l.value){let o=f(n);if((o==null?void 0:o.tabIndex)===0)return o.focus(),!0}return!1}}),M({theirProps:{...s,...ee(a,[\"selectedIndex\",\"defaultIndex\",\"manual\",\"vertical\",\"onChange\"])},ourProps:{},slot:t,slots:b,attrs:s,name:\"TabGroup\"})])}}}),pe=L({name:\"TabList\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{attrs:b,slots:s}){let d=C(\"TabList\");return()=>{let i={selectedIndex:d.selectedIndex.value},l={role:\"tablist\",\"aria-orientation\":d.orientation.value};return M({ourProps:l,theirProps:a,slot:i,attrs:b,slots:s,name:\"TabList\"})}}}),xe=L({name:\"Tab\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(a,{attrs:b,slots:s,expose:d}){var o;let i=(o=a.id)!=null?o:`headlessui-tabs-tab-${$()}`,l=C(\"Tab\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerTab(r)),K(()=>l.unregisterTab(r));let p=j(G),R=v(()=>{if(p.value){let e=p.value.tabs.indexOf(i);return e===-1?p.value.tabs.push(i)-1:e}return-1}),y=v(()=>{let e=l.tabs.value.indexOf(r);return e===-1?R.value:e}),u=v(()=>y.value===l.selectedIndex.value);function T(e){var I;let c=e();if(c===B.Success&&l.activation.value===\"auto\"){let m=(I=Z(r))==null?void 0:I.activeElement,h=l.tabs.value.findIndex(O=>f(O)===m);h!==-1&&l.setSelectedIndex(h)}return c}function x(e){let c=l.tabs.value.map(m=>f(m)).filter(Boolean);if(e.key===S.Space||e.key===S.Enter){e.preventDefault(),e.stopPropagation(),l.setSelectedIndex(y.value);return}switch(e.key){case S.Home:case S.PageUp:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.First));case S.End:case S.PageDown:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.Last))}if(T(()=>H(l.orientation.value,{vertical(){return e.key===S.ArrowUp?D(c,g.Previous|g.WrapAround):e.key===S.ArrowDown?D(c,g.Next|g.WrapAround):B.Error},horizontal(){return e.key===S.ArrowLeft?D(c,g.Previous|g.WrapAround):e.key===S.ArrowRight?D(c,g.Next|g.WrapAround):B.Error}}))===B.Success)return e.preventDefault()}let w=P(!1);function E(){var e;w.value||(w.value=!0,!a.disabled&&((e=f(r))==null||e.focus({preventScroll:!0}),l.setSelectedIndex(y.value),Y(()=>{w.value=!1})))}function t(e){e.preventDefault()}let n=Q(v(()=>({as:a.as,type:b.type})),r);return()=>{var m,h;let e={selected:u.value,disabled:(m=a.disabled)!=null?m:!1},{...c}=a,I={ref:r,onKeydown:x,onMousedown:t,onClick:E,id:i,role:\"tab\",type:n.value,\"aria-controls\":(h=f(l.panels.value[y.value]))==null?void 0:h.id,\"aria-selected\":u.value,tabIndex:u.value?0:-1,disabled:a.disabled?!0:void 0};return M({ourProps:I,theirProps:c,slot:e,attrs:b,slots:s,name:\"Tab\"})}}}),Ie=L({name:\"TabPanels\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{slots:b,attrs:s}){let d=C(\"TabPanels\");return()=>{let i={selectedIndex:d.selectedIndex.value};return M({theirProps:a,ourProps:{},slot:i,attrs:s,slots:b,name:\"TabPanels\"})}}}),ye=L({name:\"TabPanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(a,{attrs:b,slots:s,expose:d}){var T;let i=(T=a.id)!=null?T:`headlessui-tabs-panel-${$()}`,l=C(\"TabPanel\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerPanel(r)),K(()=>l.unregisterPanel(r));let p=j(G),R=v(()=>{if(p.value){let x=p.value.panels.indexOf(i);return x===-1?p.value.panels.push(i)-1:x}return-1}),y=v(()=>{let x=l.panels.value.indexOf(r);return x===-1?R.value:x}),u=v(()=>y.value===l.selectedIndex.value);return()=>{var n;let x={selected:u.value},{tabIndex:w,...E}=a,t={ref:r,id:i,role:\"tabpanel\",\"aria-labelledby\":(n=f(l.tabs.value[y.value]))==null?void 0:n.id,tabIndex:u.value?w:-1};return!u.value&&a.unmount&&!a.static?A(X,{as:\"span\",\"aria-hidden\":!0,...t}):M({ourProps:t,theirProps:E,slot:x,attrs:b,slots:s,features:q.Static|q.RenderStrategy,visible:u.value,name:\"TabPanel\"})}}});export{xe as Tab,me as TabGroup,pe as TabList,ye as TabPanel,Ie as TabPanels};\n", "function l(r){let e={called:!1};return(...t)=>{if(!e.called)return e.called=!0,r(...t)}}export{l as once};\n", "import{disposables as p}from'../../../utils/disposables.js';import{once as f}from'../../../utils/once.js';function m(e,...t){e&&t.length>0&&e.classList.add(...t)}function d(e,...t){e&&t.length>0&&e.classList.remove(...t)}var g=(i=>(i.Finished=\"finished\",i.Cancelled=\"cancelled\",i))(g||{});function F(e,t){let i=p();if(!e)return i.dispose;let{transitionDuration:n,transitionDelay:a}=getComputedStyle(e),[l,s]=[n,a].map(o=>{let[u=0]=o.split(\",\").filter(Boolean).map(r=>r.includes(\"ms\")?parseFloat(r):parseFloat(r)*1e3).sort((r,c)=>c-r);return u});return l!==0?i.setTimeout(()=>t(\"finished\"),l+s):t(\"finished\"),i.add(()=>t(\"cancelled\")),i.dispose}function L(e,t,i,n,a,l){let s=p(),o=l!==void 0?f(l):()=>{};return d(e,...a),m(e,...t,...i),s.nextFrame(()=>{d(e,...i),m(e,...n),s.add(F(e,u=>(d(e,...n,...t),m(e,...a),o(u))))}),s.add(()=>d(e,...t,...i,...n,...a)),s.add(()=>o(\"cancelled\")),s.dispose}export{g as Reason,L as transition};\n", "import{computed as w,defineComponent as K,h as k,inject as F,normalizeClass as ae,onMounted as C,onUnmounted as z,provide as B,ref as m,watch as le,watchEffect as x}from\"vue\";import{useId as ie}from'../../hooks/use-id.js';import{hasOpenClosed as se,State as u,useOpenClosed as oe,useOpenClosedProvider as ue}from'../../internal/open-closed.js';import{dom as $}from'../../utils/dom.js';import{env as fe}from'../../utils/env.js';import{match as O}from'../../utils/match.js';import{Features as de,omit as ve,render as q,RenderStrategy as T}from'../../utils/render.js';import{Reason as G,transition as J}from'./utils/transition.js';function g(e=\"\"){return e.split(/\\s+/).filter(t=>t.length>1)}let R=Symbol(\"TransitionContext\");var pe=(a=>(a.Visible=\"visible\",a.Hidden=\"hidden\",a))(pe||{});function me(){return F(R,null)!==null}function Te(){let e=F(R,null);if(e===null)throw new Error(\"A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.\");return e}function ge(){let e=F(N,null);if(e===null)throw new Error(\"A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.\");return e}let N=Symbol(\"NestingContext\");function L(e){return\"children\"in e?L(e.children):e.value.filter(({state:t})=>t===\"visible\").length>0}function Q(e){let t=m([]),a=m(!1);C(()=>a.value=!0),z(()=>a.value=!1);function s(n,r=T.Hidden){let l=t.value.findIndex(({id:f})=>f===n);l!==-1&&(O(r,{[T.Unmount](){t.value.splice(l,1)},[T.Hidden](){t.value[l].state=\"hidden\"}}),!L(t)&&a.value&&(e==null||e()))}function h(n){let r=t.value.find(({id:l})=>l===n);return r?r.state!==\"visible\"&&(r.state=\"visible\"):t.value.push({id:n,state:\"visible\"}),()=>s(n,T.Unmount)}return{children:t,register:h,unregister:s}}let W=de.RenderStrategy,he=K({props:{as:{type:[Object,String],default:\"div\"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:\"\"},enterFrom:{type:[String],default:\"\"},enterTo:{type:[String],default:\"\"},entered:{type:[String],default:\"\"},leave:{type:[String],default:\"\"},leaveFrom:{type:[String],default:\"\"},leaveTo:{type:[String],default:\"\"}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:s,expose:h}){let n=m(0);function r(){n.value|=u.Opening,t(\"beforeEnter\")}function l(){n.value&=~u.Opening,t(\"afterEnter\")}function f(){n.value|=u.Closing,t(\"beforeLeave\")}function S(){n.value&=~u.Closing,t(\"afterLeave\")}if(!me()&&se())return()=>k(Se,{...e,onBeforeEnter:r,onAfterEnter:l,onBeforeLeave:f,onAfterLeave:S},s);let d=m(null),y=w(()=>e.unmount?T.Unmount:T.Hidden);h({el:d,$el:d});let{show:v,appear:A}=Te(),{register:D,unregister:H}=ge(),i=m(v.value?\"visible\":\"hidden\"),I={value:!0},c=ie(),b={value:!1},P=Q(()=>{!b.value&&i.value!==\"hidden\"&&(i.value=\"hidden\",H(c),S())});C(()=>{let o=D(c);z(o)}),x(()=>{if(y.value===T.Hidden&&c){if(v.value&&i.value!==\"visible\"){i.value=\"visible\";return}O(i.value,{[\"hidden\"]:()=>H(c),[\"visible\"]:()=>D(c)})}});let j=g(e.enter),M=g(e.enterFrom),X=g(e.enterTo),_=g(e.entered),Y=g(e.leave),Z=g(e.leaveFrom),ee=g(e.leaveTo);C(()=>{x(()=>{if(i.value===\"visible\"){let o=$(d);if(o instanceof Comment&&o.data===\"\")throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")}})});function te(o){let E=I.value&&!A.value,p=$(d);!p||!(p instanceof HTMLElement)||E||(b.value=!0,v.value&&r(),v.value||f(),o(v.value?J(p,j,M,X,_,V=>{b.value=!1,V===G.Finished&&l()}):J(p,Y,Z,ee,_,V=>{b.value=!1,V===G.Finished&&(L(P)||(i.value=\"hidden\",H(c),S()))})))}return C(()=>{le([v],(o,E,p)=>{te(p),I.value=!1},{immediate:!0})}),B(N,P),ue(w(()=>O(i.value,{[\"visible\"]:u.Open,[\"hidden\"]:u.Closed})|n.value)),()=>{let{appear:o,show:E,enter:p,enterFrom:V,enterTo:Ce,entered:ye,leave:be,leaveFrom:Ee,leaveTo:Ve,...U}=e,ne={ref:d},re={...U,...A.value&&v.value&&fe.isServer?{class:ae([a.class,U.class,...j,...M])}:{}};return q({theirProps:re,ourProps:ne,slot:{},slots:s,attrs:a,features:W,visible:i.value===\"visible\",name:\"TransitionChild\"})}}}),ce=he,Se=K({inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:\"\"},enterFrom:{type:[String],default:\"\"},enterTo:{type:[String],default:\"\"},entered:{type:[String],default:\"\"},leave:{type:[String],default:\"\"},leaveFrom:{type:[String],default:\"\"},leaveTo:{type:[String],default:\"\"}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:s}){let h=oe(),n=w(()=>e.show===null&&h!==null?(h.value&u.Open)===u.Open:e.show);x(()=>{if(![!0,!1].includes(n.value))throw new Error('A <Transition /> is used but it is missing a `:show=\"true | false\"` prop.')});let r=m(n.value?\"visible\":\"hidden\"),l=Q(()=>{r.value=\"hidden\"}),f=m(!0),S={show:n,appear:w(()=>e.appear||!f.value)};return C(()=>{x(()=>{f.value=!1,n.value?r.value=\"visible\":L(l)||(r.value=\"hidden\")})}),B(N,l),B(R,S),()=>{let d=ve(e,[\"show\",\"appear\",\"unmount\",\"onBeforeEnter\",\"onBeforeLeave\",\"onAfterEnter\",\"onAfterLeave\"]),y={unmount:e.unmount};return q({ourProps:{...y,as:\"template\"},theirProps:{},slot:{},slots:{...s,default:()=>[k(ce,{onBeforeEnter:()=>t(\"beforeEnter\"),onAfterEnter:()=>t(\"afterEnter\"),onBeforeLeave:()=>t(\"beforeLeave\"),onAfterLeave:()=>t(\"afterLeave\"),...a,...y,...d},s.default)]},attrs:{},features:W,visible:r.value===\"visible\",name:\"Transition\"})}}});export{he as TransitionChild,Se as TransitionRoot};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgB,SAAA,KACd,SACA,IACA,MAMA;AACI,MAAA,OAAO,KAAK,eAAe,CAAC;AAC5B,MAAA;AAEJ,WAAS,mBAA4B;AAbvB,QAAA,IAAA,IAAA,IAAA;AAcR,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,WAAU,KAAK,IAAI;AAEnD,UAAM,UAAU,QAAQ;AAExB,UAAM,cACJ,QAAQ,WAAW,KAAK,UACxB,QAAQ,KAAK,CAAC,KAAU,UAAkB,KAAK,KAAK,MAAM,GAAG;AAE/D,QAAI,CAAC,aAAa;AACT,aAAA;IAAA;AAGF,WAAA;AAEH,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,cAAa,KAAK,IAAI;AAE7C,aAAA,GAAG,GAAG,OAAO;AAEtB,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,IAAgB;AACxB,YAAA,aAAa,KAAK,OAAO,KAAK,IAAA,IAAQ,WAAY,GAAG,IAAI;AACzD,YAAA,gBAAgB,KAAK,OAAO,KAAK,IAAA,IAAQ,cAAe,GAAG,IAAI;AACrE,YAAM,sBAAsB,gBAAgB;AAEtC,YAAA,MAAM,CAAC,KAAsB,QAAgB;AACjD,cAAM,OAAO,GAAG;AACT,eAAA,IAAI,SAAS,KAAK;AACvB,gBAAM,MAAM;QAAA;AAEP,eAAA;MACT;AAEQ,cAAA;QACN,OAAO,IAAI,eAAe,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC;QACnD;;;yBAGiB,KAAK;UAChB;UACA,KAAK,IAAI,MAAM,MAAM,qBAAqB,GAAG;QAC9C,CAAA;QACL,QAAA,OAAA,SAAA,KAAM;MACR;IAAA;AAGF,KAAA,KAAA,QAAA,OAAA,SAAA,KAAM,aAAN,OAAA,SAAA,GAAA,KAAA,MAAiB,MAAA;AAEV,WAAA;EAAA;AAIQ,mBAAA,aAAa,CAAC,YAAwB;AAC9C,WAAA;EACT;AAEO,SAAA;AACT;AAEgB,SAAA,aAAgB,OAAsB,KAAiB;AACrE,MAAI,UAAU,QAAW;AACjB,UAAA,IAAI,MAAM,uBAAuB,MAAM,KAAK,GAAG,KAAK,EAAE,EAAE;EAAA,OACzD;AACE,WAAA;EAAA;AAEX;AAEa,IAAA,cAAc,CAACA,IAAWC,OAAc,KAAK,IAAID,KAAIC,EAAC,IAAI;AAEhE,IAAM,WAAW,CACtB,cACA,IACA,OACG;AACC,MAAA;AACJ,SAAO,YAAwB,MAAkB;AAC/C,iBAAa,aAAa,SAAS;AACvB,gBAAA,aAAa,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE;EACpE;AACF;;;ACnDA,IAAM,UAAU,CAAC,YAA+B;AACxC,QAAA,EAAE,aAAa,aAAA,IAAiB;AACtC,SAAO,EAAE,OAAO,aAAa,QAAQ,aAAa;AACpD;AAEa,IAAA,sBAAsB,CAAC,UAAkB;AAEzC,IAAA,wBAAwB,CAAC,UAAiB;AACrD,QAAM,QAAQ,KAAK,IAAI,MAAM,aAAa,MAAM,UAAU,CAAC;AACrD,QAAA,MAAM,KAAK,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ,CAAC;AAErE,QAAM,MAAM,CAAC;AAEb,WAASC,KAAI,OAAOA,MAAK,KAAKA,MAAK;AACjC,QAAI,KAAKA,EAAC;EAAA;AAGL,SAAA;AACT;AAEa,IAAA,qBAAqB,CAChC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGI,QAAA,UAAU,CAAC,SAAe;AACxB,UAAA,EAAE,OAAO,OAAA,IAAW;AACvB,OAAA,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,QAAQ,KAAK,MAAM,MAAM,EAAA,CAAG;EAC7D;AAEQ,UAAA,QAAQ,OAAiC,CAAC;AAE9C,MAAA,CAAC,aAAa,gBAAgB;AAChC,WAAO,MAAM;IAAC;EAAA;AAGhB,QAAM,WAAW,IAAI,aAAa,eAAe,CAAC,YAAY;AAC5D,UAAM,MAAM,MAAM;AACV,YAAA,QAAQ,QAAQ,CAAC;AACvB,UAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,cAAA,MAAM,MAAM,cAAc,CAAC;AACjC,YAAI,KAAK;AACP,kBAAQ,EAAE,OAAO,IAAI,YAAY,QAAQ,IAAI,UAAA,CAAW;AACxD;QAAA;MACF;AAEM,cAAA,QAAQ,OAAiC,CAAC;IACpD;AAEA,aAAS,QAAQ,sCACb,sBAAsB,GAAG,IACzB,IAAI;EAAA,CACT;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAA,CAAc;AAE/C,SAAO,MAAM;AACX,aAAS,UAAU,OAAO;EAC5B;AACF;AAEA,IAAM,0BAA0B;EAC9B,SAAS;AACX;AAuBA,IAAM,oBACJ,OAAO,UAAU,cAAc,OAAO,iBAAiB;AAI5C,IAAA,uBAAuB,CAClC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGF,MAAI,SAAS;AACb,QAAM,WACJ,SAAS,QAAQ,qBAAqB,oBAClC,MAAM,SACN;IACE;IACA,MAAM;AACJ,SAAG,QAAQ,KAAK;IAClB;IACA,SAAS,QAAQ;EACnB;AAEA,QAAA,gBAAgB,CAAC,gBAAyB,MAAM;AACpD,UAAM,EAAE,YAAY,MAAM,IAAI,SAAS;AAC9B,aAAA,aACL,QAAQ,YAAY,KAAM,SAAS,MAAO,KAC1C,QAAQ,WAAW;AACd,aAAA;AACT,OAAG,QAAQ,WAAW;EACxB;AACM,QAAA,UAAU,cAAc,IAAI;AAC5B,QAAA,aAAa,cAAc,KAAK;AAC3B,aAAA;AAEH,UAAA,iBAAiB,UAAU,SAAS,uBAAuB;AAC7D,QAAA,yBACJ,SAAS,QAAQ,qBAAqB;AACxC,MAAI,wBAAwB;AAClB,YAAA,iBAAiB,aAAa,YAAY,uBAAuB;EAAA;AAE3E,SAAO,MAAM;AACH,YAAA,oBAAoB,UAAU,OAAO;AAC7C,QAAI,wBAAwB;AAClB,cAAA,oBAAoB,aAAa,UAAU;IAAA;EAEvD;AACF;AAkDO,IAAM,iBAAiB,CAC5B,SACA,OACA,aACG;AACH,MAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,UAAA,MAAM,MAAM,cAAc,CAAC;AACjC,QAAI,KAAK;AACP,YAAM,OAAO,KAAK;QAChB,IAAI,SAAS,QAAQ,aAAa,eAAe,WAAW;MAC9D;AACO,aAAA;IAAA;EACT;AAGF,SAAQ,QACN,SAAS,QAAQ,aAAa,gBAAgB,cAChD;AACF;AAkBa,IAAA,gBAAgB,CAC3B,QACA;EACE,cAAc;EACd;AACF,GACA,aACG;;AACH,QAAM,WAAW,SAAS;AAE1B,GAAA,MAAA,KAAA,SAAS,kBAAT,OAAA,SAAA,GAAwB,aAAxB,OAAA,SAAA,GAAA,KAAA,IAAmC;IACjC,CAAC,SAAS,QAAQ,aAAa,SAAS,KAAK,GAAG;IAChD;EAAA,CAAA;AAEJ;AA0DO,IAAM,cAAN,MAGL;EAyDA,YAAY,MAAwD;AAxDpE,SAAQ,SAAqC,CAAC;AAEP,SAAA,gBAAA;AACa,SAAA,eAAA;AACtC,SAAA,cAAA;AACd,SAAA,oBAAwC,CAAC;AACjC,SAAA,gBAAA,oBAAoB,IAAiB;AAC7C,SAAQ,8BAA6C,CAAC;AAC5B,SAAA,aAAA;AACI,SAAA,eAAA;AACY,SAAA,kBAAA;AAC1C,SAAQ,oBAAoB;AAQ5B,SAAA,gBAAA,oBAAoB,IAAuB;AAC3C,SAAQ,WAAkB,uBAAA;AACxB,UAAI,MAA6B;AAEjC,YAAM,MAAM,MAAM;AAChB,YAAI,KAAK;AACA,iBAAA;QAAA;AAGT,YAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,gBAAgB;AACpD,iBAAA;QAAA;AAGT,eAAQ,MAAM,IAAI,KAAK,aAAa,eAAe,CAAC,YAAY;AACtD,kBAAA,QAAQ,CAAC,UAAU;AACzB,kBAAM,MAAM,MAAM;AACX,mBAAA,gBAAgB,MAAM,QAAwB,KAAK;YAC1D;AACA,iBAAK,QAAQ,sCACT,sBAAsB,GAAG,IACzB,IAAI;UAAA,CACT;QAAA,CACF;MACH;AAEO,aAAA;QACL,YAAY,MAAM;;AAChB,WAAA,KAAA,IAAA,MAAA,OAAA,SAAA,GAAO,WAAA;AACD,gBAAA;QACR;QACA,SAAS,CAAC,WAAA;;AACR,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,QAAQ,QAAQ,EAAE,KAAK,aAAA,CAAA;;QAChC,WAAW,CAAC,WAAA;;AAAoB,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,UAAU,MAAA;QAAA;MACnD;IAAA,GACC;AACsD,SAAA,QAAA;AAMzD,SAAA,aAAa,CAACC,UAA2D;AAChE,aAAA,QAAQA,KAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAI,OAAO,UAAU,YAAa,QAAQA,MAAa,GAAG;MAAA,CAC3D;AAED,WAAK,UAAU;QACb,OAAO;QACP,eAAe;QACf,UAAU;QACV,cAAc;QACd,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU,MAAM;QAAC;QACjB;QACA,aAAa,EAAE,OAAO,GAAG,QAAQ,EAAE;QACnC,cAAc;QACd,KAAK;QACL,gBAAgB;QAChB,0BAA0B,CAAC;QAC3B,OAAO;QACP,uBAAuB;QACvB,SAAS;QACT,OAAO;QACP,mBAAmB;QACnB,qCAAqC;QACrC,GAAGA;MACL;IACF;AAEQ,SAAA,SAAS,CAAC,SAAkB;;AAC7B,OAAA,MAAA,KAAA,KAAA,SAAQ,aAAR,OAAA,SAAA,GAAA,KAAA,IAAmB,MAAM,IAAA;IAChC;AAEA,SAAQ,cAAc;MACpB,MAAM;AACJ,aAAK,eAAe;AAEb,eAAA;UACL,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QACrC;MACF;MACA,CAAC,gBAAgB;AACf,aAAK,OAAO,WAAW;MACzB;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;QAC1B,aAAa;UACX,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QAAA;MACrC;IAEJ;AAEA,SAAQ,UAAU,MAAM;AACjB,WAAA,OAAO,OAAO,OAAO,EAAE,QAAQ,CAACC,OAAMA,GAAA,CAAI;AAC/C,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,WAAW;AACzB,WAAK,gBAAgB;AACrB,WAAK,eAAe;IACtB;AAEA,SAAA,YAAY,MAAM;AAChB,aAAO,MAAM;AACX,aAAK,QAAQ;MACf;IACF;AAEA,SAAA,cAAc,MAAM;;AAClB,YAAM,gBAAgB,KAAK,QAAQ,UAC/B,KAAK,QAAQ,iBAAA,IACb;AAEA,UAAA,KAAK,kBAAkB,eAAe;AACxC,aAAK,QAAQ;AAEb,YAAI,CAAC,eAAe;AAClB,eAAK,YAAY;AACjB;QAAA;AAGF,aAAK,gBAAgB;AAErB,YAAI,KAAK,iBAAiB,mBAAmB,KAAK,eAAe;AAC1D,eAAA,eAAe,KAAK,cAAc,cAAc;QAAA,OAChD;AACA,eAAA,iBAAe,KAAA,KAAK,kBAAL,OAAA,SAAA,GAAoB,WAAU;QAAA;AAG/C,aAAA,cAAc,QAAQ,CAAC,WAAW;AAChC,eAAA,SAAS,QAAQ,MAAM;QAAA,CAC7B;AAEI,aAAA,gBAAgB,KAAK,gBAAA,GAAmB;UAC3C,aAAa;UACb,UAAU;QAAA,CACX;AAED,aAAK,OAAO;UACV,KAAK,QAAQ,mBAAmB,MAAM,CAAC,SAAS;AAC9C,iBAAK,aAAa;AAClB,iBAAK,YAAY;UAClB,CAAA;QACH;AAEA,aAAK,OAAO;UACV,KAAK,QAAQ,qBAAqB,MAAM,CAAC,QAAQ,gBAAgB;AAC/D,iBAAK,oBAAoB;AACzB,iBAAK,kBAAkB,cACnB,KAAK,gBAAA,IAAoB,SACvB,YACA,aACF;AACJ,iBAAK,eAAe;AACpB,iBAAK,cAAc;AAEnB,iBAAK,YAAY;UAClB,CAAA;QACH;MAAA;IAEJ;AAEA,SAAQ,UAAU,MAAM;AAClB,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,aAAa;AACX,eAAA;MAAA;AAGT,WAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAElD,aAAO,KAAK,WAAW,KAAK,QAAQ,aAAa,UAAU,QAAQ;IACrE;AAEA,SAAQ,kBAAkB,MAAM;AAC1B,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,eAAe;AACb,eAAA;MAAA;AAGT,WAAK,eACH,KAAK,iBACJ,OAAO,KAAK,QAAQ,kBAAkB,aACnC,KAAK,QAAQ,cAAc,IAC3B,KAAK,QAAQ;AAEnB,aAAO,KAAK;IACd;AAEQ,SAAA,yBAAyB,CAC/B,cACA,UACG;AACG,YAAA,4BAAA,oBAAgC,IAAkB;AAClD,YAAA,uBAAA,oBAA2B,IAAyB;AAC1D,eAASC,KAAI,QAAQ,GAAGA,MAAK,GAAGA,MAAK;AAC7B,cAAA,cAAc,aAAaA,EAAC;AAElC,YAAI,0BAA0B,IAAI,YAAY,IAAI,GAAG;AACnD;QAAA;AAGF,cAAM,8BAA8B,qBAAqB;UACvD,YAAY;QACd;AACA,YACE,+BAA+B,QAC/B,YAAY,MAAM,4BAA4B,KAC9C;AACqB,+BAAA,IAAI,YAAY,MAAM,WAAW;QAC7C,WAAA,YAAY,MAAM,4BAA4B,KAAK;AAClC,oCAAA,IAAI,YAAY,MAAM,IAAI;QAAA;AAGtD,YAAI,0BAA0B,SAAS,KAAK,QAAQ,OAAO;AACzD;QAAA;MACF;AAGF,aAAO,qBAAqB,SAAS,KAAK,QAAQ,QAC9C,MAAM,KAAK,qBAAqB,OAAA,CAAQ,EAAE,KAAK,CAACC,IAAGC,OAAM;AACnD,YAAAD,GAAE,QAAQC,GAAE,KAAK;AACZ,iBAAAD,GAAE,QAAQC,GAAE;QAAA;AAGd,eAAAD,GAAE,MAAMC,GAAE;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;AAEA,SAAQ,wBAAwB;MAC9B,MAAM;QACJ,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;MACf;MACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;AAC1D,aAAK,8BAA8B,CAAC;AAC7B,eAAA;UACL;UACA;UACA;UACA;UACA;QACF;MACF;MACA;QACE,KAAK;MAAA;IAET;AAEA,SAAQ,kBAAkB;MACxB,MAAM,CAAC,KAAK,sBAAA,GAAyB,KAAK,aAAa;MACvD,CACE,EAAE,OAAO,cAAc,cAAc,YAAY,QAAA,GACjD,kBACG;AACH,YAAI,CAAC,SAAS;AACZ,eAAK,oBAAoB,CAAC;AAC1B,eAAK,cAAc,MAAM;AACzB,iBAAO,CAAC;QAAA;AAGN,YAAA,KAAK,kBAAkB,WAAW,GAAG;AAClC,eAAA,oBAAoB,KAAK,QAAQ;AACjC,eAAA,kBAAkB,QAAQ,CAAC,SAAS;AACvC,iBAAK,cAAc,IAAI,KAAK,KAAK,KAAK,IAAI;UAAA,CAC3C;QAAA;AAGG,cAAA,MACJ,KAAK,4BAA4B,SAAS,IACtC,KAAK,IAAI,GAAG,KAAK,2BAA2B,IAC5C;AACN,aAAK,8BAA8B,CAAC;AAEpC,cAAM,eAAe,KAAK,kBAAkB,MAAM,GAAG,GAAG;AAExD,iBAASC,KAAI,KAAKA,KAAI,OAAOA,MAAK;AAC1B,gBAAA,MAAM,WAAWA,EAAC;AAExB,gBAAM,sBACJ,KAAK,QAAQ,UAAU,IACnB,aAAaA,KAAI,CAAC,IAClB,KAAK,uBAAuB,cAAcA,EAAC;AAEjD,gBAAM,QAAQ,sBACV,oBAAoB,MAAM,KAAK,QAAQ,MACvC,eAAe;AAEb,gBAAA,eAAe,cAAc,IAAI,GAAG;AACpC,gBAAA,OACJ,OAAO,iBAAiB,WACpB,eACA,KAAK,QAAQ,aAAaA,EAAC;AAEjC,gBAAM,MAAM,QAAQ;AAEpB,gBAAM,OAAO,sBACT,oBAAoB,OACpBA,KAAI,KAAK,QAAQ;AAErB,uBAAaA,EAAC,IAAI;YAChB,OAAOA;YACP;YACA;YACA;YACA;YACA;UACF;QAAA;AAGF,aAAK,oBAAoB;AAElB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEiB,SAAA,iBAAA;MACf,MAAM;QACJ,KAAK,gBAAgB;QACrB,KAAK,QAAQ;QACb,KAAK,gBAAgB;QACrB,KAAK,QAAQ;MACf;MACA,CAAC,cAAc,WAAW,cAAc,UAAU;AAChD,eAAQ,KAAK,QACX,aAAa,SAAS,KAAK,YAAY,IACnC,eAAe;UACb;UACA;UACA;UACA;QACD,CAAA,IACD;MACR;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEoB,SAAA,oBAAA;MAClB,MAAM;AACJ,YAAI,aAA4B;AAChC,YAAI,WAA0B;AACxB,cAAA,QAAQ,KAAK,eAAe;AAClC,YAAI,OAAO;AACT,uBAAa,MAAM;AACnB,qBAAW,MAAM;QAAA;AAEnB,aAAK,YAAY,WAAW,CAAC,KAAK,aAAa,YAAY,QAAQ,CAAC;AAC7D,eAAA;UACL,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb;UACA;QACF;MACF;MACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;AACzD,eAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,IACA,eAAe;UACb;UACA;UACA;UACA;QAAA,CACD;MACP;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,mBAAmB,CAAC,SAAuB;AACnC,YAAA,gBAAgB,KAAK,QAAQ;AAC7B,YAAA,WAAW,KAAK,aAAa,aAAa;AAEhD,UAAI,CAAC,UAAU;AACL,gBAAA;UACN,2BAA2B,aAAa;QAC1C;AACO,eAAA;MAAA;AAGF,aAAA,SAAS,UAAU,EAAE;IAC9B;AAEQ,SAAA,kBAAkB,CACxB,MACA,UACG;AACG,YAAA,QAAQ,KAAK,iBAAiB,IAAI;AAClC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,KAAK,cAAc,IAAI,GAAG;AAE3C,UAAI,aAAa,MAAM;AACrB,YAAI,UAAU;AACP,eAAA,SAAS,UAAU,QAAQ;QAAA;AAE7B,aAAA,SAAS,QAAQ,IAAI;AACrB,aAAA,cAAc,IAAI,KAAK,IAAI;MAAA;AAGlC,UAAI,KAAK,aAAa;AACf,aAAA,WAAW,OAAO,KAAK,QAAQ,eAAe,MAAM,OAAO,IAAI,CAAC;MAAA;IAEzE;AAEa,SAAA,aAAA,CAAC,OAAe,SAAiB;AACtC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,WAAW,KAAK,cAAc,IAAI,KAAK,GAAG,KAAK,KAAK;AAC1D,YAAM,QAAQ,OAAO;AAErB,UAAI,UAAU,GAAG;AACf,YACE,KAAK,+CAA+C,SAChD,KAAK,2CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,QAAQ,KAAK,gBAAgB,IAAI,KAAK,mBAC/C;AACA,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,cAAc,KAAK;UAAA;AAG7B,eAAA,gBAAgB,KAAK,gBAAA,GAAmB;YAC3C,aAAc,KAAK,qBAAqB;YACxC,UAAU;UAAA,CACX;QAAA;AAGE,aAAA,4BAA4B,KAAK,KAAK,KAAK;AAC3C,aAAA,gBAAgB,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AAEnE,aAAK,OAAO,KAAK;MAAA;IAErB;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,UAAI,CAAC,MAAM;AACT,aAAK,cAAc,QAAQ,CAAC,QAAQ,QAAQ;AACtC,cAAA,CAAC,OAAO,aAAa;AAClB,iBAAA,SAAS,UAAU,MAAM;AACzB,iBAAA,cAAc,OAAO,GAAG;UAAA;QAC/B,CACD;AACD;MAAA;AAGG,WAAA,gBAAgB,MAAM,MAAS;IACtC;AAEkB,SAAA,kBAAA;MAChB,MAAM,CAAC,KAAK,kBAAqB,GAAA,KAAK,gBAAA,CAAiB;MACvD,CAAC,SAAS,iBAAiB;AACzB,cAAM,eAAmC,CAAC;AAE1C,iBAASC,KAAI,GAAG,MAAM,QAAQ,QAAQA,KAAI,KAAKA,MAAK;AAC5C,gBAAAD,KAAI,QAAQC,EAAC;AACb,gBAAA,cAAc,aAAaD,EAAC;AAElC,uBAAa,KAAK,WAAW;QAAA;AAGxB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,0BAA0B,CAAC,WAAmB;AACtC,YAAA,eAAe,KAAK,gBAAgB;AACtC,UAAA,aAAa,WAAW,GAAG;AACtB,eAAA;MAAA;AAEF,aAAA;QACL,aACE;UACE;UACA,aAAa,SAAS;UACtB,CAAC,UAAkB,aAAa,aAAa,KAAK,CAAC,EAAE;UACrD;QAEJ,CAAA;MACF;IACF;AAEA,SAAA,wBAAwB,CACtB,UACA,OACA,WAAW,MACR;AACG,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACZ,gBAAA,YAAY,eAAe,OAAO,QAAQ;MAAA;AAGpD,UAAI,UAAU,UAAU;AAGtB,qBAAa,WAAW,QAAQ;MAAA,WACvB,UAAU,OAAO;AACd,oBAAA;MAAA;AAGd,YAAM,YAAY,KAAK,aAAA,IAAiB,KAAK,QAAQ,eAAe;AAEpE,aAAO,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,GAAG,CAAC;IAClD;AAEoB,SAAA,oBAAA,CAAC,OAAe,QAAyB,WAAW;AAC9D,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAErD,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACF,eAAA;MAAA;AAGH,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACpB,YAAI,KAAK,OAAO,eAAe,OAAO,KAAK,QAAQ,kBAAkB;AAC3D,kBAAA;QAAA,WACC,KAAK,SAAS,eAAe,KAAK,QAAQ,oBAAoB;AAC/D,kBAAA;QAAA,OACH;AACE,iBAAA,CAAC,cAAc,KAAK;QAAA;MAC7B;AAGI,YAAA,WACJ,UAAU,QACN,KAAK,MAAM,KAAK,QAAQ,mBACxB,KAAK,QAAQ,KAAK,QAAQ;AAEzB,aAAA;QACL,KAAK,sBAAsB,UAAU,OAAO,KAAK,IAAI;QACrD;MACF;IACF;AAEA,SAAQ,gBAAgB,MAAM,KAAK,cAAc,OAAO;AAEvC,SAAA,iBAAA,CACf,UACA,EAAE,QAAQ,SAAS,SAAS,IAA2B,CAAA,MACpD;AACH,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,sBAAsB,UAAU,KAAK,GAAG;QAChE,aAAa;QACb;MAAA,CACD;IACH;AAEgB,SAAA,gBAAA,CACd,OACA,EAAE,OAAO,eAAe,QAAQ,SAAmC,IAAA,CAAA,MAChE;AACH,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGM,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAE3D,UAAI,WAAW;AACf,YAAM,cAAc;AAEd,YAAA,YAAY,CAAC,iBAAkC;AAC/C,YAAA,CAAC,KAAK,aAAc;AAExB,cAAM,aAAa,KAAK,kBAAkB,OAAO,YAAY;AAC7D,YAAI,CAAC,YAAY;AACP,kBAAA,KAAK,mCAAmC,KAAK;AACrD;QAAA;AAEI,cAAA,CAAC,QAAQ,KAAK,IAAI;AACxB,aAAK,gBAAgB,QAAQ,EAAE,aAAa,QAAW,SAAA,CAAU;AAE5D,aAAA,aAAa,sBAAsB,MAAM;AACtC,gBAAA,gBAAgB,KAAK,gBAAgB;AAC3C,gBAAM,YAAY,KAAK,kBAAkB,OAAO,KAAK;AACrD,cAAI,CAAC,WAAW;AACN,oBAAA,KAAK,mCAAmC,KAAK;AACrD;UAAA;AAGF,cAAI,CAAC,YAAY,UAAU,CAAC,GAAG,aAAa,GAAG;AAC7C,0BAAc,KAAK;UAAA;QACrB,CACD;MACH;AAEM,YAAA,gBAAgB,CAAC,UAA2B;AAC5C,YAAA,CAAC,KAAK,aAAc;AAExB;AACA,YAAI,WAAW,aAAa;AAC1B,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,kBAAkB,UAAU,WAAW;UAAA;AAEtD,eAAK,aAAa,sBAAsB,MAAM,UAAU,KAAK,CAAC;QAAA,OACzD;AACG,kBAAA;YACN,6BAA6B,KAAK,UAAU,WAAW;UACzD;QAAA;MAEJ;AAEA,gBAAU,YAAY;IACxB;AAEA,SAAA,WAAW,CAAC,OAAe,EAAE,SAAS,IAA2B,CAAA,MAAO;AACtE,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;QACnD,aAAa;QACb;MAAA,CACD;IACH;AAEA,SAAA,eAAe,MAAM;;AACb,YAAA,eAAe,KAAK,gBAAgB;AAEtC,UAAA;AAIA,UAAA,aAAa,WAAW,GAAG;AAC7B,cAAM,KAAK,QAAQ;MACV,WAAA,KAAK,QAAQ,UAAU,GAAG;AACnC,gBAAM,KAAA,aAAa,aAAa,SAAS,CAAC,MAApC,OAAA,SAAA,GAAuC,QAAO;MAAA,OAC/C;AACL,cAAM,YAAY,MAAqB,KAAK,QAAQ,KAAK,EAAE,KAAK,IAAI;AAChE,YAAA,WAAW,aAAa,SAAS;AAC9B,eAAA,YAAY,KAAK,UAAU,KAAK,CAAC,QAAQ,QAAQ,IAAI,GAAG;AACvD,gBAAA,OAAO,aAAa,QAAQ;AAClC,cAAI,UAAU,KAAK,IAAI,MAAM,MAAM;AACvB,sBAAA,KAAK,IAAI,IAAI,KAAK;UAAA;AAG9B;QAAA;AAGI,cAAA,KAAK,IAAI,GAAG,UAAU,OAAO,CAAC,QAAuB,QAAQ,IAAI,CAAC;MAAA;AAG1E,aAAO,KAAK;QACV,MAAM,KAAK,QAAQ,eAAe,KAAK,QAAQ;QAC/C;MACF;IACF;AAEQ,SAAA,kBAAkB,CACxB,QACA;MACE;MACA;IAAA,MAKC;AACH,WAAK,QAAQ,WAAW,QAAQ,EAAE,UAAU,YAAA,GAAe,IAAI;IACjE;AAEA,SAAA,UAAU,MAAM;AACT,WAAA,gBAAA,oBAAoB,IAAI;AAC7B,WAAK,OAAO,KAAK;IACnB;AA3pBE,SAAK,WAAW,IAAI;EAAA;AA4pBxB;AAEA,IAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;AACH,SAAO,OAAO,MAAM;AACZ,UAAA,UAAW,MAAM,QAAQ,IAAK;AAC9B,UAAA,eAAe,gBAAgB,MAAM;AAE3C,QAAI,eAAe,OAAO;AACxB,YAAM,SAAS;IAAA,WACN,eAAe,OAAO;AAC/B,aAAO,SAAS;IAAA,OACX;AACE,aAAA;IAAA;EACT;AAGF,MAAI,MAAM,GAAG;AACX,WAAO,MAAM;EAAA,OACR;AACE,WAAA;EAAA;AAEX;AAEA,SAAS,eAAe;EACtB;EACA;EACA;EACA;AACF,GAKG;AACK,QAAA,YAAY,aAAa,SAAS;AACxC,QAAM,YAAY,CAAC,UAAkB,aAAa,KAAK,EAAG;AAGtD,MAAA,aAAa,UAAU,OAAO;AACzB,WAAA;MACL,YAAY;MACZ,UAAU;IACZ;EAAA;AAGF,MAAI,aAAa;IACf;IACA;IACA;IACA;EACF;AACA,MAAI,WAAW;AAEf,MAAI,UAAU,GAAG;AACf,WACE,WAAW,aACX,aAAa,QAAQ,EAAG,MAAM,eAAe,WAC7C;AACA;IAAA;EACF,WACS,QAAQ,GAAG;AAGpB,UAAM,aAAa,MAAM,KAAK,EAAE,KAAK,CAAC;AAEpC,WAAA,WAAW,aACX,WAAW,KAAK,CAAC,QAAQ,MAAM,eAAe,SAAS,GACvD;AACM,YAAA,OAAO,aAAa,QAAQ;AACvB,iBAAA,KAAK,IAAI,IAAI,KAAK;AAC7B;IAAA;AAKF,UAAM,eAAe,MAAM,KAAK,EAAE,KAAK,eAAe,SAAS;AACxD,WAAA,cAAc,KAAK,aAAa,KAAK,CAAC,QAAQ,OAAO,YAAY,GAAG;AACnE,YAAA,OAAO,aAAa,UAAU;AACvB,mBAAA,KAAK,IAAI,IAAI,KAAK;AAC/B;IAAA;AAIF,iBAAa,KAAK,IAAI,GAAG,aAAc,aAAa,KAAM;AAE1D,eAAW,KAAK,IAAI,WAAW,YAAY,QAAQ,IAAK,WAAW,MAAO;EAAA;AAGrE,SAAA,EAAE,YAAY,SAAS;AAChC;;;AC/nCA,SAAS,mBAIP,SACgD;AAChD,QAAM,cAAc,IAAI,YAAY,MAAM,OAAO,CAAC;AAC5C,QAAA,QAAQ,WAAW,WAAW;AAE9B,QAAA,UAAU,YAAY,UAAU;AAEtC;IACE,MAAM,MAAM,OAAO,EAAE,iBAAiB;IACtC,CAAC,OAAO;AACN,UAAI,IAAI;AACN,oBAAY,YAAY;MAAA;IAE5B;IACA;MACE,WAAW;IAAA;EAEf;AAEA;IACE,MAAM,MAAM,OAAO;IACnB,CAACE,aAAY;AACX,kBAAY,WAAW;QACrB,GAAGA;QACH,UAAU,CAAC,UAAU,SAAS;;AAC5B,qBAAW,KAAK;AAChBA,WAAAA,KAAAA,SAAQ,aAARA,OAAAA,SAAAA,GAAAA,KAAAA,UAAmB,UAAU,IAAA;QAAI;MACnC,CACD;AAED,kBAAY,YAAY;AACxB,iBAAW,KAAK;IAClB;IACA;MACE,WAAW;IAAA;EAEf;AAEA,iBAAe,OAAO;AAEf,SAAA;AACT;AAEO,SAAS,eAId,SAMgD;AACzC,SAAA;IACL,SAAS,OAAO;MACd;MACA;MACA,YAAY;MACZ,GAAG,MAAM,OAAO;IAAA,EAChB;EACJ;AACF;;;AC1FwC,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAED,MAAG,OAAK,SAAOA,GAAE,KAAK,GAAEE,KAAE,SAAE,MAAIJ,GAAE,UAAQ,MAAM;AAAE,SAAM,CAAC,SAAE,MAAII,GAAE,QAAMJ,GAAE,QAAMG,GAAE,KAAK,GAAE,SAASE,IAAE;AAAC,WAAOD,GAAE,UAAQD,GAAE,QAAME,KAAGJ,MAAG,OAAK,SAAOA,GAAEI,EAAC;AAAA,EAAC,CAAC;AAAC;;;ACAvN,SAAS,EAAEC,IAAE;AAAC,SAAO,kBAAgB,aAAW,eAAeA,EAAC,IAAE,QAAQ,QAAQ,EAAE,KAAKA,EAAC,EAAE,MAAM,CAAAC,OAAG,WAAW,MAAI;AAAC,UAAMA;AAAA,EAAC,CAAC,CAAC;AAAC;;;ACAnF,SAAS,IAAG;AAAC,MAAIC,KAAE,CAAC,GAAEC,KAAE,EAAC,iBAAiBC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAOH,GAAE,iBAAiBC,IAAEC,IAAEC,EAAC,GAAEJ,GAAE,IAAI,MAAIC,GAAE,oBAAoBC,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,GAAE,yBAAyBH,IAAE;AAAC,QAAIC,KAAE,sBAAsB,GAAGD,EAAC;AAAE,IAAAD,GAAE,IAAI,MAAI,qBAAqBE,EAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,IAAAD,GAAE,sBAAsB,MAAI;AAAC,MAAAA,GAAE,sBAAsB,GAAGC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,cAAcA,IAAE;AAAC,QAAIC,KAAE,WAAW,GAAGD,EAAC;AAAE,IAAAD,GAAE,IAAI,MAAI,aAAaE,EAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,QAAIC,KAAE,EAAC,SAAQ,KAAE;AAAE,WAAO,EAAE,MAAI;AAAC,MAAAA,GAAE,WAASD,GAAE,CAAC,EAAE;AAAA,IAAC,CAAC,GAAED,GAAE,IAAI,MAAI;AAAC,MAAAE,GAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAC,GAAE,MAAMD,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAEH,GAAE,MAAM,iBAAiBC,EAAC;AAAE,WAAO,OAAO,OAAOD,GAAE,OAAM,EAAC,CAACC,EAAC,GAAEC,GAAC,CAAC,GAAE,KAAK,IAAI,MAAI;AAAC,aAAO,OAAOF,GAAE,OAAM,EAAC,CAACC,EAAC,GAAEE,GAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,MAAMH,IAAE;AAAC,QAAIC,KAAE,EAAE;AAAE,WAAOD,GAAEC,EAAC,GAAE,KAAK,IAAI,MAAIA,GAAE,QAAQ,CAAC;AAAA,EAAC,GAAE,IAAID,IAAE;AAAC,WAAOF,GAAE,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,KAAEH,GAAE,QAAQE,EAAC;AAAE,UAAGC,MAAG,EAAE,UAAQC,MAAKJ,GAAE,OAAOG,IAAE,CAAC,EAAE,CAAAC,GAAE;AAAA,IAAC;AAAA,EAAC,GAAE,UAAS;AAAC,aAAQF,MAAKF,GAAE,OAAO,CAAC,EAAE,CAAAE,GAAE;AAAA,EAAC,EAAC;AAAE,SAAOD;AAAC;;;ACA9uB,SAAS,IAAG;AAAC,MAAIK,KAAE,EAAE;AAAE,SAAO,YAAE,MAAIA,GAAE,QAAQ,CAAC,GAAEA;AAAC;;;ACApF,SAASC,KAAG;AAAC,MAAIC,KAAE,EAAE;AAAE,SAAO,CAAAC,OAAG;AAAC,IAAAD,GAAE,QAAQ,GAAEA,GAAE,UAAUC,EAAC;AAAA,EAAC;AAAC;;;ACAnH,IAAI;AAAwB,IAAI,IAAE,OAAO,kBAAkB;AAA/B,IAAiCC,KAAE;AAAE,IAAMC,MAAG,IAAI,UAAQ,OAAK,IAAE,WAAU;AAAC,SAAS,OAAO,GAAE,MAAI,GAAG,EAAED,EAAC,EAAE,EAAE;AAAC;AAAE,SAAS,EAAEE,IAAE;AAAC,EAAE,QAAQ,GAAEA,EAAC;AAAC;;;ACAvK,SAASC,GAAEC,IAAE;AAAC,MAAIC;AAAE,MAAGD,MAAG,QAAMA,GAAE,SAAO,KAAK,QAAO;AAAK,MAAIE,MAAGD,KAAED,GAAE,MAAM,QAAM,OAAKC,KAAED,GAAE;AAAM,SAAOE,cAAa,OAAKA,KAAE;AAAI;;;ACA/H,SAAS,EAAEC,IAAEC,OAAKC,IAAE;AAAC,MAAGF,MAAKC,IAAE;AAAC,QAAIE,KAAEF,GAAED,EAAC;AAAE,WAAO,OAAOG,MAAG,aAAWA,GAAE,GAAGD,EAAC,IAAEC;AAAA,EAAC;AAAC,MAAIC,KAAE,IAAI,MAAM,oBAAoBJ,EAAC,iEAAiE,OAAO,KAAKC,EAAC,EAAE,IAAI,CAAAE,OAAG,IAAIA,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG;AAAE,QAAM,MAAM,qBAAmB,MAAM,kBAAkBC,IAAE,CAAC,GAAEA;AAAC;;;ACAnS,IAAIC,KAAE,OAAO;AAAe,IAAIC,KAAE,CAACC,IAAEC,IAAEC,OAAID,MAAKD,KAAEF,GAAEE,IAAEC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAE,IAAIC,KAAE,CAACH,IAAEC,IAAEC,QAAKH,GAAEC,IAAE,OAAOC,MAAG,WAASA,KAAE,KAAGA,IAAEC,EAAC,GAAEA;AAAG,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,IAAAD,GAAE,MAAK,WAAU,KAAK,OAAO,CAAC;AAAE,IAAAA,GAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAE;AAAC,SAAK,YAAUA,OAAI,KAAK,YAAU,GAAE,KAAK,UAAQA;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,IAAI,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,WAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;AAAA,EAAQ;AAAC;AAAC,IAAI,IAAE,IAAIG;;;ACAnf,SAASC,GAAEC,IAAE;AAAC,MAAG,EAAE,SAAS,QAAO;AAAK,MAAGA,cAAa,KAAK,QAAOA,GAAE;AAAc,MAAGA,MAAG,QAAMA,GAAE,eAAe,OAAO,GAAE;AAAC,QAAIC,KAAEC,GAAEF,EAAC;AAAE,QAAGC,GAAE,QAAOA,GAAE;AAAA,EAAa;AAAC,SAAO;AAAQ;;;ACA/H,IAAIE,KAAE,CAAC,0BAAyB,cAAa,WAAU,cAAa,0BAAyB,UAAS,yBAAwB,0BAAyB,0BAA0B,EAAE,IAAI,CAAAC,OAAG,GAAGA,EAAC,uBAAuB,EAAE,KAAK,GAAG;AAAE,IAAI,KAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,aAAW,EAAE,IAAE,cAAaA,GAAEA,GAAE,WAAS,EAAE,IAAE,YAAWA,KAAI,KAAG,CAAC,CAAC;AAAtK,IAAwK,KAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,KAAI,KAAG,CAAC,CAAC;AAA9R,IAAgS,KAAG,CAAAC,QAAIA,GAAEA,GAAE,WAAS,EAAE,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,KAAI,KAAG,CAAC,CAAC;AAAE,SAAS,EAAEH,KAAE,SAAS,MAAK;AAAC,SAAOA,MAAG,OAAK,CAAC,IAAE,MAAM,KAAKA,GAAE,iBAAiBD,EAAC,CAAC,EAAE,KAAK,CAACK,IAAED,OAAI,KAAK,MAAMC,GAAE,YAAU,OAAO,qBAAmBD,GAAE,YAAU,OAAO,iBAAiB,CAAC;AAAC;AAAC,IAAIE,MAAG,CAAAF,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAIE,MAAG,CAAC,CAAC;AAAE,SAAS,EAAEL,IAAEI,KAAE,GAAE;AAAC,MAAID;AAAE,SAAOH,SAAMG,KAAEG,GAAEN,EAAC,MAAI,OAAK,SAAOG,GAAE,QAAM,QAAG,EAAEC,IAAE,EAAC,CAAC,CAAC,IAAG;AAAC,WAAOJ,GAAE,QAAQD,EAAC;AAAA,EAAC,GAAE,CAAC,CAAC,IAAG;AAAC,QAAIQ,KAAEP;AAAE,WAAKO,OAAI,QAAM;AAAC,UAAGA,GAAE,QAAQR,EAAC,EAAE,QAAM;AAAG,MAAAQ,KAAEA,GAAE;AAAA,IAAa;AAAC,WAAM;AAAA,EAAE,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEP,IAAE;AAAC,MAAII,KAAEE,GAAEN,EAAC;AAAE,WAAE,MAAI;AAAC,IAAAI,MAAG,CAAC,EAAEA,GAAE,eAAc,CAAC,KAAG,EAAEJ,EAAC;AAAA,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAAG,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,KAAG,CAAC,CAAC;AAAE,OAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,WAAU,CAAAH,OAAG;AAAC,EAAAA,GAAE,WAASA,GAAE,UAAQA,GAAE,YAAU,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE,GAAE,SAAS,iBAAiB,SAAQ,CAAAA,OAAG;AAAC,EAAAA,GAAE,WAAS,IAAE,OAAO,SAAS,gBAAgB,QAAQ,yBAAuBA,GAAE,WAAS,MAAI,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE;AAAG,SAAS,EAAEA,IAAE;AAAC,EAAAA,MAAG,QAAMA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAC;AAAC,IAAI,IAAE,CAAC,YAAW,OAAO,EAAE,KAAK,GAAG;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAII,IAAED;AAAE,UAAOA,MAAGC,KAAEJ,MAAG,OAAK,SAAOA,GAAE,YAAU,OAAK,SAAOI,GAAE,KAAKJ,IAAE,CAAC,MAAI,OAAKG,KAAE;AAAE;AAAC,SAAS,EAAEH,IAAEI,KAAE,CAAAD,OAAGA,IAAE;AAAC,SAAOH,GAAE,MAAM,EAAE,KAAK,CAACG,IAAEI,OAAI;AAAC,QAAIL,KAAEE,GAAED,EAAC,GAAEG,KAAEF,GAAEG,EAAC;AAAE,QAAGL,OAAI,QAAMI,OAAI,KAAK,QAAO;AAAE,QAAIL,KAAEC,GAAE,wBAAwBI,EAAC;AAAE,WAAOL,KAAE,KAAK,8BAA4B,KAAGA,KAAE,KAAK,8BAA4B,IAAE;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAEI,IAAE;AAAC,SAAO,EAAE,EAAE,GAAEA,IAAE,EAAC,YAAWJ,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEI,IAAE,EAAC,QAAOD,KAAE,MAAG,YAAWI,KAAE,MAAK,cAAaL,KAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIM;AAAE,MAAIF,MAAGE,KAAE,MAAM,QAAQR,EAAC,IAAEA,GAAE,SAAO,IAAEA,GAAE,CAAC,EAAE,gBAAc,WAASA,MAAG,OAAK,SAAOA,GAAE,kBAAgB,OAAKQ,KAAE,UAASP,KAAE,MAAM,QAAQD,EAAC,IAAEG,KAAE,EAAEH,EAAC,IAAEA,KAAE,EAAEA,EAAC;AAAE,EAAAE,GAAE,SAAO,KAAGD,GAAE,SAAO,MAAIA,KAAEA,GAAE,OAAO,CAAAQ,OAAG,CAACP,GAAE,SAASO,EAAC,CAAC,IAAGF,KAAEA,MAAG,OAAKA,KAAED,GAAE;AAAc,MAAII,MAAG,MAAI;AAAC,QAAGN,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,GAAG,QAAM;AAAG,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEO,MAAG,MAAI;AAAC,QAAGP,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEH,GAAE,QAAQM,EAAC,CAAC,IAAE;AAAE,QAAGH,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEH,GAAE,QAAQM,EAAC,CAAC,IAAE;AAAE,QAAGH,KAAE,EAAE,QAAOH,GAAE,SAAO;AAAE,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEW,KAAER,KAAE,KAAG,EAAC,eAAc,KAAE,IAAE,CAAC,GAAES,KAAE,GAAEC,KAAEb,GAAE,QAAOc;AAAE,KAAE;AAAC,QAAGF,MAAGC,MAAGD,KAAEC,MAAG,EAAE,QAAO;AAAE,QAAIL,KAAEE,KAAEE;AAAE,QAAGT,KAAE,GAAG,CAAAK,MAAGA,KAAEK,MAAGA;AAAA,SAAM;AAAC,UAAGL,KAAE,EAAE,QAAO;AAAE,UAAGA,MAAGK,GAAE,QAAO;AAAA,IAAC;AAAC,IAAAC,KAAEd,GAAEQ,EAAC,GAAEM,MAAG,QAAMA,GAAE,MAAMH,EAAC,GAAEC,MAAGH;AAAA,EAAC,SAAOK,OAAIT,GAAE;AAAe,SAAOF,KAAE,KAAG,EAAEW,EAAC,KAAGA,GAAE,OAAO,GAAE;AAAC;;;ACAv6F,SAASC,KAAG;AAAC,SAAM,WAAW,KAAK,OAAO,UAAU,QAAQ,KAAG,QAAQ,KAAK,OAAO,UAAU,QAAQ,KAAG,OAAO,UAAU,iBAAe;AAAC;AAAC,SAASC,KAAG;AAAC,SAAM,YAAY,KAAK,OAAO,UAAU,SAAS;AAAC;AAAC,SAASC,KAAG;AAAC,SAAOF,GAAE,KAAGC,GAAE;AAAC;;;ACA9J,SAASE,GAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAE,YAAU,YAAE,CAAAC,OAAG;AAAC,aAAS,iBAAiBH,IAAEC,IAAEC,EAAC,GAAEC,GAAE,MAAI,SAAS,oBAAoBH,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ACAjH,SAASE,GAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAE,YAAU,YAAE,CAAAC,OAAG;AAAC,WAAO,iBAAiBH,IAAEC,IAAEC,EAAC,GAAEC,GAAE,MAAI,OAAO,oBAAoBH,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ACAgJ,SAASE,GAAEC,IAAEC,IAAEC,KAAE,SAAE,MAAI,IAAE,GAAE;AAAC,WAASC,GAAEC,IAAEC,IAAE;AAAC,QAAG,CAACH,GAAE,SAAOE,GAAE,iBAAiB;AAAO,QAAIE,KAAED,GAAED,EAAC;AAAE,QAAGE,OAAI,QAAM,CAACA,GAAE,YAAY,EAAE,SAASA,EAAC,EAAE;AAAO,QAAIC,KAAE,SAASC,GAAEC,IAAE;AAAC,aAAO,OAAOA,MAAG,aAAWD,GAAEC,GAAE,CAAC,IAAE,MAAM,QAAQA,EAAC,KAAGA,cAAa,MAAIA,KAAE,CAACA,EAAC;AAAA,IAAC,EAAET,EAAC;AAAE,aAAQQ,MAAKD,IAAE;AAAC,UAAGC,OAAI,KAAK;AAAS,UAAIC,KAAED,cAAa,cAAYA,KAAEA,GAAEA,EAAC;AAAE,UAAGC,MAAG,QAAMA,GAAE,SAASH,EAAC,KAAGF,GAAE,YAAUA,GAAE,aAAa,EAAE,SAASK,EAAC,EAAE;AAAA,IAAM;AAAC,WAAM,CAAC,EAAEH,IAAEI,GAAE,KAAK,KAAGJ,GAAE,aAAW,MAAIF,GAAE,eAAe,GAAEH,GAAEG,IAAEE,EAAC;AAAA,EAAC;AAAC,MAAIK,KAAE,IAAE,IAAI;AAAE,EAAAA,GAAE,eAAc,CAAAP,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAJ,GAAE,UAAQS,GAAE,UAAQL,MAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,GAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEO,GAAE,aAAY,CAAAP,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAJ,GAAE,UAAQS,GAAE,UAAQL,MAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,GAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEO,GAAE,SAAQ,CAAAP,OAAG;AAAC,IAAAK,GAAE,KAAGE,GAAE,UAAQR,GAAEC,IAAE,MAAIO,GAAE,KAAK,GAAEA,GAAE,QAAM;AAAA,EAAK,GAAE,IAAE,GAAEA,GAAE,YAAW,CAAAP,OAAGD,GAAEC,IAAE,MAAIA,GAAE,kBAAkB,cAAYA,GAAE,SAAO,IAAI,GAAE,IAAE,GAAEL,GAAE,QAAO,CAAAK,OAAGD,GAAEC,IAAE,MAAI,OAAO,SAAS,yBAAyB,oBAAkB,OAAO,SAAS,gBAAc,IAAI,GAAE,IAAE;AAAC;;;ACA5rC,SAASQ,GAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,QAAOA;AAAE,MAAIE,KAAED,MAAG,OAAKA,KAAE;AAAS,MAAG,OAAOC,MAAG,YAAUA,GAAE,YAAY,MAAI,SAAS,QAAM;AAAQ;AAAC,SAASC,GAAEH,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAEH,GAAEC,GAAE,MAAM,MAAKA,GAAE,MAAM,EAAE,CAAC;AAAE,SAAO,UAAE,MAAI;AAAC,IAAAE,GAAE,QAAMH,GAAEC,GAAE,MAAM,MAAKA,GAAE,MAAM,EAAE;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,QAAII;AAAE,IAAAF,GAAE,SAAOG,GAAEJ,EAAC,KAAGI,GAAEJ,EAAC,aAAY,qBAAmB,GAAGG,KAAEC,GAAEJ,EAAC,MAAI,QAAMG,GAAE,aAAa,MAAM,OAAKF,GAAE,QAAM;AAAA,EAAS,CAAC,GAAEA;AAAC;;;ACApa,SAASI,GAAEC,IAAE;AAAC,SAAM,CAACA,GAAE,SAAQA,GAAE,OAAO;AAAC;AAAC,SAASC,KAAG;AAAC,MAAID,KAAE,IAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAM,EAAC,SAASE,IAAE;AAAC,QAAIC,KAAEJ,GAAEG,EAAC;AAAE,WAAOF,GAAE,MAAM,CAAC,MAAIG,GAAE,CAAC,KAAGH,GAAE,MAAM,CAAC,MAAIG,GAAE,CAAC,IAAE,SAAIH,GAAE,QAAMG,IAAE;AAAA,EAAG,GAAE,OAAOD,IAAE;AAAC,IAAAF,GAAE,QAAMD,GAAEG,EAAC;AAAA,EAAC,EAAC;AAAC;;;ACAhI,SAASE,GAAE,EAAC,WAAUC,IAAE,QAAOC,IAAE,MAAKC,IAAE,SAAQC,GAAC,GAAE;AAAC,cAAE,MAAI;AAAC,QAAIC,KAAEJ,GAAE;AAAM,QAAG,CAACI,MAAGD,OAAI,UAAQ,CAACA,GAAE,MAAM;AAAO,QAAIE,KAAEN,GAAEC,EAAC;AAAE,QAAG,CAACK,GAAE;AAAO,QAAIC,KAAE,OAAO,OAAO,CAAAC,OAAGN,GAAEM,EAAC,GAAE,EAAC,YAAWN,GAAC,CAAC,GAAEO,KAAEH,GAAE,iBAAiBD,IAAE,WAAW,cAAaE,IAAE,KAAE;AAAE,WAAKE,GAAE,SAAS,IAAG,CAAAN,GAAEM,GAAE,WAAW;AAAA,EAAC,CAAC;AAAC;;;ACAxQ,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAArG,IAAuGE,MAAG,CAAAC,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,SAAS,EAAE,EAAC,SAAQE,KAAE,MAAG,UAASC,KAAE,GAAE,UAASF,IAAE,YAAWF,IAAE,GAAGK,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,KAAE,EAAEP,IAAEE,EAAC,GAAEM,KAAE,OAAO,OAAOH,IAAE,EAAC,OAAME,GAAC,CAAC;AAAE,MAAGJ,MAAGC,KAAE,KAAGG,GAAE,OAAO,QAAOE,GAAED,EAAC;AAAE,MAAGJ,KAAE,GAAE;AAAC,QAAIM,MAAGJ,KAAEC,GAAE,YAAU,QAAMD,KAAE,IAAE;AAAE,WAAO,EAAEI,IAAE,EAAC,CAAC,CAAC,IAAG;AAAC,aAAO;AAAA,IAAI,GAAE,CAAC,CAAC,IAAG;AAAC,aAAOD,GAAE,EAAC,GAAGJ,IAAE,OAAM,EAAC,GAAGE,IAAE,QAAO,MAAG,OAAM,EAAC,SAAQ,OAAM,EAAC,EAAC,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOE,GAAED,EAAC;AAAC;AAAC,SAASC,GAAE,EAAC,OAAMN,IAAE,OAAMC,IAAE,OAAMF,IAAE,MAAKF,IAAE,MAAKK,GAAC,GAAE;AAAC,MAAIM,IAAEC;AAAE,MAAG,EAAC,IAAGL,IAAE,GAAGC,GAAC,IAAEK,GAAEV,IAAE,CAAC,WAAU,QAAQ,CAAC,GAAEG,MAAGK,KAAET,GAAE,YAAU,OAAK,SAAOS,GAAE,KAAKT,IAAEF,EAAC,GAAEU,KAAE,CAAC;AAAE,MAAGV,IAAE;AAAC,QAAIc,KAAE,OAAGC,KAAE,CAAC;AAAE,aAAO,CAACC,IAAEC,EAAC,KAAI,OAAO,QAAQjB,EAAC,EAAE,QAAOiB,MAAG,cAAYH,KAAE,OAAIG,OAAI,QAAIF,GAAE,KAAKC,EAAC;AAAE,IAAAF,OAAIJ,GAAE,uBAAuB,IAAEK,GAAE,KAAK,GAAG;AAAA,EAAE;AAAC,MAAGR,OAAI,YAAW;AAAC,QAAGD,KAAE,EAAEA,MAAG,OAAKA,KAAE,CAAC,CAAC,GAAE,OAAO,KAAKE,EAAC,EAAE,SAAO,KAAG,OAAO,KAAKJ,EAAC,EAAE,SAAO,GAAE;AAAC,UAAG,CAACU,IAAE,GAAGC,EAAC,IAAET,MAAG,OAAKA,KAAE,CAAC;AAAE,UAAG,CAACY,GAAEJ,EAAC,KAAGC,GAAE,SAAO,EAAE,OAAM,IAAI,MAAM,CAAC,gCAA+B,IAAG,0BAA0BV,EAAC,kCAAiC,uDAAsD,OAAO,KAAKG,EAAC,EAAE,OAAO,OAAO,KAAKJ,EAAC,CAAC,EAAE,IAAI,CAAAe,OAAGA,GAAE,KAAK,CAAC,EAAE,OAAO,CAACA,IAAEC,IAAEC,OAAIA,GAAE,QAAQF,EAAC,MAAIC,EAAC,EAAE,KAAK,CAACD,IAAEC,OAAID,GAAE,cAAcC,EAAC,CAAC,EAAE,IAAI,CAAAD,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CAClxC,GAAE,IAAG,kCAAiC,CAAC,+FAA8F,0FAA0F,EAAE,IAAI,CAAAA,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CACzP,CAAC,EAAE,KAAK;AAAA,CACR,CAAC;AAAE,UAAIH,KAAE,GAAGJ,KAAEE,GAAE,UAAQ,OAAKF,KAAE,CAAC,GAAEJ,IAAEE,EAAC,GAAEO,KAAE,WAAEH,IAAEE,IAAE,IAAE;AAAE,eAAQG,MAAKH,GAAE,CAAAG,GAAE,WAAW,IAAI,MAAIF,GAAE,UAAQA,GAAE,QAAM,CAAC,IAAGA,GAAE,MAAME,EAAC,IAAEH,GAAEG,EAAC;AAAG,aAAOF;AAAA,IAAC;AAAC,WAAO,MAAM,QAAQX,EAAC,KAAGA,GAAE,WAAS,IAAEA,GAAE,CAAC,IAAEA;AAAA,EAAC;AAAC,SAAO,EAAEC,IAAE,OAAO,OAAO,CAAC,GAAEC,IAAEE,EAAC,GAAE,EAAC,SAAQ,MAAIJ,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAOA,GAAE,QAAQ,CAAAC,OAAGA,GAAE,SAAO,WAAE,EAAEA,GAAE,QAAQ,IAAE,CAACA,EAAC,CAAC;AAAC;AAAC,SAAS,KAAKD,IAAE;AAAC,MAAIH;AAAE,MAAGG,GAAE,WAAS,EAAE,QAAM,CAAC;AAAE,MAAGA,GAAE,WAAS,EAAE,QAAOA,GAAE,CAAC;AAAE,MAAIC,KAAE,CAAC,GAAEF,KAAE,CAAC;AAAE,WAAQG,MAAKF,GAAE,UAAQI,MAAKF,GAAE,CAAAE,GAAE,WAAW,IAAI,KAAG,OAAOF,GAAEE,EAAC,KAAG,eAAaP,KAAEE,GAAEK,EAAC,MAAI,SAAOL,GAAEK,EAAC,IAAE,CAAC,IAAGL,GAAEK,EAAC,EAAE,KAAKF,GAAEE,EAAC,CAAC,KAAGH,GAAEG,EAAC,IAAEF,GAAEE,EAAC;AAAE,MAAGH,GAAE,YAAUA,GAAE,eAAe,EAAE,QAAO,OAAO,OAAOA,IAAE,OAAO,YAAY,OAAO,KAAKF,EAAC,EAAE,IAAI,CAAAG,OAAG,CAACA,IAAE,MAAM,CAAC,CAAC,CAAC;AAAE,WAAQA,MAAKH,GAAE,QAAO,OAAOE,IAAE,EAAC,CAACC,EAAC,EAAEE,OAAKC,IAAE;AAAC,QAAIF,KAAEJ,GAAEG,EAAC;AAAE,aAAQK,MAAKJ,IAAE;AAAC,UAAGC,cAAa,SAAOA,GAAE,iBAAiB;AAAO,MAAAG,GAAEH,IAAE,GAAGC,EAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC;AAAE,SAAOJ;AAAC;AAAC,SAASkB,GAAEnB,IAAE;AAAC,MAAIC,KAAE,OAAO,OAAO,CAAC,GAAED,EAAC;AAAE,WAAQD,MAAKE,GAAE,CAAAA,GAAEF,EAAC,MAAI,UAAQ,OAAOE,GAAEF,EAAC;AAAE,SAAOE;AAAC;AAAC,SAASS,GAAEV,IAAEC,KAAE,CAAC,GAAE;AAAC,MAAIF,KAAE,OAAO,OAAO,CAAC,GAAEC,EAAC;AAAE,WAAQH,MAAKI,GAAE,CAAAJ,MAAKE,MAAG,OAAOA,GAAEF,EAAC;AAAE,SAAOE;AAAC;AAAC,SAASgB,GAAEf,IAAE;AAAC,SAAOA,MAAG,OAAK,QAAG,OAAOA,GAAE,QAAM,YAAU,OAAOA,GAAE,QAAM,YAAU,OAAOA,GAAE,QAAM;AAAU;;;ACH78B,IAAIoB,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,IAAI,IAAE,gBAAE,EAAC,MAAK,UAAS,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,MAAME,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,SAAM,MAAI;AAAC,QAAIC;AAAE,QAAG,EAAC,UAASJ,IAAE,GAAGK,GAAC,IAAEJ,IAAEK,KAAE,EAAC,gBAAeN,KAAE,OAAK,IAAE,QAAII,KAAEC,GAAE,aAAa,MAAI,OAAKD,KAAE,QAAO,SAAQJ,KAAE,OAAK,IAAE,OAAG,QAAO,OAAM,EAAC,UAAS,SAAQ,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,QAAO,IAAG,UAAS,UAAS,MAAK,oBAAmB,YAAW,UAAS,aAAY,KAAI,IAAIA,KAAE,OAAK,MAAIA,KAAE,OAAK,KAAG,EAAC,SAAQ,OAAM,EAAC,EAAC;AAAE,WAAO,EAAE,EAAC,UAASM,IAAE,YAAWD,IAAE,MAAK,CAAC,GAAE,OAAMF,IAAE,OAAMD,IAAE,MAAK,SAAQ,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA7pB,IAAIK,KAAE,OAAO,SAAS;AAAE,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,SAAO,EAAE,MAAI;AAAI;AAAC,SAAS,IAAG;AAAC,SAAO,OAAEH,IAAE,IAAI;AAAC;AAAC,SAASI,GAAEC,IAAE;AAAC,UAAEL,IAAEK,EAAC;AAAC;;;ACArQ,IAAIC,MAAG,CAAAC,QAAIA,GAAE,QAAM,KAAIA,GAAE,QAAM,SAAQA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,UAAQ,WAAUA,GAAE,aAAW,cAAaA,GAAE,YAAU,aAAYA,GAAE,OAAK,QAAOA,GAAE,MAAI,OAAMA,GAAE,SAAO,UAASA,GAAE,WAAS,YAAWA,GAAE,MAAI,OAAMA,KAAID,MAAG,CAAC,CAAC;;;ACAxR,IAAI,KAAG,CAAAE,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,KAAG,CAAC,CAAC;;;ACA5D,SAASC,GAAEC,IAAE;AAAC,WAASC,KAAG;AAAC,aAAS,eAAa,cAAYD,GAAE,GAAE,SAAS,oBAAoB,oBAAmBC,EAAC;AAAA,EAAE;AAAC,SAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,oBAAmBA,EAAC,GAAEA,GAAE;AAAE;;;ACA9K,IAAIC,KAAE,CAAC;AAAEA,GAAE,MAAI;AAAC,WAASC,GAAEC,IAAE;AAAC,IAAAA,GAAE,kBAAkB,eAAaA,GAAE,WAAS,SAAS,QAAMF,GAAE,CAAC,MAAIE,GAAE,WAASF,GAAE,QAAQE,GAAE,MAAM,GAAEF,KAAEA,GAAE,OAAO,CAAAG,OAAGA,MAAG,QAAMA,GAAE,WAAW,GAAEH,GAAE,OAAO,EAAE;AAAA,EAAE;AAAC,SAAO,iBAAiB,SAAQC,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC;AAAC,CAAC;;;ACAtiB,SAASG,GAAEC,IAAE;AAAC,QAAM,IAAI,MAAM,wBAAsBA,EAAC;AAAC;AAAC,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEH,IAAEI,IAAE;AAAC,MAAIC,KAAED,GAAE,aAAa;AAAE,MAAGC,GAAE,UAAQ,EAAE,QAAO;AAAK,MAAIC,KAAEF,GAAE,mBAAmB,GAAEG,KAAED,MAAG,OAAKA,KAAE;AAAG,UAAON,GAAE,OAAM;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQQ,KAAE,GAAEA,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,MAAAC,OAAI,OAAKA,KAAEF,GAAE;AAAQ,eAAQG,KAAED,KAAE,GAAEC,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAED,KAAE,GAAEC,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAEH,GAAE,SAAO,GAAEG,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAGJ,GAAE,UAAUC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,MAAIL,GAAE,GAAG,QAAOQ;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK;AAAE,aAAO;AAAA,IAAK;AAAQ,MAAAP,GAAEC,EAAC;AAAA,EAAC;AAAC;;;ACApzB,SAAS,EAAES,KAAE,CAAC,GAAEC,KAAE,MAAKC,KAAE,CAAC,GAAE;AAAC,WAAO,CAACC,IAAEC,EAAC,KAAI,OAAO,QAAQJ,EAAC,EAAE,CAAAK,GAAEH,IAAEI,GAAEL,IAAEE,EAAC,GAAEC,EAAC;AAAE,SAAOF;AAAC;AAAC,SAASI,GAAEN,IAAEC,IAAE;AAAC,SAAOD,KAAEA,KAAE,MAAIC,KAAE,MAAIA;AAAC;AAAC,SAASI,GAAEL,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQA,EAAC,EAAE,UAAO,CAACC,IAAEC,EAAC,KAAIF,GAAE,QAAQ,EAAE,CAAAG,GAAEL,IAAEM,GAAEL,IAAEE,GAAE,SAAS,CAAC,GAAEC,EAAC;AAAA,MAAO,CAAAF,cAAa,OAAKF,GAAE,KAAK,CAACC,IAAEC,GAAE,YAAY,CAAC,CAAC,IAAE,OAAOA,MAAG,YAAUF,GAAE,KAAK,CAACC,IAAEC,KAAE,MAAI,GAAG,CAAC,IAAE,OAAOA,MAAG,WAASF,GAAE,KAAK,CAACC,IAAEC,EAAC,CAAC,IAAE,OAAOA,MAAG,WAASF,GAAE,KAAK,CAACC,IAAE,GAAGC,EAAC,EAAE,CAAC,IAAEA,MAAG,OAAKF,GAAE,KAAK,CAACC,IAAE,EAAE,CAAC,IAAE,EAAEC,IAAED,IAAED,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAIE,IAAEC;AAAE,MAAIF,MAAGC,KAAEF,MAAG,OAAK,SAAOA,GAAE,SAAO,OAAKE,KAAEF,GAAE,QAAQ,MAAM;AAAE,MAAGC,IAAE;AAAC,aAAQG,MAAKH,GAAE,SAAS,KAAGG,OAAIJ,OAAII,GAAE,YAAU,WAASA,GAAE,SAAO,YAAUA,GAAE,YAAU,YAAUA,GAAE,SAAO,YAAUA,GAAE,aAAW,WAASA,GAAE,SAAO,UAAS;AAAC,MAAAA,GAAE,MAAM;AAAE;AAAA,IAAM;AAAC,KAACD,KAAEF,GAAE,kBAAgB,QAAME,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAC;;;ACAk5B,SAAS,GAAGM,IAAEC,IAAE;AAAC,SAAOD,OAAIC;AAAC;AAAC,IAAI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAhE,IAAkE,MAAI,CAAAA,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAhI,IAAkI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,iBAAiB;AAAE,SAAS,EAAEH,IAAE;AAAC,MAAIC,KAAE,OAAG,IAAG,IAAI;AAAE,MAAGA,OAAI,MAAK;AAAC,QAAIC,KAAE,IAAI,MAAM,IAAIF,EAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBE,IAAE,CAAC,GAAEA;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,IAAI,KAAG,OAAO,gBAAgB;AAA9B,IAAgC,KAAG,gBAAE,EAAC,MAAK,mBAAkB,MAAMD,IAAE,EAAC,OAAMC,GAAC,GAAE;AAAC,MAAIC,KAAE,EAAE,iBAAiB,GAAEC,KAAE,SAAE,MAAI;AAAC,QAAIC,KAAEC,GAAEH,GAAE,UAAU;AAAE,QAAG,CAACE,GAAE,QAAM,EAAC,OAAM,GAAE,KAAI,EAAC;AAAE,QAAIE,KAAE,OAAO,iBAAiBF,EAAC;AAAE,WAAM,EAAC,OAAM,WAAWE,GAAE,qBAAmBA,GAAE,UAAU,GAAE,KAAI,WAAWA,GAAE,mBAAiBA,GAAE,aAAa,EAAC;AAAA,EAAC,CAAC,GAAED,KAAE,eAAG,SAAE,OAAK,EAAC,oBAAmBF,GAAE,MAAM,OAAM,kBAAiBA,GAAE,MAAM,KAAI,OAAMD,GAAE,QAAQ,MAAM,QAAQ,QAAO,eAAc;AAAC,WAAO;AAAA,EAAE,GAAE,mBAAkB;AAAC,WAAOG,GAAEH,GAAE,UAAU;AAAA,EAAC,GAAE,UAAS,GAAE,EAAE,CAAC,GAAEK,KAAE,SAAE,MAAI;AAAC,QAAIH;AAAE,YAAOA,KAAEF,GAAE,QAAQ,UAAQ,OAAK,SAAOE,GAAE;AAAA,EAAO,CAAC,GAAEI,KAAE,IAAE,CAAC;AAAE,SAAO,MAAE,CAACD,EAAC,GAAE,MAAI;AAAC,IAAAC,GAAE,SAAO;AAAA,EAAC,CAAC,GAAE,QAAG,IAAGN,GAAE,QAAQ,QAAMG,KAAE,IAAI,GAAE,MAAI,CAAC,EAAE,OAAM,EAAC,OAAM,EAAC,UAAS,YAAW,OAAM,QAAO,QAAO,GAAGA,GAAE,MAAM,aAAa,CAAC,KAAI,GAAE,KAAI,CAAAD,OAAG;AAAC,QAAGA,IAAE;AAAC,UAAG,OAAO,WAAS,eAAa,QAAQ,IAAI,mBAAiB,UAAQF,GAAE,kBAAkB,UAAQ,EAAE;AAAO,MAAAA,GAAE,kBAAkB,UAAQ,QAAMA,GAAE,QAAQ,MAAM,QAAQ,SAAOA,GAAE,kBAAkB,SAAOG,GAAE,MAAM,cAAcH,GAAE,kBAAkB,KAAK;AAAA,IAAC;AAAA,EAAC,EAAC,GAAEG,GAAE,MAAM,gBAAgB,EAAE,IAAI,CAAAD,OAAG,WAAGH,GAAE,QAAQ,EAAC,QAAOC,GAAE,QAAQ,MAAM,QAAQE,GAAE,KAAK,GAAE,MAAKF,GAAE,cAAc,UAAQ,EAAC,CAAC,EAAE,CAAC,GAAE,EAAC,KAAI,GAAGM,GAAE,KAAK,IAAIJ,GAAE,KAAK,IAAG,cAAaA,GAAE,OAAM,gBAAeF,GAAE,QAAQ,MAAM,QAAQ,QAAO,iBAAgBE,GAAE,QAAM,GAAE,OAAM,EAAC,UAAS,YAAW,KAAI,GAAE,MAAK,GAAE,WAAU,cAAcA,GAAE,KAAK,OAAM,gBAAe,OAAM,EAAC,CAAC,CAAC,CAAC,CAAC;AAAC,EAAC,CAAC;AAAt0C,IAAw0C,KAAG,gBAAE,EAAC,MAAK,YAAW,OAAM,EAAC,qBAAoB,CAAAJ,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,UAAS,MAAG,SAAQ,KAAI,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,WAAU,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,MAAKC,GAAC,GAAE;AAAC,MAAIE,KAAE,IAAE,CAAC,GAAEE,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEJ,KAAE,IAAE,IAAI,GAAEE,KAAE,IAAE,IAAI,GAAEG,KAAE,IAAE,EAAC,QAAO,OAAG,MAAK,MAAE,CAAC,GAAEC,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,IAAE,IAAI,GAAE,IAAE,IAAE,CAAC,GAAEC,KAAE,IAAE,KAAE;AAAE,WAASC,GAAEC,KAAE,CAAAC,OAAGA,IAAE;AAAC,QAAIA,KAAEJ,GAAE,UAAQ,OAAKD,GAAE,MAAMC,GAAE,KAAK,IAAE,MAAKK,KAAEF,GAAEJ,GAAE,MAAM,MAAM,CAAC,GAAEO,KAAED,GAAE,SAAO,KAAGA,GAAE,CAAC,EAAE,QAAQ,MAAM,UAAQ,OAAKA,GAAE,KAAK,CAACE,IAAEC,OAAID,GAAE,QAAQ,MAAM,QAAMC,GAAE,QAAQ,MAAM,KAAK,IAAE,EAAGH,IAAE,CAAAE,OAAGb,GAAEa,GAAE,QAAQ,MAAM,CAAC,GAAEE,KAAEL,KAAEE,GAAE,QAAQF,EAAC,IAAE;AAAK,WAAOK,OAAI,OAAKA,KAAE,OAAM,EAAC,SAAQH,IAAE,mBAAkBG,GAAC;AAAA,EAAC;AAAC,MAAIC,KAAE,SAAE,MAAIrB,GAAE,WAAS,IAAE,CAAC,GAAEsB,KAAE,SAAE,MAAItB,GAAE,QAAQ,GAAE,CAACuB,IAAEC,EAAC,IAAE,EAAG,SAAE,MAAIxB,GAAE,UAAU,GAAE,CAAAc,OAAGX,GAAE,qBAAoBW,EAAC,GAAE,SAAE,MAAId,GAAE,YAAY,CAAC,GAAEyB,KAAE,SAAE,MAAIF,GAAE,UAAQ,SAAO,EAAEF,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,OAAM,CAAC,IAAEE,GAAE,KAAK,GAAEG,KAAE,MAAKC,KAAE;AAAK,WAASC,GAAEd,IAAE;AAAC,WAAO,EAAEO,GAAE,OAAM,EAAC,CAAC,CAAC,IAAG;AAAC,aAAOG,MAAG,OAAK,SAAOA,GAAEV,EAAC;AAAA,IAAC,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAIC,KAAE,MAAEc,GAAE,MAAM,KAAK,EAAE,MAAM,GAAEb,KAAE,MAAEF,EAAC,GAAEG,KAAEF,GAAE,UAAU,CAAAK,OAAGS,GAAE,QAAQb,IAAE,MAAEI,EAAC,CAAC,CAAC;AAAE,aAAOH,OAAI,KAAGF,GAAE,KAAKC,EAAC,IAAED,GAAE,OAAOE,IAAE,CAAC,GAAEO,MAAG,OAAK,SAAOA,GAAET,EAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAIe,KAAE,SAAE,MAAI;AAAA,EAAC,CAAC;AAAE,QAAE,CAACA,EAAC,GAAE,CAAC,CAAChB,EAAC,GAAE,CAACC,EAAC,MAAI;AAAC,QAAGc,GAAE,QAAQ,SAAOf,MAAGC,MAAGJ,GAAE,UAAQ,MAAK;AAAC,UAAIK,KAAEF,GAAE,QAAQC,GAAEJ,GAAE,KAAK,CAAC;AAAE,MAAAK,OAAI,KAAGL,GAAE,QAAMK,KAAEL,GAAE,QAAM;AAAA,IAAI;AAAA,EAAC,CAAC;AAAE,MAAIkB,KAAE,EAAC,eAAcxB,IAAE,OAAMoB,IAAE,MAAKJ,IAAE,QAAQP,IAAEC,IAAE;AAAC,QAAG,OAAOf,GAAE,MAAI,UAAS;AAAC,UAAIgB,KAAEhB,GAAE;AAAG,cAAOc,MAAG,OAAK,SAAOA,GAAEE,EAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,EAAC;AAAA,IAAE;AAAC,WAAOhB,GAAE,OAAK,OAAK,GAAGc,IAAEC,EAAC,IAAEf,GAAE,GAAGc,IAAEC,EAAC;AAAA,EAAC,GAAE,eAAeD,IAAE;AAAC,WAAOe,GAAE,QAAQ,QAAM7B,GAAE,OAAK,OAAK6B,GAAE,QAAQ,MAAM,QAAQ,QAAQf,EAAC,IAAEe,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAd,OAAGc,GAAE,QAAQd,IAAED,EAAC,CAAC,IAAEJ,GAAE,MAAM,UAAU,CAAAK,OAAGc,GAAE,QAAQd,GAAE,QAAQ,OAAMD,EAAC,CAAC;AAAA,EAAC,GAAE,cAAa,SAAE,MAAId,GAAE,YAAY,GAAE,UAASsB,IAAE,WAAU,SAAE,MAAI,KAAE,GAAE,SAAQ,SAAE,MAAI,IAAI,GAAE,UAASd,IAAE,UAASD,IAAE,WAAUH,IAAE,YAAWE,IAAE,UAAS,SAAE,MAAIN,GAAE,QAAQ,GAAE,SAAQU,IAAE,OAAOI,IAAE;AAAC,IAAAU,GAAEV,EAAC;AAAA,EAAC,GAAE,mBAAkB,SAAE,MAAI;AAAC,QAAGF,GAAE,SAAOD,GAAE,UAAQ,SAAOkB,GAAE,QAAQ,QAAMA,GAAE,QAAQ,MAAM,QAAQ,SAAO,IAAEnB,GAAE,MAAM,SAAO,IAAG;AAAC,UAAGmB,GAAE,QAAQ,OAAM;AAAC,YAAId,KAAEc,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAb,OAAG;AAAC,cAAIC;AAAE,iBAAM,GAAGA,KAAEY,GAAE,QAAQ,UAAQ,QAAMZ,GAAE,SAASD,EAAC;AAAA,QAAE,CAAC;AAAE,YAAGD,OAAI,GAAG,QAAOA;AAAA,MAAC;AAAC,UAAID,KAAEJ,GAAE,MAAM,UAAU,CAAAK,OAAG,CAACA,GAAE,QAAQ,QAAQ;AAAE,UAAGD,OAAI,GAAG,QAAOA;AAAA,IAAC;AAAC,WAAOH,GAAE;AAAA,EAAK,CAAC,GAAE,mBAAkB,GAAE,iBAAgBF,IAAE,gBAAe;AAAC,IAAAG,GAAE,QAAM,OAAG,CAACZ,GAAE,YAAUK,GAAE,UAAQ,MAAIA,GAAE,QAAM,GAAEM,GAAE,QAAM;AAAA,EAAK,GAAE,eAAc;AAAC,QAAGC,GAAE,QAAM,MAAG,CAACZ,GAAE,YAAUK,GAAE,UAAQ,GAAE;AAAC,UAAGwB,GAAE,MAAM,OAAM;AAAC,YAAIf,KAAEe,GAAE,eAAeA,GAAE,MAAM,KAAK;AAAE,QAAAf,OAAI,OAAKH,GAAE,QAAMG;AAAA,MAAE;AAAC,MAAAT,GAAE,QAAM;AAAA,IAAC;AAAA,EAAC,GAAE,qBAAqBS,IAAE;AAAC,MAAE,QAAMA;AAAA,EAAC,GAAE,WAAWA,IAAEC,IAAEC,IAAE;AAAC,IAAAJ,GAAE,QAAM,OAAGc,OAAI,QAAM,qBAAqBA,EAAC,GAAEA,KAAE,sBAAsB,MAAI;AAAC,UAAG1B,GAAE,YAAUM,GAAE,SAAO,CAACG,GAAE,MAAM,UAAQJ,GAAE,UAAQ,EAAE;AAAO,UAAGwB,GAAE,QAAQ,OAAM;AAAC,QAAAlB,GAAE,QAAMG,OAAIV,GAAE,WAASW,KAAET,GAAG,EAAC,OAAMQ,GAAC,GAAE,EAAC,cAAa,MAAIe,GAAE,QAAQ,MAAM,SAAQ,oBAAmB,MAAI;AAAC,cAAIX,IAAEC;AAAE,kBAAOA,MAAGD,KAAEW,GAAE,kBAAkB,UAAQ,OAAKX,KAAEW,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAE,OAAG;AAAC,gBAAIC;AAAE,mBAAM,GAAGA,KAAEH,GAAE,QAAQ,UAAQ,QAAMG,GAAE,SAASD,EAAC;AAAA,UAAE,CAAC,MAAI,OAAKZ,KAAE;AAAA,QAAI,GAAE,iBAAgB,CAAAD,OAAGW,GAAE,QAAQ,MAAM,SAASX,EAAC,GAAE,YAAW;AAAC,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAAC,EAAC,CAAC,GAAE,EAAE,QAAMF,MAAG,OAAKA,KAAE;AAAE;AAAA,MAAM;AAAC,UAAIC,KAAEJ,GAAE;AAAE,UAAGI,GAAE,sBAAoB,MAAK;AAAC,YAAIC,KAAED,GAAE,QAAQ,UAAU,CAAAE,OAAG,CAACA,GAAE,QAAQ,QAAQ;AAAE,QAAAD,OAAI,OAAKD,GAAE,oBAAkBC;AAAA,MAAE;AAAC,UAAIE,KAAEN,OAAIV,GAAE,WAASW,KAAET,GAAG,EAAC,OAAMQ,GAAC,GAAE,EAAC,cAAa,MAAIG,GAAE,SAAQ,oBAAmB,MAAIA,GAAE,mBAAkB,WAAU,CAAAC,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,SAAQ,CAAC;AAAE,MAAAP,GAAE,QAAMS,IAAE,EAAE,QAAMJ,MAAG,OAAKA,KAAE,GAAEN,GAAE,QAAMO,GAAE;AAAA,IAAO,CAAC;AAAA,EAAC,GAAE,aAAaH,IAAE;AAAC,QAAIC,KAAEL,GAAE,MAAM,KAAK,CAAAO,OAAGA,GAAE,OAAKH,EAAC;AAAE,QAAG,CAACC,GAAE;AAAO,QAAG,EAAC,SAAQC,GAAC,IAAED;AAAE,IAAAa,GAAEZ,GAAE,KAAK;AAAA,EAAC,GAAE,qBAAoB;AAAC,QAAGa,GAAE,kBAAkB,UAAQ,MAAK;AAAC,UAAGA,GAAE,QAAQ,MAAM,CAAAD,GAAEC,GAAE,QAAQ,MAAM,QAAQA,GAAE,kBAAkB,KAAK,CAAC;AAAA,WAAM;AAAC,YAAG,EAAC,SAAQf,GAAC,IAAEJ,GAAE,MAAMmB,GAAE,kBAAkB,KAAK;AAAE,QAAAD,GAAEd,GAAE,KAAK;AAAA,MAAC;AAAC,MAAAe,GAAE,WAAWzB,GAAE,UAASyB,GAAE,kBAAkB,KAAK;AAAA,IAAC;AAAA,EAAC,GAAE,eAAef,IAAEC,IAAE;AAAC,QAAIC,KAAE,SAAG,EAAC,IAAGF,IAAE,SAAQC,GAAC,CAAC;AAAE,QAAGc,GAAE,QAAQ,OAAM;AAAC,MAAAnB,GAAE,MAAM,KAAKM,EAAC;AAAE;AAAA,IAAM;AAAC,IAAAW,MAAG,qBAAqBA,EAAC;AAAE,QAAIV,KAAEJ,GAAE,CAAAO,QAAIA,GAAE,KAAKJ,EAAC,GAAEI,GAAE;AAAE,IAAAT,GAAE,UAAQ,QAAMkB,GAAE,WAAWd,GAAE,MAAM,KAAK,MAAIE,GAAE,oBAAkBA,GAAE,QAAQ,QAAQD,EAAC,IAAGN,GAAE,QAAMO,GAAE,SAAQN,GAAE,QAAMM,GAAE,mBAAkB,EAAE,QAAM,GAAEA,GAAE,QAAQ,KAAK,CAAAG,OAAG,CAACf,GAAEe,GAAE,QAAQ,MAAM,CAAC,MAAIO,KAAE,sBAAsB,MAAI;AAAC,UAAIP,KAAEP,GAAE;AAAE,MAAAH,GAAE,QAAMU,GAAE,SAAQT,GAAE,QAAMS,GAAE;AAAA,IAAiB,CAAC;AAAA,EAAE,GAAE,iBAAiBN,IAAEC,IAAE;AAAC,QAAGW,OAAI,QAAM,qBAAqBA,EAAC,GAAEX,OAAIH,GAAE,QAAM,OAAIiB,GAAE,QAAQ,OAAM;AAAC,MAAAnB,GAAE,QAAMA,GAAE,MAAM,OAAO,CAAAO,OAAGA,GAAE,OAAKH,EAAC;AAAE;AAAA,IAAM;AAAC,QAAIE,KAAEH,GAAE,CAAAI,OAAG;AAAC,UAAIG,KAAEH,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKJ,EAAC;AAAE,aAAOM,OAAI,MAAIH,GAAE,OAAOG,IAAE,CAAC,GAAEH;AAAA,IAAC,CAAC;AAAE,IAAAP,GAAE,QAAMM,GAAE,SAAQL,GAAE,QAAMK,GAAE,mBAAkB,EAAE,QAAM;AAAA,EAAC,GAAE,WAAWF,IAAE;AAAC,WAAO,EAAEO,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIQ,GAAE,QAAQ,MAAEA,GAAE,MAAM,KAAK,GAAE,MAAEf,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,MAAEe,GAAE,MAAM,KAAK,EAAE,KAAK,CAAAd,OAAGc,GAAE,QAAQ,MAAEd,EAAC,GAAE,MAAED,EAAC,CAAC,CAAC,EAAC,CAAC;AAAA,EAAC,GAAE,SAASA,IAAE;AAAC,WAAOH,GAAE,UAAQkB,GAAE,eAAef,EAAC;AAAA,EAAC,EAAC;AAAE,EAAAD,GAAG,CAACL,IAAEJ,IAAEE,EAAC,GAAE,MAAIuB,GAAE,cAAc,GAAE,SAAE,MAAIxB,GAAE,UAAQ,CAAC,CAAC,GAAE,QAAG,IAAGwB,EAAC,GAAEf,GAAG,SAAE,MAAI,EAAET,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEsB,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIM,KAAE,SAAE,MAAI;AAAC,QAAInB;AAAE,YAAOA,KAAET,GAAEG,EAAC,MAAI,OAAK,SAAOM,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAO,UAAE,MAAI;AAAC,UAAE,CAACmB,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAOjC,GAAE,iBAAe,OAAO;AAAO,eAASc,KAAG;AAAC,QAAAe,GAAE,OAAO7B,GAAE,YAAY;AAAA,MAAC;AAAC,aAAOiC,GAAE,MAAM,iBAAiB,SAAQnB,EAAC,GAAE,MAAI;AAAC,YAAIC;AAAE,SAACA,KAAEkB,GAAE,UAAQ,QAAMlB,GAAE,oBAAoB,SAAQD,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAII,IAAEC,IAAEY;AAAE,QAAG,EAAC,MAAKjB,IAAE,UAASC,IAAE,MAAKC,IAAE,GAAGC,GAAC,IAAEjB,IAAEoB,KAAE,EAAC,MAAKf,GAAE,UAAQ,GAAE,UAASU,IAAE,aAAYc,GAAE,kBAAkB,OAAM,cAAaA,GAAE,kBAAkB,UAAQ,OAAK,OAAKA,GAAE,QAAQ,QAAMA,GAAE,QAAQ,MAAM,SAASX,KAAEW,GAAE,kBAAkB,UAAQ,OAAKX,KAAE,CAAC,KAAGa,MAAGZ,KAAEU,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOV,GAAE,QAAQ,UAAQ,OAAKY,KAAE,MAAK,OAAMN,GAAE,MAAK;AAAE,WAAO,EAAE,UAAG,CAAC,GAAGX,MAAG,QAAMW,GAAE,SAAO,OAAK,EAAG,EAAC,CAACX,EAAC,GAAEW,GAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACO,IAAEE,GAAE,MAAI,EAAE,GAAGtB,GAAG,EAAC,UAASL,GAAG,QAAO,KAAIyB,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKhB,IAAE,UAASD,IAAE,MAAKiB,IAAE,OAAME,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,YAAW,EAAC,GAAGhC,IAAE,GAAG4B,GAAEb,IAAE,CAAC,MAAK,gBAAe,aAAY,cAAa,YAAW,YAAW,uBAAsB,SAAS,CAAC,EAAC,GAAE,UAAS,CAAC,GAAE,MAAKG,IAAE,OAAMnB,IAAE,OAAMC,IAAE,MAAK,WAAU,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAzsO,IAA2sO,KAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMF,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIL,MAAGK,KAAER,GAAE,OAAK,OAAKQ,KAAE,6BAA6BmB,GAAE,CAAC,IAAGtB,KAAE,EAAE,eAAe;AAAE,WAASE,KAAG;AAAC,QAAIH;AAAE,KAACA,KAAEC,GAAEA,GAAE,QAAQ,MAAI,QAAMD,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIA,KAAE,EAAC,MAAKC,GAAE,cAAc,UAAQ,GAAE,UAASA,GAAE,SAAS,MAAK,GAAE,EAAC,GAAGC,GAAC,IAAEN,IAAES,KAAE,EAAC,IAAGN,IAAE,KAAIE,GAAE,UAAS,SAAQE,GAAC;AAAE,WAAO,EAAE,EAAC,UAASE,IAAE,YAAWH,IAAE,MAAKF,IAAE,OAAMH,IAAE,OAAMC,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvrP,IAAyrP,KAAG,gBAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMF,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIJ,MAAGI,KAAET,GAAE,OAAK,OAAKS,KAAE,8BAA8BkB,GAAE,CAAC,IAAGpB,KAAE,EAAE,gBAAgB;AAAE,EAAAJ,GAAE,EAAC,IAAGI,GAAE,WAAU,KAAIA,GAAE,UAAS,CAAC;AAAE,WAASC,GAAEE,IAAE;AAAC,IAAAH,GAAE,SAAS,UAAQA,GAAE,cAAc,UAAQ,IAAEA,GAAE,cAAc,KAAGG,GAAE,eAAe,GAAEH,GAAE,aAAa,IAAG,SAAE,MAAI;AAAC,UAAII;AAAE,cAAOA,KAAEN,GAAEE,GAAE,QAAQ,MAAI,OAAK,SAAOI,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,WAASP,GAAEM,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKL,GAAE;AAAU,QAAAK,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEH,GAAE,cAAc,UAAQ,KAAGA,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,cAAII;AAAE,kBAAOA,KAAEJ,GAAE,SAAS,UAAQ,OAAK,SAAOI,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAO,KAAKN,GAAE;AAAQ,QAAAK,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEH,GAAE,cAAc,UAAQ,MAAIA,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,UAAAA,GAAE,MAAM,SAAOA,GAAE,WAAWH,GAAE,IAAI;AAAA,QAAC,CAAC,IAAG,SAAE,MAAI;AAAC,cAAIO;AAAE,kBAAOA,KAAEJ,GAAE,SAAS,UAAQ,OAAK,SAAOI,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAO,KAAKN,GAAE;AAAO,YAAGE,GAAE,cAAc,UAAQ,EAAE;AAAO,QAAAG,GAAE,eAAe,GAAEH,GAAE,WAAW,SAAO,CAACA,GAAE,gBAAgB,MAAM,UAAQG,GAAE,gBAAgB,GAAEH,GAAE,cAAc,GAAE,SAAE,MAAI;AAAC,cAAII;AAAE,kBAAOA,KAAEJ,GAAE,SAAS,UAAQ,OAAK,SAAOI,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,IAAM;AAAA,EAAC;AAAC,MAAIL,KAAEU,GAAG,SAAE,OAAK,EAAC,IAAGhB,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEM,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIK,IAAEC;AAAE,QAAIH,KAAE,EAAC,MAAKH,GAAE,cAAc,UAAQ,GAAE,UAASA,GAAE,SAAS,OAAM,OAAMA,GAAE,MAAM,MAAK,GAAE,EAAC,GAAGI,GAAC,IAAEX,IAAE,IAAE,EAAC,KAAIO,GAAE,WAAU,IAAGF,IAAE,MAAKC,GAAE,OAAM,UAAS,MAAK,iBAAgB,WAAU,kBAAiBM,KAAEP,GAAEE,GAAE,UAAU,MAAI,OAAK,SAAOK,GAAE,IAAG,iBAAgBL,GAAE,cAAc,UAAQ,GAAE,mBAAkBA,GAAE,SAAS,QAAM,EAAEM,KAAER,GAAEE,GAAE,QAAQ,MAAI,OAAK,SAAOM,GAAE,IAAGR,EAAC,EAAE,KAAK,GAAG,IAAE,QAAO,UAASE,GAAE,SAAS,UAAQ,OAAG,OAAG,QAAO,WAAUH,IAAE,SAAQI,GAAC;AAAE,WAAO,EAAE,EAAC,UAAS,GAAE,YAAWG,IAAE,MAAKD,IAAE,OAAMT,IAAE,OAAMC,IAAE,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA34S,IAA64S,KAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,cAAa,EAAC,MAAK,SAAQ,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,OAAM,EAAC,QAAO,CAAAF,OAAG,KAAE,GAAE,MAAMA,IAAE,EAAC,MAAKC,IAAE,OAAMC,IAAE,OAAMC,IAAE,QAAOE,GAAC,GAAE;AAAC,MAAIqB;AAAE,MAAInB,MAAGmB,KAAE1B,GAAE,OAAK,OAAK0B,KAAE,6BAA6BC,GAAE,CAAC,IAAGnB,KAAE,EAAE,eAAe,GAAEJ,KAAE,SAAE,MAAIuB,GAAGtB,GAAEG,GAAE,QAAQ,CAAC,CAAC,GAAEF,KAAE,EAAC,OAAM,MAAE;AAAE,EAAAD,GAAE,EAAC,IAAGG,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC;AAAE,WAASC,KAAG;AAAC,IAAAD,GAAE,OAAO,IAAI;AAAE,QAAImB,KAAEtB,GAAEG,GAAE,UAAU;AAAE,IAAAmB,OAAIA,GAAE,YAAU,IAAGnB,GAAE,WAAWJ,GAAE,OAAO;AAAA,EAAC;AAAC,MAAIM,KAAE,SAAE,MAAI;AAAC,QAAIkB;AAAE,QAAID,KAAEnB,GAAE,MAAM;AAAM,WAAOH,GAAEG,GAAE,QAAQ,IAAE,OAAOR,GAAE,gBAAc,eAAa2B,OAAI,UAAQC,KAAE5B,GAAE,aAAa2B,EAAC,MAAI,OAAKC,KAAE,KAAG,OAAOD,MAAG,WAASA,KAAE,KAAG;AAAA,EAAE,CAAC;AAAE,YAAE,MAAI;AAAC,UAAE,CAACjB,IAAEF,GAAE,eAAcJ,EAAC,GAAE,CAAC,CAACuB,IAAEC,EAAC,GAAE,CAACE,IAAED,EAAC,MAAI;AAAC,UAAGvB,GAAE,MAAM;AAAO,UAAI2B,KAAE5B,GAAEG,GAAE,QAAQ;AAAE,MAAAyB,QAAKJ,OAAI,KAAGD,OAAI,KAAGD,OAAIG,QAAKG,GAAE,QAAMN,KAAG,sBAAsB,MAAI;AAAC,YAAIX;AAAE,YAAGV,GAAE,SAAO,CAAC2B,QAAKjB,KAAEZ,GAAE,UAAQ,OAAK,SAAOY,GAAE,mBAAiBiB,GAAE;AAAO,YAAG,EAAC,gBAAenB,IAAE,cAAaC,GAAC,IAAEkB;AAAE,aAAK,KAAKlB,MAAG,OAAKA,KAAE,MAAID,MAAG,OAAKA,KAAE,EAAE,MAAI,KAAGA,OAAI,KAAGmB,GAAE,kBAAkBA,GAAE,MAAM,QAAOA,GAAE,MAAM,MAAM;AAAA,MAAC,CAAC;AAAA,IAAE,GAAE,EAAC,WAAU,KAAE,CAAC,GAAE,MAAE,CAACzB,GAAE,aAAa,GAAE,CAAC,CAACmB,EAAC,GAAE,CAACC,EAAC,MAAI;AAAC,UAAGD,OAAI,KAAGC,OAAI,GAAE;AAAC,YAAGtB,GAAE,MAAM;AAAO,YAAIwB,KAAEzB,GAAEG,GAAE,QAAQ;AAAE,YAAG,CAACsB,GAAE;AAAO,YAAID,KAAEC,GAAE,OAAM,EAAC,gBAAeG,IAAE,cAAanB,IAAE,oBAAmBC,GAAC,IAAEe;AAAE,QAAAA,GAAE,QAAM,IAAGA,GAAE,QAAMD,IAAEd,OAAI,OAAKe,GAAE,kBAAkBG,IAAEnB,IAAEC,EAAC,IAAEe,GAAE,kBAAkBG,IAAEnB,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,MAAIH,KAAE,IAAE,KAAE;AAAE,WAAS,IAAG;AAAC,IAAAA,GAAE,QAAM;AAAA,EAAE;AAAC,WAASC,KAAG;AAAC,MAAG,EAAE,UAAU,MAAI;AAAC,MAAAD,GAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAC;AAAC,MAAIE,KAAEC,GAAG;AAAE,WAASO,GAAEM,IAAE;AAAC,YAAOrB,GAAE,QAAM,MAAGO,GAAE,MAAI;AAAC,MAAAP,GAAE,QAAM;AAAA,IAAE,CAAC,GAAEqB,GAAE,KAAI;AAAA,MAAC,KAAKtB,GAAE;AAAM,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,KAAGG,GAAE,MAAM;AAAO,YAAGgB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnB,GAAE,kBAAkB,UAAQ,MAAK;AAAC,UAAAA,GAAE,cAAc;AAAE;AAAA,QAAM;AAAC,QAAAA,GAAE,mBAAmB,GAAEA,GAAE,KAAK,UAAQ,KAAGA,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKH,GAAE;AAAU,eAAOC,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEnB,GAAE,cAAc,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,WAAWJ,GAAE,IAAI,GAAE,CAAC,CAAC,GAAE,MAAII,GAAE,aAAa,EAAC,CAAC;AAAA,MAAE,KAAKH,GAAE;AAAQ,eAAOC,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEnB,GAAE,cAAc,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,WAAWJ,GAAE,QAAQ,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAAI,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,YAAAA,GAAE,MAAM,SAAOA,GAAE,WAAWJ,GAAE,IAAI;AAAA,UAAC,CAAC;AAAA,QAAC,EAAC,CAAC;AAAA,MAAE,KAAKC,GAAE;AAAK,YAAGsB,GAAE,SAAS;AAAM,eAAOrB,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnB,GAAE,WAAWJ,GAAE,KAAK;AAAA,MAAE,KAAKC,GAAE;AAAO,eAAOC,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnB,GAAE,WAAWJ,GAAE,KAAK;AAAA,MAAE,KAAKC,GAAE;AAAI,YAAGsB,GAAE,SAAS;AAAM,eAAOrB,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnB,GAAE,WAAWJ,GAAE,IAAI;AAAA,MAAE,KAAKC,GAAE;AAAS,eAAOC,GAAE,QAAM,OAAGqB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnB,GAAE,WAAWJ,GAAE,IAAI;AAAA,MAAE,KAAKC,GAAE;AAAO,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,EAAE;AAAO,QAAAmB,GAAE,eAAe,GAAEnB,GAAE,WAAW,SAAO,CAACA,GAAE,gBAAgB,MAAM,UAAQmB,GAAE,gBAAgB,GAAEnB,GAAE,SAAS,SAAOA,GAAE,KAAK,UAAQ,KAAGA,GAAE,MAAM,UAAQ,QAAMC,GAAE,GAAED,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKH,GAAE;AAAI,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,EAAE;AAAO,QAAAA,GAAE,KAAK,UAAQ,KAAGA,GAAE,kBAAkB,UAAQ,KAAGA,GAAE,mBAAmB,GAAEA,GAAE,cAAc;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASc,GAAEK,IAAE;AAAC,IAAA1B,GAAE,UAAS0B,EAAC,GAAEnB,GAAE,SAAS,SAAOA,GAAE,KAAK,UAAQ,KAAGmB,GAAE,OAAO,UAAQ,MAAIlB,GAAE,GAAED,GAAE,aAAa;AAAA,EAAC;AAAC,WAASe,GAAEI,IAAE;AAAC,QAAIG,IAAED,IAAEI;AAAE,QAAIL,MAAGE,KAAEH,GAAE,kBAAgB,OAAKG,KAAEhB,GAAG,KAAK,CAAAA,OAAGA,OAAIa,GAAE,aAAa;AAAE,QAAGrB,GAAE,QAAM,OAAG,GAAGuB,KAAExB,GAAEG,GAAE,UAAU,MAAI,QAAMqB,GAAE,SAASD,EAAC,MAAI,GAAGK,KAAE5B,GAAEG,GAAE,SAAS,MAAI,QAAMyB,GAAE,SAASL,EAAC,MAAIpB,GAAE,cAAc,UAAQ,EAAE,QAAOmB,GAAE,eAAe,GAAEnB,GAAE,KAAK,UAAQ,MAAIA,GAAE,SAAS,SAAOA,GAAE,MAAM,UAAQ,OAAKC,GAAE,IAAED,GAAE,kBAAkB,UAAQ,KAAGA,GAAE,mBAAmB,IAAGA,GAAE,cAAc;AAAA,EAAC;AAAC,WAASgB,GAAEG,IAAE;AAAC,QAAIG,IAAED,IAAEI;AAAE,QAAIL,MAAGE,KAAEH,GAAE,kBAAgB,OAAKG,KAAEhB,GAAG,KAAK,CAAAA,OAAGA,OAAIa,GAAE,aAAa;AAAE,KAACE,KAAExB,GAAEG,GAAE,SAAS,MAAI,QAAMqB,GAAE,SAASD,EAAC,MAAIK,KAAE5B,GAAEG,GAAE,UAAU,MAAI,QAAMyB,GAAE,SAASL,EAAC,KAAGpB,GAAE,SAAS,SAAOA,GAAE,UAAU,SAAOA,GAAE,cAAc,UAAQ,MAAIA,GAAE,aAAa,GAAE,EAAG,EAAE,UAAU,MAAI;AAAC,MAAAA,GAAE,qBAAqB,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,MAAIiB,KAAE,SAAE,MAAI;AAAC,QAAIE,IAAEC,IAAEE,IAAED;AAAE,YAAOA,MAAGC,MAAGF,KAAE5B,GAAE,iBAAe,OAAK4B,KAAEpB,GAAE,aAAa,UAAQ,UAAQmB,KAAE3B,GAAE,iBAAe,OAAK,SAAO2B,GAAE,KAAK3B,IAAEQ,GAAE,aAAa,KAAK,IAAE,SAAO,OAAKsB,KAAEtB,GAAE,aAAa,UAAQ,OAAKqB,KAAE;AAAA,EAAE,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIf,IAAEC,IAAEC,IAAEC,IAAEG,IAAEF,IAAEC;AAAE,QAAIQ,KAAE,EAAC,MAAKnB,GAAE,cAAc,UAAQ,EAAC,GAAE,EAAC,cAAaoB,IAAE,UAASE,IAAE,GAAGD,GAAC,IAAE7B,IAAEiC,KAAE,EAAC,kBAAiBnB,KAAEN,GAAE,WAAW,UAAQ,OAAK,SAAOM,GAAE,IAAG,iBAAgBN,GAAE,cAAc,UAAQ,GAAE,yBAAwBA,GAAE,kBAAkB,UAAQ,OAAK,SAAOA,GAAE,QAAQ,SAAOO,KAAEP,GAAE,QAAQ,MAAM,KAAK,CAAAuB,OAAG,CAACvB,GAAE,QAAQ,MAAM,SAASuB,GAAE,QAAQ,KAAK,KAAGvB,GAAE,QAAQuB,GAAE,QAAQ,OAAMvB,GAAE,QAAQ,MAAM,QAAQA,GAAE,kBAAkB,KAAK,CAAC,CAAC,MAAI,OAAK,SAAOO,GAAE,MAAIC,KAAER,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOQ,GAAE,IAAG,oBAAmBE,MAAGD,KAAEZ,GAAEG,GAAE,QAAQ,MAAI,OAAK,SAAOS,GAAE,OAAK,OAAKC,MAAGE,KAAEf,GAAEG,GAAE,SAAS,MAAI,OAAK,SAAOY,GAAE,IAAG,qBAAoB,QAAO,IAAGb,IAAE,oBAAmB,GAAE,kBAAiBK,IAAE,WAAUS,IAAE,SAAQC,IAAE,SAAQE,IAAE,QAAOD,IAAE,MAAK,YAAW,OAAMJ,KAAEjB,GAAE,SAAO,OAAKiB,KAAE,QAAO,UAAS,GAAE,KAAIX,GAAE,UAAS,cAAaiB,GAAE,OAAM,UAASjB,GAAE,SAAS,UAAQ,OAAG,OAAG,OAAM;AAAE,WAAO,EAAE,EAAC,UAASyB,IAAE,YAAWJ,IAAE,MAAKF,IAAE,OAAMzB,IAAE,OAAMC,IAAE,UAASgC,GAAE,iBAAeA,GAAE,QAAO,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvrc,IAAyrc,KAAG,gBAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,MAAMnC,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIE,KAAE,EAAE,iBAAiB,GAAEE,KAAE,+BAA+BoB,GAAE,CAAC;AAAG,EAAAxB,GAAE,EAAC,IAAGE,GAAE,YAAW,KAAIA,GAAE,WAAU,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAA,GAAE,gBAAgB,MAAM,SAAOL,GAAE;AAAA,EAAM,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAK,GAAE,gBAAgB,MAAM,OAAKL,GAAE;AAAA,EAAI,CAAC;AAAE,MAAIQ,KAAE,EAAG,GAAEJ,KAAE,SAAE,MAAII,OAAI,QAAMA,GAAE,QAAMmB,GAAE,UAAQA,GAAE,OAAKtB,GAAE,cAAc,UAAQ,CAAC;AAAE,EAAAsB,GAAG,EAAC,WAAU,SAAE,MAAItB,GAAEA,GAAE,UAAU,CAAC,GAAE,SAAQ,SAAE,MAAIA,GAAE,cAAc,UAAQ,CAAC,GAAE,OAAOI,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,WAAS,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASH,GAAEG,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAI,GAAEG,IAAEC;AAAE,QAAIJ,KAAE,EAAC,MAAKJ,GAAE,cAAc,UAAQ,EAAC,GAAEK,KAAE,EAAC,oBAAmBG,MAAG,IAAER,GAAEA,GAAE,QAAQ,MAAI,OAAK,SAAO,EAAE,OAAK,OAAKQ,MAAGD,KAAEP,GAAEA,GAAE,SAAS,MAAI,OAAK,SAAOO,GAAE,IAAG,IAAGL,IAAE,KAAIF,GAAE,YAAW,MAAK,WAAU,wBAAuBA,GAAE,KAAK,UAAQ,IAAE,OAAG,QAAO,aAAYC,GAAC,GAAEK,KAAEmB,GAAE9B,IAAE,CAAC,MAAM,CAAC;AAAE,WAAO,EAAE,EAAC,UAASU,IAAE,YAAWC,IAAE,MAAKF,IAAE,OAAMR,IAAE,OAAMI,GAAE,QAAQ,SAAOA,GAAE,cAAc,UAAQ,IAAE,EAAC,GAAGH,IAAE,SAAQ,MAAI,CAAC,EAAE,IAAG,CAAC,GAAEA,GAAE,OAAO,CAAC,EAAC,IAAEA,IAAE,UAASiC,GAAE,iBAAeA,GAAE,QAAO,SAAQ/B,GAAE,OAAM,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAt8e,IAAw8e,KAAG,gBAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIE,KAAE,EAAE,gBAAgB,GAAEE,KAAE,8BAA8BoB,GAAE,CAAC,IAAGnB,KAAE,IAAE,IAAI,GAAEJ,KAAE,SAAE,MAAIJ,GAAE,QAAQ;AAAE,EAAAG,GAAE,EAAC,IAAGK,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIF,KAAE,SAAE,MAAI;AAAC,QAAIkB;AAAE,WAAOnB,GAAE,QAAQ,QAAMA,GAAE,kBAAkB,UAAQA,GAAE,eAAeL,GAAE,KAAK,IAAEK,GAAE,kBAAkB,UAAQ,OAAK,UAAKmB,KAAEnB,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOmB,GAAE,QAAMjB;AAAA,EAAC,CAAC,GAAEE,KAAE,SAAE,MAAIJ,GAAE,WAAWL,GAAE,KAAK,CAAC,GAAEU,KAAE,OAAG,IAAG,IAAI,GAAEC,KAAE,SAAE,OAAK,EAAC,UAASX,GAAE,UAAS,OAAMA,GAAE,OAAM,QAAOQ,IAAE,OAAM,SAAE,MAAIR,GAAE,KAAK,EAAC,EAAE;AAAE,YAAE,MAAIK,GAAE,eAAeE,IAAEI,EAAC,CAAC,GAAE,YAAG,MAAIN,GAAE,iBAAiBE,IAAED,GAAE,KAAK,CAAC,GAAE,YAAE,MAAI;AAAC,QAAIkB,KAAEnB,GAAEG,EAAC;AAAE,IAAAgB,OAAId,MAAG,QAAMA,GAAE,MAAM,eAAec,EAAC;AAAA,EAAE,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAnB,GAAE,cAAc,UAAQ,KAAGC,GAAE,UAAQD,GAAE,QAAQ,SAAOA,GAAE,kBAAkB,UAAQ,KAAG,SAAE,MAAI;AAAC,UAAImB,IAAEC;AAAE,cAAOA,MAAGD,KAAEnB,GAAEG,EAAC,MAAI,OAAK,SAAOgB,GAAE,mBAAiB,OAAK,SAAOC,GAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE,CAAC;AAAE,WAAS,EAAEA,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAEA,GAAE,WAAS,EAAG,SAAOpB,GAAE,UAAQC,GAAE,aAAaE,EAAC,GAAEQ,GAAG,KAAG,sBAAsB,MAAI;AAAC,UAAIU;AAAE,cAAOA,KAAEpB,GAAEA,GAAE,QAAQ,MAAI,OAAK,SAAOoB,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,GAAEpB,GAAE,KAAK,UAAQ,KAAGA,GAAE,cAAc;AAAA,EAAG;AAAC,WAASO,KAAG;AAAC,QAAIa;AAAE,QAAGzB,GAAE,aAAWyB,KAAEpB,GAAE,QAAQ,UAAQ,QAAMoB,GAAE,SAASzB,GAAE,KAAK,EAAE,QAAOK,GAAE,WAAWD,GAAE,OAAO;AAAE,QAAIoB,KAAEnB,GAAE,eAAeL,GAAE,KAAK;AAAE,IAAAK,GAAE,WAAWD,GAAE,UAASoB,EAAC;AAAA,EAAC;AAAC,MAAIX,KAAEN,GAAG;AAAE,WAASc,GAAEG,IAAE;AAAC,IAAAX,GAAE,OAAOW,EAAC;AAAA,EAAC;AAAC,WAASF,GAAEE,IAAE;AAAC,QAAIE;AAAE,QAAG,CAACb,GAAE,SAASW,EAAC,KAAGxB,GAAE,aAAW0B,KAAErB,GAAE,QAAQ,UAAQ,QAAMqB,GAAE,SAAS1B,GAAE,KAAK,KAAGM,GAAE,MAAM;AAAO,QAAImB,KAAEpB,GAAE,eAAeL,GAAE,KAAK;AAAE,IAAAK,GAAE,WAAWD,GAAE,UAASqB,IAAE,CAAC;AAAA,EAAC;AAAC,WAASF,GAAEC,IAAE;AAAC,QAAIC;AAAE,IAAAZ,GAAE,SAASW,EAAC,MAAIxB,GAAE,aAAWyB,KAAEpB,GAAE,QAAQ,UAAQ,QAAMoB,GAAE,SAASzB,GAAE,KAAK,KAAGM,GAAE,UAAQD,GAAE,gBAAgB,MAAM,QAAMA,GAAE,WAAWD,GAAE,OAAO;AAAA,EAAG;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAASoB,GAAC,IAAExB,IAAEyB,KAAE,EAAC,QAAOnB,GAAE,OAAM,UAASG,GAAE,OAAM,UAASe,GAAC,GAAEE,KAAE,EAAC,IAAGnB,IAAE,KAAIC,IAAE,MAAK,UAAS,UAASgB,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,iBAAgBf,GAAE,OAAM,UAAS,QAAO,aAAY,GAAE,SAAQG,IAAE,gBAAeS,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC,GAAEI,KAAEG,GAAE9B,IAAE,CAAC,SAAQ,OAAO,CAAC;AAAE,WAAO,EAAE,EAAC,UAAS0B,IAAE,YAAWC,IAAE,MAAKF,IAAE,OAAMvB,IAAE,OAAMD,IAAE,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACAzjnB,SAASmC,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAE,YAAU,YAAE,CAAAC,OAAG;AAAC,IAAAJ,KAAEA,MAAG,OAAKA,KAAE,QAAOA,GAAE,iBAAiBC,IAAEC,IAAEC,EAAC,GAAEC,GAAE,MAAIJ,GAAE,oBAAoBC,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ACA/G,IAAIE,MAAG,CAAAC,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,MAAIC,KAAE,IAAE,CAAC;AAAE,SAAOC,GAAE,WAAU,CAAAC,OAAG;AAAC,IAAAA,GAAE,QAAM,UAAQF,GAAE,QAAME,GAAE,WAAS,IAAE;AAAA,EAAE,CAAC,GAAEF;AAAC;;;ACAugB,SAAS,EAAEG,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO,oBAAI;AAAI,MAAG,OAAOA,MAAG,WAAW,QAAO,IAAI,IAAIA,GAAE,CAAC;AAAE,MAAIC,KAAE,oBAAI;AAAI,WAAQC,MAAKF,GAAE,OAAM;AAAC,QAAIG,KAAEC,GAAEF,EAAC;AAAE,IAAAC,cAAa,eAAaF,GAAE,IAAIE,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,IAAII,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,eAAa,EAAE,IAAE,gBAAeA,GAAEA,GAAE,MAAI,EAAE,IAAE,OAAMA,KAAID,MAAG,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,OAAO,gBAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQ,IAAE,oBAAI,KAAG,EAAC,EAAC,GAAE,cAAa,OAAG,MAAML,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,IAAI;AAAE,EAAAD,GAAE,EAAC,IAAGC,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIG,KAAE,SAAE,MAAIA,GAAEH,EAAC,CAAC,GAAEE,KAAE,IAAE,KAAE;AAAE,YAAE,MAAIA,GAAE,QAAM,IAAE,GAAE,YAAE,MAAIA,GAAE,QAAM,KAAE,GAAE,EAAE,EAAC,eAAcC,GAAC,GAAE,SAAE,MAAID,GAAE,SAAO,QAAQN,GAAE,WAAS,EAAE,CAAC,CAAC;AAAE,MAAIQ,KAAE,EAAE,EAAC,eAAcD,IAAE,WAAUH,IAAE,cAAa,SAAE,MAAIJ,GAAE,YAAY,EAAC,GAAE,SAAE,MAAIM,GAAE,SAAO,QAAQN,GAAE,WAAS,CAAC,CAAC,CAAC;AAAE,IAAE,EAAC,eAAcO,IAAE,WAAUH,IAAE,YAAWJ,GAAE,YAAW,uBAAsBQ,GAAC,GAAE,SAAE,MAAIF,GAAE,SAAO,QAAQN,GAAE,WAAS,CAAC,CAAC,CAAC;AAAE,MAAIS,KAAER,GAAE;AAAE,WAASS,GAAEC,IAAE;AAAC,QAAIC,KAAER,GAAEA,EAAC;AAAE,QAAG,CAACQ,GAAE;AAAO,KAAC,CAAAC,OAAGA,GAAE,GAAG,MAAI;AAAC,QAAEJ,GAAE,OAAM,EAAC,CAACK,GAAE,QAAQ,GAAE,MAAI;AAAC,UAAEF,IAAE,EAAE,OAAM,EAAC,cAAa,CAACD,GAAE,aAAa,EAAC,CAAC;AAAA,MAAC,GAAE,CAACG,GAAE,SAAS,GAAE,MAAI;AAAC,UAAEF,IAAE,EAAE,MAAK,EAAC,cAAa,CAACD,GAAE,aAAa,EAAC,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,MAAII,KAAE,IAAE,KAAE;AAAE,WAASC,GAAEL,IAAE;AAAC,IAAAA,GAAE,QAAM,UAAQI,GAAE,QAAM,MAAG,sBAAsB,MAAI;AAAC,MAAAA,GAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAE;AAAC,WAASE,GAAEN,IAAE;AAAC,QAAG,CAACL,GAAE,MAAM;AAAO,QAAIM,KAAE,EAAEZ,GAAE,UAAU;AAAE,IAAAI,GAAEA,EAAC,aAAY,eAAaQ,GAAE,IAAIR,GAAEA,EAAC,CAAC;AAAE,QAAIU,KAAEH,GAAE;AAAc,IAAAG,cAAa,eAAaA,GAAE,QAAQ,yBAAuB,WAASI,GAAEN,IAAEE,EAAC,MAAIC,GAAE,QAAM,EAAEX,GAAEA,EAAC,GAAE,EAAEK,GAAE,OAAM,EAAC,CAACK,GAAE,QAAQ,GAAE,MAAI,EAAE,MAAK,CAACA,GAAE,SAAS,GAAE,MAAI,EAAE,SAAQ,CAAC,IAAE,EAAE,YAAW,EAAC,YAAWH,GAAE,OAAM,CAAC,IAAEA,GAAE,kBAAkB,eAAa,EAAEA,GAAE,MAAM;AAAA,EAAG;AAAC,SAAM,MAAI;AAAC,QAAIA,KAAE,CAAC,GAAEC,KAAE,EAAC,KAAIR,IAAE,WAAUY,IAAE,YAAWC,GAAC,GAAE,EAAC,UAASH,IAAE,cAAaD,IAAE,YAAWM,IAAE,GAAGC,GAAC,IAAEpB;AAAE,WAAO,EAAE,UAAE,CAAC,QAAQc,KAAE,CAAC,KAAG,EAAE,GAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQJ,IAAE,UAASC,GAAE,UAAS,CAAC,GAAE,EAAE,EAAC,UAASC,IAAE,YAAW,EAAC,GAAGX,IAAE,GAAGmB,GAAC,GAAE,MAAKT,IAAE,OAAMV,IAAE,OAAMC,IAAE,MAAK,YAAW,CAAC,GAAE,QAAQY,KAAE,CAAC,KAAG,EAAE,GAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQJ,IAAE,UAASC,GAAE,UAAS,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC,GAAE,EAAC,UAASN,GAAC,CAAC;AAAE,SAAS,EAAEL,IAAE;AAAC,MAAIC,KAAE,IAAED,GAAE,MAAM,CAAC;AAAE,SAAO,MAAE,CAACA,EAAC,GAAE,CAAC,CAACE,EAAC,GAAE,CAACC,EAAC,MAAI;AAAC,IAAAA,OAAI,QAAID,OAAI,QAAG,EAAE,MAAI;AAAC,MAAAD,GAAE,MAAM,OAAO,CAAC;AAAA,IAAC,CAAC,IAAEE,OAAI,SAAID,OAAI,SAAKD,GAAE,QAAMD,GAAE,MAAM;AAAA,EAAE,GAAE,EAAC,OAAM,OAAM,CAAC,GAAE,MAAI;AAAC,QAAIE;AAAE,YAAOA,KAAED,GAAE,MAAM,KAAK,CAAAE,OAAGA,MAAG,QAAMA,GAAE,WAAW,MAAI,OAAKD,KAAE;AAAA,EAAI;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcF,GAAC,GAAEC,IAAE;AAAC,MAAIC,KAAE,EAAED,EAAC;AAAE,YAAE,MAAI;AAAC,gBAAE,MAAI;AAAC,UAAIE,IAAEC;AAAE,MAAAH,GAAE,WAASE,KAAEH,GAAE,UAAQ,OAAK,SAAOG,GAAE,qBAAmBC,KAAEJ,GAAE,UAAQ,OAAK,SAAOI,GAAE,SAAO,EAAEF,GAAE,CAAC;AAAA,IAAC,GAAE,EAAC,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAD,GAAE,SAAO,EAAEC,GAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcF,IAAE,WAAUC,IAAE,cAAaC,GAAC,GAAEC,IAAE;AAAC,MAAIC,KAAE,IAAE,IAAI,GAAEG,KAAE,IAAE,KAAE;AAAE,SAAO,UAAE,MAAIA,GAAE,QAAM,IAAE,GAAE,YAAE,MAAIA,GAAE,QAAM,KAAE,GAAE,UAAE,MAAI;AAAC,UAAE,CAACN,IAAEC,IAAEC,EAAC,GAAE,CAACG,IAAEE,OAAI;AAAC,UAAGF,GAAE,MAAM,CAACI,IAAEK,QAAKP,MAAG,OAAK,SAAOA,GAAEO,EAAC,OAAKL,EAAC,KAAG,CAACP,GAAE,MAAM;AAAO,UAAIM,KAAEL,GAAEH,EAAC;AAAE,MAAAQ,MAAG,EAAE,MAAI;AAAC,YAAIO,IAAEC;AAAE,YAAG,CAACV,GAAE,MAAM;AAAO,YAAIG,KAAEN,GAAEF,EAAC,GAAEa,MAAGC,KAAEhB,GAAE,UAAQ,OAAK,SAAOgB,GAAE;AAAc,YAAGN,IAAE;AAAC,cAAGA,OAAIK,IAAE;AAAC,YAAAX,GAAE,QAAMW;AAAE;AAAA,UAAM;AAAA,QAAC,WAASN,GAAE,SAASM,EAAC,GAAE;AAAC,UAAAX,GAAE,QAAMW;AAAE;AAAA,QAAM;AAAC,QAAAL,KAAE,EAAEA,EAAC,IAAE,EAAED,IAAE,EAAE,QAAM,EAAE,QAAQ,MAAI,EAAE,SAAO,QAAQ,KAAK,0DAA0D,GAAEL,GAAE,SAAOa,KAAEjB,GAAE,UAAQ,OAAK,SAAOiB,GAAE;AAAA,MAAa,CAAC;AAAA,IAAC,GAAE,EAAC,WAAU,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAEb;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcJ,IAAE,WAAUC,IAAE,YAAWC,IAAE,uBAAsBC,GAAC,GAAEC,IAAE;AAAC,MAAIG;AAAE,EAAAc,IAAGd,KAAEP,GAAE,UAAQ,OAAK,SAAOO,GAAE,aAAY,SAAQ,CAAAD,OAAG;AAAC,QAAG,CAACF,GAAE,MAAM;AAAO,QAAII,KAAE,EAAEN,EAAC;AAAE,IAAAE,GAAEH,EAAC,aAAY,eAAaO,GAAE,IAAIJ,GAAEH,EAAC,CAAC;AAAE,QAAIQ,KAAEN,GAAE;AAAM,QAAG,CAACM,GAAE;AAAO,QAAIC,KAAEJ,GAAE;AAAO,IAAAI,MAAGA,cAAa,cAAYQ,GAAEV,IAAEE,EAAC,KAAGP,GAAE,QAAMO,IAAE,EAAEA,EAAC,MAAIJ,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEG,EAAC,KAAG,EAAEN,GAAE,KAAK;AAAA,EAAC,GAAE,IAAE;AAAC;AAAC,SAASe,GAAElB,IAAEC,IAAE;AAAC,WAAQC,MAAKF,GAAE,KAAGE,GAAE,SAASD,EAAC,EAAE,QAAM;AAAG,SAAM;AAAE;;;ACA/qI,SAAS,EAAEqB,IAAE;AAAC,MAAIC,KAAE,WAAED,GAAE,YAAY,CAAC;AAAE,SAAO,YAAEA,GAAE,UAAU,MAAI;AAAC,IAAAC,GAAE,QAAMD,GAAE,YAAY;AAAA,EAAC,CAAC,CAAC,GAAEC;AAAC;;;ACA/I,SAAS,EAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,GAAEG,KAAE,oBAAI;AAAI,SAAM,EAAC,cAAa;AAAC,WAAOD;AAAA,EAAC,GAAE,UAAUE,IAAE;AAAC,WAAOD,GAAE,IAAIC,EAAC,GAAE,MAAID,GAAE,OAAOC,EAAC;AAAA,EAAC,GAAE,SAASA,OAAKC,IAAE;AAAC,QAAIC,KAAEL,GAAEG,EAAC,EAAE,KAAKF,IAAE,GAAGG,EAAC;AAAE,IAAAC,OAAIJ,KAAEI,IAAEH,GAAE,QAAQ,CAAAI,OAAGA,GAAE,CAAC;AAAA,EAAE,EAAC;AAAC;;;ACAtL,SAASC,KAAG;AAAC,MAAIC;AAAE,SAAM,EAAC,OAAO,EAAC,KAAIC,GAAC,GAAE;AAAC,QAAIC;AAAE,QAAIC,KAAEF,GAAE;AAAgB,IAAAD,OAAIE,KAAED,GAAE,gBAAc,OAAKC,KAAE,QAAQ,aAAWC,GAAE;AAAA,EAAW,GAAE,MAAM,EAAC,KAAIF,IAAE,GAAEE,GAAC,GAAE;AAAC,QAAIC,KAAEH,GAAE,iBAAgBC,KAAEE,GAAE,cAAYA,GAAE,aAAYC,KAAEL,KAAEE;AAAE,IAAAC,GAAE,MAAMC,IAAE,gBAAe,GAAGC,EAAC,IAAI;AAAA,EAAC,EAAC;AAAC;;;ACAjJ,SAASC,KAAG;AAAC,SAAOC,GAAE,IAAE,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,aAASC,GAAEC,IAAE;AAAC,aAAOF,GAAE,WAAW,QAAQ,CAAAG,OAAGA,GAAE,CAAC,EAAE,KAAK,CAAAA,OAAGA,GAAE,SAASD,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAH,GAAE,UAAU,MAAI;AAAC,UAAIK;AAAE,UAAG,OAAO,iBAAiBN,GAAE,eAAe,EAAE,mBAAiB,QAAO;AAAC,YAAID,KAAE,EAAE;AAAE,QAAAA,GAAE,MAAMC,GAAE,iBAAgB,kBAAiB,MAAM,GAAEC,GAAE,IAAI,MAAIA,GAAE,UAAU,MAAIF,GAAE,QAAQ,CAAC,CAAC;AAAA,MAAC;AAAC,UAAIK,MAAGE,KAAE,OAAO,YAAU,OAAKA,KAAE,OAAO,aAAYD,KAAE;AAAK,MAAAJ,GAAE,iBAAiBD,IAAE,SAAQ,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,YAAY,KAAG;AAAC,cAAIQ,KAAER,GAAE,OAAO,QAAQ,GAAG;AAAE,cAAG,CAACQ,GAAE;AAAO,cAAG,EAAC,MAAKC,GAAC,IAAE,IAAI,IAAID,GAAE,IAAI,GAAEE,KAAET,GAAE,cAAcQ,EAAC;AAAE,UAAAC,MAAG,CAACN,GAAEM,EAAC,MAAIJ,KAAEI;AAAA,QAAE,QAAM;AAAA,QAAC;AAAA,MAAC,GAAE,IAAE,GAAER,GAAE,iBAAiBD,IAAE,cAAa,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,YAAY,KAAGI,GAAEJ,GAAE,MAAM,GAAE;AAAC,cAAIQ,KAAER,GAAE;AAAO,iBAAKQ,GAAE,iBAAeJ,GAAEI,GAAE,aAAa,IAAG,CAAAA,KAAEA,GAAE;AAAc,UAAAN,GAAE,MAAMM,IAAE,sBAAqB,SAAS;AAAA,QAAC,MAAM,CAAAN,GAAE,MAAMF,GAAE,QAAO,eAAc,MAAM;AAAA,MAAC,CAAC,GAAEE,GAAE,iBAAiBD,IAAE,aAAY,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,aAAY;AAAC,cAAGA,GAAE,OAAO,YAAU,QAAQ;AAAO,cAAGI,GAAEJ,GAAE,MAAM,GAAE;AAAC,gBAAIQ,KAAER,GAAE;AAAO,mBAAKQ,GAAE,iBAAeA,GAAE,QAAQ,qBAAmB,MAAI,EAAEA,GAAE,eAAaA,GAAE,gBAAcA,GAAE,cAAYA,GAAE,eAAc,CAAAA,KAAEA,GAAE;AAAc,YAAAA,GAAE,QAAQ,qBAAmB,MAAIR,GAAE,eAAe;AAAA,UAAC,MAAM,CAAAA,GAAE,eAAe;AAAA,QAAC;AAAA,MAAC,GAAE,EAAC,SAAQ,MAAE,CAAC,GAAEE,GAAE,IAAI,MAAI;AAAC,YAAIM;AAAE,YAAIR,MAAGQ,KAAE,OAAO,YAAU,OAAKA,KAAE,OAAO;AAAY,QAAAH,OAAIL,MAAG,OAAO,SAAS,GAAEK,EAAC,GAAEC,MAAGA,GAAE,gBAAcA,GAAE,eAAe,EAAC,OAAM,UAAS,CAAC,GAAEA,KAAE;AAAA,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,IAAE,CAAC;AAAC;;;ACAx7C,SAASK,KAAG;AAAC,SAAM,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,GAAC,GAAE;AAAC,IAAAA,GAAE,MAAMD,GAAE,iBAAgB,YAAW,QAAQ;AAAA,EAAC,EAAC;AAAC;;;ACA0M,SAASE,GAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,WAAQC,MAAKF,GAAE,QAAO,OAAOC,IAAEC,GAAED,EAAC,CAAC;AAAE,SAAOA;AAAC;AAAC,IAAIE,KAAE,EAAE,MAAI,oBAAI,OAAI,EAAC,KAAKH,IAAEC,IAAE;AAAC,MAAIG;AAAE,MAAIF,MAAGE,KAAE,KAAK,IAAIJ,EAAC,MAAI,OAAKI,KAAE,EAAC,KAAIJ,IAAE,OAAM,GAAE,GAAE,EAAE,GAAE,MAAK,oBAAI,MAAG;AAAE,SAAOE,GAAE,SAAQA,GAAE,KAAK,IAAID,EAAC,GAAE,KAAK,IAAID,IAAEE,EAAC,GAAE;AAAI,GAAE,IAAIF,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,IAAIF,EAAC;AAAE,SAAOE,OAAIA,GAAE,SAAQA,GAAE,KAAK,OAAOD,EAAC,IAAG;AAAI,GAAE,eAAe,EAAC,KAAID,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,MAAIE,KAAE,EAAC,KAAIJ,IAAE,GAAEC,IAAE,MAAKF,GAAEG,EAAC,EAAC,GAAEG,KAAE,CAACC,GAAE,GAAED,GAAE,GAAEE,GAAE,CAAC;AAAE,EAAAF,GAAE,QAAQ,CAAC,EAAC,QAAOG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,EAAC,CAAC,GAAEC,GAAE,QAAQ,CAAC,EAAC,OAAMG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,EAAC,CAAC;AAAC,GAAE,aAAa,EAAC,GAAEJ,GAAC,GAAE;AAAC,EAAAA,GAAE,QAAQ;AAAC,GAAE,SAAS,EAAC,KAAIA,GAAC,GAAE;AAAC,OAAK,OAAOA,EAAC;AAAC,EAAC,CAAC;AAAEG,GAAE,UAAU,MAAI;AAAC,MAAIH,KAAEG,GAAE,YAAY,GAAEF,KAAE,oBAAI;AAAI,WAAO,CAACC,EAAC,KAAIF,GAAE,CAAAC,GAAE,IAAIC,IAAEA,GAAE,gBAAgB,MAAM,QAAQ;AAAE,WAAQA,MAAKF,GAAE,OAAO,GAAE;AAAC,QAAII,KAAEH,GAAE,IAAIC,GAAE,GAAG,MAAI,UAASG,KAAEH,GAAE,UAAQ;AAAE,KAACG,MAAG,CAACD,MAAG,CAACC,MAAGD,OAAID,GAAE,SAASD,GAAE,QAAM,IAAE,mBAAiB,gBAAeA,EAAC,GAAEA,GAAE,UAAQ,KAAGC,GAAE,SAAS,YAAWD,EAAC;AAAA,EAAC;AAAC,CAAC;;;ACAt8B,SAASO,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAEF,EAAC,GAAEG,KAAE,SAAE,MAAI;AAAC,QAAIC,KAAEL,GAAE,QAAMG,GAAE,MAAM,IAAIH,GAAE,KAAK,IAAE;AAAO,WAAOK,KAAEA,GAAE,QAAM,IAAE;AAAA,EAAE,CAAC;AAAE,SAAO,MAAE,CAACL,IAAEC,EAAC,GAAE,CAAC,CAACI,IAAEC,EAAC,GAAE,CAACC,EAAC,GAAEC,OAAI;AAAC,QAAG,CAACH,MAAG,CAACC,GAAE;AAAO,IAAAL,GAAE,SAAS,QAAOI,IAAEH,EAAC;AAAE,QAAIO,KAAE;AAAG,IAAAD,GAAE,MAAI;AAAC,MAAAC,OAAIR,GAAE,SAAS,OAAMM,MAAG,OAAKA,KAAEF,IAAEH,EAAC,GAAEO,KAAE;AAAA,IAAG,CAAC;AAAA,EAAC,GAAE,EAAC,WAAU,KAAE,CAAC,GAAEL;AAAC;;;ACA1T,IAAIM,KAAE,oBAAI;AAAV,IAAcC,KAAE,oBAAI;AAAI,SAASC,GAAEC,IAAEC,KAAE,IAAE,IAAE,GAAE;AAAC,cAAE,CAAAC,OAAG;AAAC,QAAIC;AAAE,QAAG,CAACF,GAAE,MAAM;AAAO,QAAIG,KAAEF,GAAEF,EAAC;AAAE,QAAG,CAACI,GAAE;AAAO,IAAAF,GAAE,WAAU;AAAC,UAAIG;AAAE,UAAG,CAACD,GAAE;AAAO,UAAIE,MAAGD,KAAEP,GAAE,IAAIM,EAAC,MAAI,OAAKC,KAAE;AAAE,UAAGC,OAAI,IAAER,GAAE,OAAOM,EAAC,IAAEN,GAAE,IAAIM,IAAEE,KAAE,CAAC,GAAEA,OAAI,EAAE;AAAO,UAAIC,KAAEV,GAAE,IAAIO,EAAC;AAAE,MAAAG,OAAIA,GAAE,aAAa,MAAI,OAAKH,GAAE,gBAAgB,aAAa,IAAEA,GAAE,aAAa,eAAcG,GAAE,aAAa,CAAC,GAAEH,GAAE,QAAMG,GAAE,OAAMV,GAAE,OAAOO,EAAC;AAAA,IAAE,CAAC;AAAE,QAAII,MAAGL,KAAEL,GAAE,IAAIM,EAAC,MAAI,OAAKD,KAAE;AAAE,IAAAL,GAAE,IAAIM,IAAEI,KAAE,CAAC,GAAEA,OAAI,MAAIX,GAAE,IAAIO,IAAE,EAAC,eAAcA,GAAE,aAAa,aAAa,GAAE,OAAMA,GAAE,MAAK,CAAC,GAAEA,GAAE,aAAa,eAAc,MAAM,GAAEA,GAAE,QAAM;AAAA,EAAG,CAAC;AAAC;;;ACA5a,SAASK,GAAE,EAAC,mBAAkBC,KAAE,CAAC,GAAE,SAAQC,IAAE,iBAAgBC,GAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,IAAI,GAAEC,KAAEH,GAAEE,EAAC;AAAE,WAASE,KAAG;AAAC,QAAIC,IAAEC,IAAEC;AAAE,QAAIC,KAAE,CAAC;AAAE,aAAQC,MAAKV,GAAE,CAAAU,OAAI,SAAOA,cAAa,cAAYD,GAAE,KAAKC,EAAC,IAAE,WAAUA,MAAGA,GAAE,iBAAiB,eAAaD,GAAE,KAAKC,GAAE,KAAK;AAAG,QAAGT,MAAG,QAAMA,GAAE,MAAM,UAAQS,MAAKT,GAAE,MAAM,CAAAQ,GAAE,KAAKC,EAAC;AAAE,aAAQA,OAAKJ,KAAEF,MAAG,OAAK,SAAOA,GAAE,iBAAiB,oBAAoB,MAAI,OAAKE,KAAE,CAAC,EAAE,CAAAI,OAAI,SAAS,QAAMA,OAAI,SAAS,QAAMA,cAAa,eAAaA,GAAE,OAAK,6BAA2BA,GAAE,SAASV,GAAEG,EAAC,CAAC,KAAGO,GAAE,UAAUF,MAAGD,KAAEP,GAAEG,EAAC,MAAI,OAAK,SAAOI,GAAE,YAAY,MAAI,OAAK,SAAOC,GAAE,IAAI,KAAGC,GAAE,KAAK,CAAAE,OAAGD,GAAE,SAASC,EAAC,CAAC,KAAGF,GAAE,KAAKC,EAAC;AAAG,WAAOD;AAAA,EAAC;AAAC,SAAM,EAAC,mBAAkBJ,IAAE,SAASI,IAAE;AAAC,WAAOJ,GAAE,EAAE,KAAK,CAAAC,OAAGA,GAAE,SAASG,EAAC,CAAC;AAAA,EAAC,GAAE,iBAAgBN,IAAE,eAAc;AAAC,WAAOD,MAAG,OAAK,OAAK,EAAE,GAAE,EAAC,UAASG,GAAE,QAAO,KAAIF,GAAC,CAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASS,KAAG;AAAC,MAAIZ,KAAE,IAAE,IAAI;AAAE,SAAM,EAAC,iBAAgBA,IAAE,eAAc;AAAC,WAAO,EAAE,GAAE,EAAC,UAASK,GAAE,QAAO,KAAIL,GAAC,CAAC;AAAA,EAAC,EAAC;AAAC;;;ACA/7B,IAAIa,KAAE,OAAO,wBAAwB;AAAE,SAASC,KAAG;AAAC,SAAO,OAAED,IAAE,KAAE;AAAC;AAAC,IAAIE,KAAE,gBAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,OAAM,EAAC,MAAK,SAAQ,SAAQ,MAAE,EAAC,GAAE,MAAMC,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,SAAO,QAAEL,IAAEG,GAAE,KAAK,GAAE,MAAI;AAAC,QAAG,EAAC,OAAMG,IAAE,GAAGC,GAAC,IAAEJ;AAAE,WAAO,EAAE,EAAC,YAAWI,IAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMH,IAAE,OAAMC,IAAE,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA5W,IAAIG,KAAE,OAAO,cAAc;AAAE,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,MAAI,CAAC,IAAE,OAAMA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,SAAO,OAAEH,IAAE,MAAI;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,MAAKI,IAAE,SAAQC,IAAE,SAAQH,IAAE,UAASI,GAAC,GAAE;AAAC,MAAIC,KAAEJ,GAAE;AAAE,WAASK,MAAKC,IAAE;AAAC,IAAAH,MAAG,QAAMA,GAAE,GAAGG,EAAC,GAAEF,GAAE,GAAGE,EAAC;AAAA,EAAC;AAAC,YAAE,MAAI;AAAC,UAAEJ,IAAE,CAACI,IAAEC,OAAI;AAAC,MAAAD,KAAED,GAAE,GAAEJ,IAAEF,EAAC,IAAEQ,OAAI,QAAIF,GAAE,GAAEJ,IAAEF,EAAC;AAAA,IAAC,GAAE,EAAC,WAAU,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAG,GAAE,SAAOG,GAAE,GAAEJ,IAAEF,EAAC;AAAA,EAAC,CAAC,GAAE,QAAEF,IAAEQ,EAAC;AAAC;;;ACA1M,IAAIG,KAAE,OAAO,oBAAoB;AAAE,SAASC,KAAG;AAAC,MAAIC,KAAE,OAAEF,IAAE,IAAI;AAAE,MAAGE,OAAI,KAAK,OAAM,IAAI,MAAM,gBAAgB;AAAE,SAAOA;AAAC;AAAC,SAAS,EAAE,EAAC,MAAKA,KAAE,IAAE,CAAC,CAAC,GAAE,MAAKC,KAAE,eAAc,OAAMC,KAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,CAAC,CAAC;AAAE,WAASC,GAAEC,IAAE;AAAC,WAAOF,GAAE,MAAM,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,KAAEH,GAAE,MAAM,QAAQE,EAAC;AAAE,MAAAC,OAAI,MAAIH,GAAE,MAAM,OAAOG,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,QAAER,IAAE,EAAC,UAASM,IAAE,MAAKJ,IAAE,MAAKC,IAAE,OAAMC,GAAC,CAAC,GAAE,SAAE,MAAIC,GAAE,MAAM,SAAO,IAAEA,GAAE,MAAM,KAAK,GAAG,IAAE,MAAM;AAAC;AAAC,IAAII,KAAE,gBAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,IAAG,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMP,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIG;AAAE,MAAIF,MAAGE,KAAEL,GAAE,OAAK,OAAKK,KAAE,0BAA0BC,GAAE,CAAC,IAAGF,KAAEL,GAAE;AAAE,SAAO,UAAE,MAAI,YAAEK,GAAE,SAASD,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKG,KAAE,eAAc,MAAKE,KAAE,IAAE,CAAC,CAAC,GAAE,OAAMC,KAAE,CAAC,EAAC,IAAEL,IAAE,EAAC,GAAGM,GAAC,IAAEV,IAAEW,KAAE,EAAC,GAAG,OAAO,QAAQF,EAAC,EAAE,OAAO,CAACG,IAAE,CAACC,IAAEC,EAAC,MAAI,OAAO,OAAOF,IAAE,EAAC,CAACC,EAAC,GAAE,MAAEC,EAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,IAAGX,GAAC;AAAE,WAAO,EAAE,EAAC,UAASQ,IAAE,YAAWD,IAAE,MAAKF,GAAE,OAAM,OAAMP,IAAE,OAAMC,IAAE,MAAKI,GAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA/nB,SAAS,EAAES,IAAE;AAAC,MAAIC,KAAEC,GAAEF,EAAC;AAAE,MAAG,CAACC,IAAE;AAAC,QAAGD,OAAI,KAAK,QAAO;AAAK,UAAM,IAAI,MAAM,gEAAgEA,EAAC,EAAE;AAAA,EAAC;AAAC,MAAIG,KAAEF,GAAE,eAAe,wBAAwB;AAAE,MAAGE,GAAE,QAAOA;AAAE,MAAIC,KAAEH,GAAE,cAAc,KAAK;AAAE,SAAOG,GAAE,aAAa,MAAK,wBAAwB,GAAEH,GAAE,KAAK,YAAYG,EAAC;AAAC;AAAC,IAAMC,KAAE,oBAAI;AAAQ,SAAS,EAAEL,IAAE;AAAC,MAAIC;AAAE,UAAOA,KAAEI,GAAE,IAAIL,EAAC,MAAI,OAAKC,KAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,MAAIE,KAAEF,GAAE,EAAED,EAAC,CAAC;AAAE,SAAOG,MAAG,IAAEE,GAAE,OAAOL,EAAC,IAAEK,GAAE,IAAIL,IAAEG,EAAC,GAAEA;AAAC;AAAC,IAAIG,KAAE,gBAAE,EAAC,MAAK,UAAS,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMN,IAAE,EAAC,OAAMC,IAAE,OAAME,GAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,IAAI,GAAEF,KAAE,SAAE,MAAIA,GAAEE,EAAC,CAAC,GAAEG,KAAEC,GAAE,GAAEC,KAAE,OAAEC,IAAE,IAAI,GAAEC,KAAE,IAAEJ,OAAI,QAAIE,MAAG,OAAK,EAAEL,GAAE,KAAK,IAAEK,GAAE,cAAc,CAAC;AAAE,EAAAE,GAAE,SAAO,EAAEA,GAAE,OAAM,CAAAC,OAAGA,KAAE,CAAC;AAAE,MAAIC,KAAE,IAAE,KAAE;AAAE,YAAE,MAAI;AAAC,IAAAA,GAAE,QAAM;AAAA,EAAE,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAN,MAAGE,MAAG,SAAOE,GAAE,QAAMF,GAAE,cAAc;AAAA,EAAE,CAAC;AAAE,MAAIK,KAAE,OAAEC,IAAE,IAAI,GAAEC,KAAE,OAAGC,KAAE,mBAAE;AAAE,SAAO,MAAEb,IAAE,MAAI;AAAC,QAAGY,MAAG,CAACF,GAAE;AAAO,QAAIF,KAAEL,GAAEH,EAAC;AAAE,IAAAQ,OAAI,YAAEE,GAAE,SAASF,EAAC,GAAEK,EAAC,GAAED,KAAE;AAAA,EAAG,CAAC,GAAE,YAAE,MAAI;AAAC,QAAIE,IAAEC;AAAE,QAAIP,MAAGM,KAAEhB,GAAE,UAAQ,OAAK,SAAOgB,GAAE,eAAe,wBAAwB;AAAE,KAACN,MAAGD,GAAE,UAAQC,MAAG,EAAED,GAAE,OAAM,CAAAS,OAAGA,KAAE,CAAC,KAAGT,GAAE,MAAM,SAAS,SAAO,MAAIQ,KAAER,GAAE,MAAM,kBAAgB,QAAMQ,GAAE,YAAYR,GAAE,KAAK;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,CAACE,GAAE,SAAOF,GAAE,UAAQ,KAAK,QAAO;AAAK,QAAIC,KAAE,EAAC,KAAIR,IAAE,0BAAyB,GAAE;AAAE,WAAO,EAAE,UAAE,EAAC,IAAGO,GAAE,MAAK,GAAE,EAAE,EAAC,UAASC,IAAE,YAAWZ,IAAE,MAAK,CAAC,GAAE,OAAMG,IAAE,OAAMF,IAAE,MAAK,SAAQ,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1xB,IAA4xBc,KAAE,OAAO,qBAAqB;AAAE,SAAS,IAAG;AAAC,MAAIf,KAAE,OAAEe,IAAE,IAAI,GAAEd,KAAE,IAAE,CAAC,CAAC;AAAE,WAASE,GAAEI,IAAE;AAAC,WAAON,GAAE,MAAM,KAAKM,EAAC,GAAEP,MAAGA,GAAE,SAASO,EAAC,GAAE,MAAIH,GAAEG,EAAC;AAAA,EAAC;AAAC,WAASH,GAAEG,IAAE;AAAC,QAAIE,KAAER,GAAE,MAAM,QAAQM,EAAC;AAAE,IAAAE,OAAI,MAAIR,GAAE,MAAM,OAAOQ,IAAE,CAAC,GAAET,MAAGA,GAAE,WAAWO,EAAC;AAAA,EAAC;AAAC,MAAIL,KAAE,EAAC,UAASC,IAAE,YAAWC,IAAE,SAAQH,GAAC;AAAE,SAAM,CAACA,IAAE,gBAAE,EAAC,MAAK,iBAAgB,MAAMM,IAAE,EAAC,OAAME,GAAC,GAAE;AAAC,WAAO,QAAEM,IAAEb,EAAC,GAAE,MAAI;AAAC,UAAIS;AAAE,cAAOA,KAAEF,GAAE,YAAU,OAAK,SAAOE,GAAE,KAAKF,EAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,oBAAoB;AAAjC,IAAmCW,KAAE,gBAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMrB,IAAE,EAAC,OAAMC,IAAE,OAAME,GAAC,GAAE;AAAC,MAAIC,KAAE,SAAE,EAAC,gBAAe;AAAC,WAAOJ,GAAE;AAAA,EAAM,EAAC,CAAC;AAAE,SAAO,QAAEU,IAAEN,EAAC,GAAE,MAAI;AAAC,QAAG,EAAC,QAAOF,IAAE,GAAGK,GAAC,IAAEP;AAAE,WAAO,EAAE,EAAC,YAAWO,IAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMN,IAAE,OAAME,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACApkC,IAAI,MAAI,CAAAmB,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAE,IAAIC,KAAE,OAAO,eAAe;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAE,OAAEH,IAAE,IAAI;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIJ,KAAE,IAAI,MAAM,IAAIG,EAAC,+CAA+C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,IAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAIC,KAAE;AAAN,IAA6C,KAAG,gBAAE,EAAC,MAAK,UAAS,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,MAAK,EAAC,MAAK,CAAC,SAAQ,MAAM,GAAE,SAAQA,GAAC,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,MAAK,EAAC,MAAK,QAAO,SAAQ,SAAQ,EAAC,GAAE,OAAM,EAAC,OAAM,CAAAF,OAAG,KAAE,GAAE,MAAMA,IAAE,EAAC,MAAKC,IAAE,OAAMJ,IAAE,OAAMM,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIC,MAAGF,KAAEL,GAAE,OAAK,OAAKK,KAAE,qBAAqBJ,GAAE,CAAC,IAAGO,KAAE,IAAE,KAAE;AAAE,YAAE,MAAI;AAAC,IAAAA,GAAE,QAAM;AAAA,EAAE,CAAC;AAAE,MAAIC,KAAE,OAAGC,KAAE,SAAE,MAAIV,GAAE,SAAO,YAAUA,GAAE,SAAO,gBAAcA,GAAE,QAAMS,OAAIA,KAAE,MAAG,QAAQ,KAAK,iBAAiBC,EAAC,0GAA0G,IAAG,SAAS,GAAE,IAAE,IAAE,CAAC,GAAEC,KAAE,EAAG,GAAEC,KAAE,SAAE,MAAIZ,GAAE,SAAOE,MAAGS,OAAI,QAAMA,GAAE,QAAMV,GAAE,UAAQA,GAAE,OAAKD,GAAE,IAAI,GAAEa,KAAE,IAAE,IAAI,GAAEC,KAAE,SAAE,MAAIb,GAAGY,EAAC,CAAC;AAAE,MAAGT,GAAE,EAAC,IAAGS,IAAE,KAAIA,GAAC,CAAC,GAAE,EAAEb,GAAE,SAAOE,MAAGS,OAAI,MAAM,OAAM,IAAI,MAAM,uDAAuD;AAAE,MAAG,OAAOC,GAAE,SAAO,UAAU,OAAM,IAAI,MAAM,8FAA8FA,GAAE,UAAQV,KAAE,SAAOF,GAAE,IAAI,EAAE;AAAE,MAAIe,KAAE,SAAE,MAAIP,GAAE,SAAOI,GAAE,QAAM,IAAE,CAAC,GAAEI,KAAE,SAAE,MAAID,GAAE,UAAQ,CAAC,GAAEE,KAAE,SAAE,MAAI,EAAE,QAAM,CAAC,GAAEC,KAAE,OAAEpB,IAAE,IAAI,MAAI,MAAK,CAACqB,IAAE,CAAC,IAAE,EAAG,GAAE,EAAC,mBAAkBC,IAAE,iBAAgBC,IAAE,cAAaC,GAAC,IAAEJ,GAAG,EAAC,SAAQC,IAAE,mBAAkB,CAAC,SAAE,MAAI;AAAC,QAAII;AAAE,YAAOA,KAAEC,GAAE,SAAS,UAAQ,OAAKD,KAAEV,GAAE;AAAA,EAAK,CAAC,CAAC,EAAC,CAAC,GAAEY,MAAG,SAAE,MAAIR,GAAE,QAAM,WAAS,MAAM,GAAES,KAAE,SAAE,MAAIf,OAAI,QAAMA,GAAE,QAAMV,GAAE,aAAWA,GAAE,UAAQ,KAAE,GAAE0B,MAAG,SAAE,MAAIT,MAAGQ,GAAE,QAAM,QAAGV,GAAE,KAAK,GAAEY,MAAG,SAAE,MAAI;AAAC,QAAIL,IAAEM,IAAEC;AAAE,YAAOA,KAAE,MAAM,MAAMD,MAAGN,KAAET,GAAE,UAAQ,OAAK,SAAOS,GAAE,iBAAiB,UAAU,MAAI,OAAKM,KAAE,CAAC,CAAC,EAAE,KAAK,CAAAE,OAAGA,GAAE,OAAK,2BAAyB,QAAGA,GAAE,SAASC,GAAEX,EAAC,CAAC,KAAGU,cAAa,WAAW,MAAI,OAAKD,KAAE;AAAA,EAAI,CAAC;AAAE,EAAAhB,GAAEc,KAAGD,GAAE;AAAE,MAAIM,MAAG,SAAE,MAAIhB,GAAE,QAAM,OAAGD,GAAE,KAAK,GAAEkB,MAAG,SAAE,MAAI;AAAC,QAAIX,IAAEM,IAAEC;AAAE,YAAOA,KAAE,MAAM,MAAMD,MAAGN,KAAET,GAAE,UAAQ,OAAK,SAAOS,GAAE,iBAAiB,0BAA0B,MAAI,OAAKM,KAAE,CAAC,CAAC,EAAE,KAAK,CAAAE,OAAGA,GAAE,SAASC,GAAEX,EAAC,CAAC,KAAGU,cAAa,WAAW,MAAI,OAAKD,KAAE;AAAA,EAAI,CAAC;AAAE,EAAAhB,GAAEoB,KAAGD,GAAE,GAAE,EAAG,EAAC,MAAK,UAAS,SAAQ,SAAE,MAAIlB,GAAE,UAAQ,CAAC,GAAE,SAAQF,IAAE,UAAS,CAACU,IAAEM,OAAI;AAAC,QAAGA,OAAI,SAAS,QAAO,EAAEN,IAAE,EAAC,CAACnB,GAAE,GAAG,GAAE,MAAI,EAAE,SAAO,GAAE,CAACA,GAAE,MAAM,GAAE,MAAI,EAAE,SAAO,EAAC,CAAC;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI+B,MAAG,EAAG,EAAC,MAAK,qBAAoB,MAAK,SAAE,OAAK,EAAC,MAAKvB,GAAE,MAAK,EAAE,EAAC,CAAC,GAAEwB,KAAE,IAAE,IAAI,GAAEZ,KAAE,EAAC,SAAQY,IAAE,UAAS,IAAE,IAAI,GAAE,aAAYrB,IAAE,WAAWQ,IAAE;AAAC,IAAAa,GAAE,UAAQb,OAAIa,GAAE,QAAMb;AAAA,EAAE,GAAE,QAAO;AAAC,IAAAtB,GAAE,SAAQ,KAAE;AAAA,EAAC,EAAC;AAAE,UAAGH,IAAE0B,EAAC;AAAE,MAAIa,MAAG,SAAE,MAAI,EAAE,CAACrB,GAAE,SAAOC,GAAE,MAAM;AAAE,EAAAA,GAAGG,IAAE,CAACG,IAAEM,OAAI;AAAC,IAAAN,GAAE,eAAe,GAAEC,GAAE,MAAM,GAAE,SAAG,MAAIK,MAAG,OAAK,SAAOA,GAAE,MAAM,CAAC;AAAA,EAAC,GAAEQ,GAAE;AAAE,MAAIC,MAAG,SAAE,MAAI,EAAErB,GAAE,SAAOF,GAAE,UAAQ,EAAE;AAAE,EAAAD,IAAIR,KAAEQ,GAAE,UAAQ,OAAK,SAAOR,GAAE,aAAY,WAAU,CAAAiB,OAAG;AAAC,IAAAe,IAAG,UAAQf,GAAE,oBAAkBA,GAAE,QAAMS,GAAG,WAAST,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEC,GAAE,MAAM;AAAA,EAAG,CAAC;AAAE,MAAIe,MAAG,SAAE,MAAI,EAAEb,GAAE,SAAOX,GAAE,UAAQ,KAAGG,GAAE;AAAE,SAAOY,GAAGhB,IAAEyB,KAAG,CAAAhB,OAAG;AAAC,QAAIM;AAAE,WAAM,EAAC,YAAW,CAAC,IAAIA,KAAEN,GAAE,eAAa,OAAKM,KAAE,CAAC,GAAET,EAAC,EAAC;AAAA,EAAC,CAAC,GAAE,YAAG,CAAAG,OAAG;AAAC,QAAGR,GAAE,UAAQ,EAAE;AAAO,QAAIc,KAAEG,GAAEnB,EAAC;AAAE,QAAG,CAACgB,GAAE;AAAO,QAAIC,KAAE,IAAI,eAAe,CAAAC,OAAG;AAAC,eAAQS,MAAKT,IAAE;AAAC,YAAIU,KAAED,GAAE,OAAO,sBAAsB;AAAE,QAAAC,GAAE,MAAI,KAAGA,GAAE,MAAI,KAAGA,GAAE,UAAQ,KAAGA,GAAE,WAAS,KAAGjB,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC,CAAC;AAAE,IAAAM,GAAE,QAAQD,EAAC,GAAEN,GAAE,MAAIO,GAAE,WAAW,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKP,IAAE,cAAaM,IAAE,GAAGC,GAAC,IAAE9B,IAAE+B,KAAE,EAAC,GAAGlC,IAAE,KAAIgB,IAAE,IAAGN,IAAE,MAAKG,GAAE,OAAM,cAAaK,GAAE,UAAQ,IAAE,OAAG,QAAO,mBAAkBqB,GAAE,OAAM,oBAAmBD,IAAG,MAAK,GAAEK,KAAE,EAAC,MAAKzB,GAAE,UAAQ,EAAC;AAAE,WAAO,EAAEP,IAAE,EAAC,OAAM,KAAE,GAAE,MAAI,CAAC,EAAEkC,IAAE,MAAI,EAAEC,IAAG,EAAC,QAAO9B,GAAE,MAAK,GAAE,MAAI,EAAEL,IAAE,EAAC,OAAM,MAAE,GAAE,MAAI,EAAE,IAAE,EAAC,cAAaqB,IAAE,YAAWT,IAAE,UAASJ,GAAE,QAAM,EAAES,IAAG,OAAM,EAAC,QAAO,GAAE,SAAS,cAAa,MAAK,GAAE,SAAS,MAAI,CAAC,GAAE,SAAS,UAAS,CAAC,IAAE,GAAE,SAAS,KAAI,GAAE,MAAI,EAAE,GAAE,CAAC,GAAE,MAAI,EAAE,EAAC,UAASM,IAAE,YAAW,EAAC,GAAGD,IAAE,GAAGjC,GAAC,GAAE,MAAK2C,IAAE,OAAM3C,IAAE,OAAMM,IAAE,SAAQY,GAAE,UAAQ,GAAE,UAASG,GAAE,iBAAeA,GAAE,QAAO,MAAK,SAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,EAAEI,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAv8G,IAAy8G,KAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMtB,IAAE,EAAC,OAAMC,IAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIW;AAAE,MAAIL,MAAGK,KAAER,GAAE,OAAK,OAAKQ,KAAE,6BAA6BP,GAAE,CAAC,IAAGG,KAAEL,GAAE,eAAe;AAAE,WAASQ,GAAEE,IAAE;AAAC,IAAAA,GAAE,WAASA,GAAE,kBAAgBA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEL,GAAE,MAAM;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,GAAGK,GAAC,IAAET;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGG,IAAE,eAAc,MAAG,SAAQI,GAAC,GAAE,YAAWE,IAAE,MAAK,EAAC,MAAKL,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMH,IAAE,OAAMJ,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAr6H,IAAu6H,KAAG,gBAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMG,IAAE,EAAC,OAAMC,IAAE,OAAMJ,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIL,MAAGK,KAAET,GAAE,OAAK,OAAKS,KAAE,8BAA8BR,GAAE,CAAC,IAAGM,KAAER,GAAE,gBAAgB,GAAES,KAAE,IAAE,IAAI;AAAE,SAAOL,GAAE,EAAC,IAAGK,IAAE,KAAIA,GAAC,CAAC,GAAE,UAAE,MAAI;AAAC,QAAGD,GAAE,SAAS,UAAQ,KAAK,OAAM,IAAI,MAAM,2FAA2F;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,GAAGG,GAAC,IAAEV,IAAE,IAAE,EAAC,IAAGI,IAAE,KAAII,IAAE,eAAc,KAAE;AAAE,WAAO,EAAEA,IAAE,EAAC,OAAM,KAAE,GAAE,MAAI,EAAEkC,IAAE,MAAI,EAAE,EAAC,UAAS,GAAE,YAAW,EAAC,GAAGzC,IAAE,GAAGS,GAAC,GAAE,MAAK,EAAC,MAAKH,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMN,IAAE,OAAMJ,IAAE,MAAK,iBAAgB,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthJ,IAAwhJ,KAAG,gBAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMG,IAAE,EAAC,OAAMC,IAAE,OAAMJ,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIL,MAAGK,KAAET,GAAE,OAAK,OAAKS,KAAE,2BAA2BR,GAAE,CAAC,IAAGM,KAAER,GAAE,aAAa;AAAE,EAAAI,GAAE,EAAC,IAAGI,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC;AAAE,WAASC,GAAEE,IAAE;AAAC,IAAAA,GAAE,gBAAgB;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,GAAGA,GAAC,IAAEV,IAAE,IAAE,EAAC,IAAGI,IAAE,KAAIG,GAAE,UAAS,SAAQC,GAAC;AAAE,WAAO,EAAE,EAAC,UAAS,GAAE,YAAWE,IAAE,MAAK,EAAC,MAAKH,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMN,IAAE,OAAMJ,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA99J,IAAg+J+C,MAAG,gBAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAM5C,IAAE,EAAC,OAAMC,IAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIU;AAAE,MAAIJ,MAAGI,KAAEP,GAAE,OAAK,OAAKO,KAAE,2BAA2BN,GAAE,CAAC,IAAGG,KAAEL,GAAE,aAAa;AAAE,SAAO,UAAE,MAAI;AAAC,IAAAK,GAAE,WAAWD,EAAC,GAAE,YAAG,MAAIC,GAAE,WAAW,IAAI,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,GAAGI,GAAC,IAAER;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGG,GAAC,GAAE,YAAWK,IAAE,MAAK,EAAC,MAAKJ,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMH,IAAE,OAAMJ,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAh3K,IAAk3K,KAAGwB;;;ACAn3M,IAAIwB,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,IAAIE,KAAE,OAAO,mBAAmB;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAE,OAAEH,IAAE,IAAI;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIJ,KAAE,IAAI,MAAM,IAAIG,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,IAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAIC,KAAE,OAAO,wBAAwB;AAAE,SAASC,KAAG;AAAC,SAAO,OAAED,IAAE,IAAI;AAAC;AAAC,IAAIE,KAAE,gBAAE,EAAC,MAAK,cAAa,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,aAAY,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIQ,KAAE,IAAEL,GAAE,cAAY,IAAE,CAAC,GAAEM,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,EAAC,UAAS,IAAE,gCAAgCD,GAAE,CAAC,EAAE,GAAE,SAAQ,IAAE,+BAA+BA,GAAE,CAAC,EAAE,GAAE,iBAAgBF,IAAE,OAAMC,IAAE,QAAOC,IAAE,mBAAkB;AAAC,IAAAF,GAAE,QAAM,EAAEA,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC;AAAA,EAAC,GAAE,kBAAiB;AAAC,IAAAA,GAAE,UAAQ,MAAIA,GAAE,QAAM;AAAA,EAAE,GAAE,MAAMI,IAAE;AAAC,IAAAD,GAAE,gBAAgB;AAAE,QAAIE,MAAG,MAAID,KAAEA,cAAa,cAAYA,KAAEA,GAAE,iBAAiB,cAAYZ,GAAEY,EAAC,IAAEZ,GAAEW,GAAE,MAAM,IAAEX,GAAEW,GAAE,MAAM,GAAG;AAAE,IAAAE,MAAG,QAAMA,GAAE,MAAM;AAAA,EAAC,EAAC;AAAE,SAAO,QAAEZ,IAAEU,EAAC,GAAER,GAAE,SAAE,MAAI,EAAEK,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEE,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,aAAYE,IAAE,GAAGC,GAAC,IAAEV,IAAEW,KAAE,EAAC,MAAKN,GAAE,UAAQ,GAAE,OAAMG,GAAE,MAAK;AAAE,WAAO,EAAE,EAAC,YAAWE,IAAE,UAAS,CAAC,GAAE,MAAKC,IAAE,OAAMV,IAAE,OAAMJ,IAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAnxB,IAAqxB,IAAE,gBAAE,EAAC,MAAK,oBAAmB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMG,IAAE,EAAC,OAAMC,IAAE,OAAMJ,IAAE,QAAOQ,GAAC,GAAE;AAAC,MAAIC,KAAEP,GAAE,kBAAkB,GAAEQ,KAAEJ,GAAE,GAAEK,KAAE,SAAE,MAAID,OAAI,OAAK,QAAGA,GAAE,UAAQD,GAAE,QAAQ,KAAK;AAAE,YAAE,MAAI;AAAC,IAAAE,GAAE,SAAOR,GAAE,OAAK,SAAOM,GAAE,SAAS,QAAMN,GAAE;AAAA,EAAG,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAQ,GAAE,UAAQF,GAAE,SAAS,QAAM;AAAA,EAAK,CAAC;AAAE,MAAIG,KAAE,IAAE,IAAI;AAAE,EAAAJ,GAAE,EAAC,IAAGI,IAAE,KAAIA,GAAC,CAAC,GAAED,GAAE,SAAO,YAAE,MAAI;AAAC,IAAAF,GAAE,OAAO,QAAMG,GAAE;AAAA,EAAK,CAAC;AAAE,MAAIC,KAAEL,GAAE,SAAE,OAAK,EAAC,IAAGL,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEQ,EAAC;AAAE,WAASE,KAAG;AAAC,QAAIC;AAAE,IAAAZ,GAAE,aAAWQ,GAAE,SAAOF,GAAE,iBAAiB,IAAGM,KAAEf,GAAES,GAAE,MAAM,MAAI,QAAMM,GAAE,MAAM,KAAGN,GAAE,iBAAiB;AAAA,EAAE;AAAC,WAAS,EAAEM,IAAE;AAAC,QAAIC;AAAE,QAAG,CAACb,GAAE,SAAS,KAAGQ,GAAE,MAAM,SAAOI,GAAE,KAAI;AAAA,MAAC,KAAKf,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,iBAAiB,IAAGO,KAAEhB,GAAES,GAAE,MAAM,MAAI,QAAMO,GAAE,MAAM;AAAE;AAAA,IAAK;AAAA,QAAM,SAAOD,GAAE,KAAI;AAAA,MAAC,KAAKf,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,iBAAiB;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASQ,GAAEF,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKf,GAAE;AAAM,QAAAe,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIG;AAAE,QAAIH,KAAE,EAAC,MAAKN,GAAE,gBAAgB,UAAQ,EAAC,GAAE,EAAC,IAAGO,IAAE,GAAGG,GAAC,IAAEhB,IAAEiB,KAAET,GAAE,QAAM,EAAC,KAAIC,IAAE,MAAKC,GAAE,OAAM,SAAQC,IAAE,WAAU,EAAC,IAAE,EAAC,KAAII,KAAET,GAAE,SAAS,UAAQ,OAAKS,KAAEF,IAAE,KAAIJ,IAAE,MAAKC,GAAE,OAAM,iBAAgBJ,GAAE,gBAAgB,UAAQ,GAAE,iBAAgBA,GAAE,gBAAgB,UAAQ,KAAGT,GAAES,GAAE,KAAK,IAAEA,GAAE,QAAQ,QAAM,QAAO,UAASN,GAAE,WAAS,OAAG,QAAO,SAAQW,IAAE,WAAU,GAAE,SAAQG,GAAC;AAAE,WAAO,EAAE,EAAC,UAASG,IAAE,YAAWD,IAAE,MAAKJ,IAAE,OAAMX,IAAE,OAAMJ,IAAE,MAAK,mBAAkB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAnpE,IAAqpE,IAAE,gBAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMG,IAAE,EAAC,OAAMC,IAAE,OAAMJ,IAAE,QAAOQ,GAAC,GAAE;AAAC,MAAIC,KAAEP,GAAE,iBAAiB;AAAE,YAAE,MAAI;AAAC,IAAAC,GAAE,OAAK,SAAOM,GAAE,QAAQ,QAAMN,GAAE;AAAA,EAAG,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAM,GAAE,QAAQ,QAAM;AAAA,EAAI,CAAC,GAAED,GAAE,EAAC,IAAGC,GAAE,OAAM,KAAIA,GAAE,MAAK,CAAC,GAAE,QAAEJ,IAAEI,GAAE,OAAO;AAAE,MAAIC,KAAE,EAAE,GAAEC,KAAE,SAAE,MAAID,OAAI,QAAMA,GAAE,QAAMA,GAAE,UAAQA,GAAE,OAAKD,GAAE,gBAAgB,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIQ;AAAE,QAAIL,KAAE,EAAC,MAAKH,GAAE,gBAAgB,UAAQ,GAAE,OAAMA,GAAE,MAAK,GAAE,EAAC,IAAGI,IAAE,GAAGC,GAAC,IAAEX,IAAE,IAAE,EAAC,KAAIc,KAAER,GAAE,QAAQ,UAAQ,OAAKQ,KAAEJ,IAAE,KAAIJ,GAAE,MAAK;AAAE,WAAO,EAAE,EAAC,UAAS,GAAE,YAAWK,IAAE,MAAKF,IAAE,OAAMR,IAAE,OAAMJ,IAAE,UAASO,GAAE,iBAAeA,GAAE,QAAO,SAAQI,GAAE,OAAM,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA7tH,IAAIU,KAAE;AAAuH,SAASC,GAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIC,MAAGF,KAAED,GAAE,cAAY,OAAKC,KAAE,IAAGG,KAAEJ,GAAE,UAAU,IAAE;AAAE,MAAG,EAAEI,cAAa,aAAa,QAAOD;AAAE,MAAIE,KAAE;AAAG,WAAQC,MAAKF,GAAE,iBAAiB,qCAAqC,EAAE,CAAAE,GAAE,OAAO,GAAED,KAAE;AAAG,MAAIE,KAAEF,MAAGH,KAAEE,GAAE,cAAY,OAAKF,KAAE,KAAGC;AAAE,SAAOL,GAAE,KAAKS,EAAC,MAAIA,KAAEA,GAAE,QAAQT,IAAE,EAAE,IAAGS;AAAC;AAAC,SAASC,GAAER,IAAE;AAAC,MAAIG,KAAEH,GAAE,aAAa,YAAY;AAAE,MAAG,OAAOG,MAAG,SAAS,QAAOA,GAAE,KAAK;AAAE,MAAIC,KAAEJ,GAAE,aAAa,iBAAiB;AAAE,MAAGI,IAAE;AAAC,QAAIC,KAAED,GAAE,MAAM,GAAG,EAAE,IAAI,CAAAG,OAAG;AAAC,UAAIN,KAAE,SAAS,eAAeM,EAAC;AAAE,UAAGN,IAAE;AAAC,YAAIC,KAAED,GAAE,aAAa,YAAY;AAAE,eAAO,OAAOC,MAAG,WAASA,GAAE,KAAK,IAAEH,GAAEE,EAAC,EAAE,KAAK;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAC,EAAE,OAAO,OAAO;AAAE,QAAGI,GAAE,SAAO,EAAE,QAAOA,GAAE,KAAK,IAAI;AAAA,EAAC;AAAC,SAAON,GAAEC,EAAC,EAAE,KAAK;AAAC;;;ACA5oB,SAASS,GAAEC,IAAE;AAAC,MAAIC,KAAE,IAAE,EAAE,GAAEC,KAAE,IAAE,EAAE;AAAE,SAAM,MAAI;AAAC,QAAIC,KAAEC,GAAEJ,EAAC;AAAE,QAAG,CAACG,GAAE,QAAM;AAAG,QAAIE,KAAEF,GAAE;AAAU,QAAGF,GAAE,UAAQI,GAAE,QAAOH,GAAE;AAAM,QAAII,KAAEC,GAAEJ,EAAC,EAAE,KAAK,EAAE,YAAY;AAAE,WAAOF,GAAE,QAAMI,IAAEH,GAAE,QAAMI,IAAEA;AAAA,EAAC;AAAC;;;ACAu5B,SAAS,GAAGE,IAAEC,IAAE;AAAC,SAAOD,OAAIC;AAAC;AAAC,IAAI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAhE,IAAkE,MAAI,CAAAA,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAhI,IAAkI,MAAI,CAAAA,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAE,SAAS,GAAGF,IAAE;AAAC,wBAAsB,MAAI,sBAAsBA,EAAC,CAAC;AAAC;AAAC,IAAIG,KAAE,OAAO,gBAAgB;AAAE,SAASC,GAAEJ,IAAE;AAAC,MAAIC,KAAE,OAAEE,IAAE,IAAI;AAAE,MAAGF,OAAI,MAAK;AAAC,QAAIC,KAAE,IAAI,MAAM,IAAIF,EAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBE,IAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,IAAI,KAAG,gBAAE,EAAC,MAAK,WAAU,OAAM,EAAC,qBAAoB,CAAAD,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQ,MAAI,GAAE,GAAE,YAAW,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,MAAKG,GAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,CAAC,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,IAAE,EAAE,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,CAAC;AAAE,WAASC,GAAEC,KAAE,CAAAC,OAAGA,IAAE;AAAC,QAAIA,KAAEJ,GAAE,UAAQ,OAAKF,GAAE,MAAME,GAAE,KAAK,IAAE,MAAKK,KAAE,EAAGF,GAAEL,GAAE,MAAM,MAAM,CAAC,GAAE,CAAAQ,OAAGlB,GAAEkB,GAAE,QAAQ,MAAM,CAAC,GAAEC,KAAEH,KAAEC,GAAE,QAAQD,EAAC,IAAE;AAAK,WAAOG,OAAI,OAAKA,KAAE,OAAM,EAAC,SAAQF,IAAE,mBAAkBE,GAAC;AAAA,EAAC;AAAC,MAAI,IAAE,SAAE,MAAInB,GAAE,WAAS,IAAE,CAAC,GAAE,CAACoB,IAAEC,EAAC,IAAE,EAAE,SAAE,MAAIrB,GAAE,UAAU,GAAE,CAAAe,OAAGV,GAAE,qBAAoBU,EAAC,GAAE,SAAE,MAAIf,GAAE,YAAY,CAAC,GAAEsB,KAAE,SAAE,MAAIF,GAAE,UAAQ,SAAO,EAAE,EAAE,OAAM,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,OAAM,CAAC,IAAEA,GAAE,KAAK,GAAEG,KAAE,EAAC,cAAajB,IAAE,OAAMgB,IAAE,MAAK,GAAE,QAAQP,IAAEC,IAAE;AAAC,QAAG,OAAOhB,GAAE,MAAI,UAAS;AAAC,UAAIiB,KAAEjB,GAAE;AAAG,cAAOe,MAAG,OAAK,SAAOA,GAAEE,EAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,EAAC;AAAA,IAAE;AAAC,WAAOjB,GAAE,GAAGe,IAAEC,EAAC;AAAA,EAAC,GAAE,aAAY,SAAE,MAAIhB,GAAE,aAAW,eAAa,UAAU,GAAE,UAASO,IAAE,WAAUC,IAAE,YAAWC,IAAE,UAAS,SAAE,MAAIT,GAAE,QAAQ,GAAE,SAAQU,IAAE,aAAYC,IAAE,mBAAkBC,IAAE,mBAAkBC,IAAE,eAAc;AAAC,IAAAb,GAAE,YAAUM,GAAE,UAAQ,MAAIA,GAAE,QAAM,GAAEM,GAAE,QAAM;AAAA,EAAK,GAAE,cAAa;AAAC,IAAAZ,GAAE,YAAUM,GAAE,UAAQ,MAAIA,GAAE,QAAM;AAAA,EAAE,GAAE,WAAWS,IAAEC,IAAEC,IAAE;AAAC,QAAGjB,GAAE,YAAUM,GAAE,UAAQ,EAAE;AAAO,QAAIa,KAAEL,GAAE,GAAEI,KAAEV,GAAGO,OAAIS,GAAE,WAAS,EAAC,OAAMA,GAAE,UAAS,IAAGR,GAAC,IAAE,EAAC,OAAMD,GAAC,GAAE,EAAC,cAAa,MAAII,GAAE,SAAQ,oBAAmB,MAAIA,GAAE,mBAAkB,WAAU,CAAAM,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,SAAQ,CAAC;AAAE,IAAAd,GAAE,QAAM,IAAGC,GAAE,QAAMM,IAAEL,GAAE,QAAMI,MAAG,OAAKA,KAAE,GAAEP,GAAE,QAAMS,GAAE;AAAA,EAAO,GAAE,OAAOJ,IAAE;AAAC,QAAGf,GAAE,YAAUM,GAAE,UAAQ,EAAE;AAAO,QAAIW,KAAEN,GAAE,UAAQ,KAAG,IAAE;AAAE,IAAAA,GAAE,SAAOI,GAAE,YAAY;AAAE,QAAIG,MAAGN,GAAE,UAAQ,OAAKF,GAAE,MAAM,MAAME,GAAE,QAAMK,EAAC,EAAE,OAAOP,GAAE,MAAM,MAAM,GAAEE,GAAE,QAAMK,EAAC,CAAC,IAAEP,GAAE,OAAO,KAAK,CAAAgB,OAAGA,GAAE,QAAQ,UAAU,WAAWf,GAAE,KAAK,KAAG,CAACe,GAAE,QAAQ,QAAQ,GAAED,KAAEP,KAAER,GAAE,MAAM,QAAQQ,EAAC,IAAE;AAAG,IAAAO,OAAI,MAAIA,OAAIb,GAAE,UAAQA,GAAE,QAAMa,IAAEZ,GAAE,QAAM;AAAA,EAAE,GAAE,cAAa;AAAC,IAAAb,GAAE,YAAUM,GAAE,UAAQ,KAAGK,GAAE,UAAQ,OAAKA,GAAE,QAAM;AAAA,EAAG,GAAE,eAAeI,IAAEC,IAAE;AAAC,QAAIC,KAAEH,GAAE,CAAAK,OAAG,CAAC,GAAGA,IAAE,EAAC,IAAGJ,IAAE,SAAQC,GAAC,CAAC,CAAC;AAAE,IAAAN,GAAE,QAAMO,GAAE,SAAQL,GAAE,QAAMK,GAAE;AAAA,EAAiB,GAAE,iBAAiBF,IAAE;AAAC,QAAIC,KAAEF,GAAE,CAAAG,OAAG;AAAC,UAAIE,KAAEF,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKH,EAAC;AAAE,aAAOI,OAAI,MAAIF,GAAE,OAAOE,IAAE,CAAC,GAAEF;AAAA,IAAC,CAAC;AAAE,IAAAP,GAAE,QAAMM,GAAE,SAAQJ,GAAE,QAAMI,GAAE,mBAAkBH,GAAE,QAAM;AAAA,EAAC,GAAE,cAAcE,IAAE;AAAC,IAAAf,GAAE,YAAUqB,GAAEN,EAAC;AAAA,EAAC,GAAE,OAAOA,IAAE;AAAC,IAAAf,GAAE,YAAUqB,GAAE,EAAE,EAAE,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIN,IAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAIC,KAAE,MAAEO,GAAE,MAAM,KAAK,EAAE,MAAM,GAAEN,KAAE,MAAEF,EAAC,GAAEI,KAAEH,GAAE,UAAU,CAAAE,OAAGK,GAAE,QAAQN,IAAE,MAAEC,EAAC,CAAC,CAAC;AAAE,aAAOC,OAAI,KAAGH,GAAE,KAAKC,EAAC,IAAED,GAAE,OAAOG,IAAE,CAAC,GAAEH;AAAA,IAAC,EAAC,CAAC,CAAC;AAAA,EAAC,EAAC;AAAE,EAAAX,GAAE,CAACG,IAAEC,EAAC,GAAE,CAACM,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAM,GAAE,aAAa,GAAE,EAAGP,IAAES,GAAG,KAAK,MAAIV,GAAE,eAAe,IAAGE,KAAEjB,GAAEQ,EAAC,MAAI,QAAMS,GAAE,MAAM;AAAA,EAAE,GAAE,SAAE,MAAIX,GAAE,UAAQ,CAAC,CAAC,GAAE,QAAEH,IAAEoB,EAAC,GAAER,GAAG,SAAE,MAAI,EAAET,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEU,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIW,KAAE,SAAE,MAAI;AAAC,QAAIZ;AAAE,YAAOA,KAAEf,GAAEQ,EAAC,MAAI,OAAK,SAAOO,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAO,UAAE,MAAI;AAAC,UAAE,CAACY,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAO3B,GAAE,iBAAe,OAAO;AAAO,eAASe,KAAG;AAAC,QAAAQ,GAAE,cAAcvB,GAAE,YAAY;AAAA,MAAC;AAAC,aAAO2B,GAAE,MAAM,iBAAiB,SAAQZ,EAAC,GAAE,MAAI;AAAC,YAAIC;AAAE,SAACA,KAAEW,GAAE,UAAQ,QAAMX,GAAE,oBAAoB,SAAQD,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKA,IAAE,YAAWC,IAAE,UAASC,IAAE,MAAKE,IAAE,GAAGD,GAAC,IAAElB,IAAEyB,KAAE,EAAC,MAAKnB,GAAE,UAAQ,GAAE,UAASW,IAAE,OAAMK,GAAE,MAAK;AAAE,WAAO,EAAE,UAAE,CAAC,GAAGP,MAAG,QAAMO,GAAE,SAAO,OAAK,EAAG,EAAC,CAACP,EAAC,GAAEO,GAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACI,IAAEE,EAAC,MAAI,EAAE,GAAGC,GAAG,EAAC,UAASf,GAAG,QAAO,KAAIY,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKP,IAAE,UAASF,IAAE,MAAKS,IAAE,OAAME,GAAC,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,UAAS,CAAC,GAAE,YAAW,EAAC,GAAG1B,IAAE,GAAG4B,GAAGZ,IAAE,CAAC,gBAAe,uBAAsB,cAAa,YAAW,IAAI,CAAC,EAAC,GAAE,MAAKO,IAAE,OAAMxB,IAAE,OAAMC,IAAE,MAAK,UAAS,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAh6G,IAAk6G6B,MAAG,gBAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAM/B,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIH,MAAGG,KAAER,GAAE,OAAK,OAAKQ,KAAE,4BAA4BQ,GAAE,CAAC,IAAGV,KAAEF,GAAE,cAAc;AAAE,WAASG,KAAG;AAAC,QAAIE;AAAE,KAACA,KAAET,GAAEM,GAAE,SAAS,MAAI,QAAMG,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIA,KAAE,EAAC,MAAKH,GAAE,aAAa,UAAQ,GAAE,UAASA,GAAE,SAAS,MAAK,GAAE,EAAC,GAAGI,GAAC,IAAEV,IAAEW,KAAE,EAAC,IAAGN,IAAE,KAAIC,GAAE,UAAS,SAAQC,GAAC;AAAE,WAAO,EAAE,EAAC,UAASI,IAAE,YAAWD,IAAE,MAAKD,IAAE,OAAMR,IAAE,OAAMC,IAAE,MAAK,eAAc,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA14H,IAA44H,KAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMF,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOG,GAAC,GAAE;AAAC,MAAIO;AAAE,MAAIN,MAAGM,KAAEZ,GAAE,OAAK,OAAKY,KAAE,6BAA6BI,GAAE,CAAC,IAAGT,KAAEH,GAAE,eAAe;AAAE,EAAAC,GAAE,EAAC,IAAGE,GAAE,WAAU,KAAIA,GAAE,UAAS,CAAC;AAAE,WAASC,GAAEK,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKb,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAU,QAAAa,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE,SAAE,MAAI;AAAC,cAAIO;AAAE,WAACA,KAAEd,GAAEO,GAAE,UAAU,MAAI,QAAMO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEP,GAAE,MAAM,SAAOA,GAAE,WAAWiB,GAAE,KAAK;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKxB,GAAE;AAAQ,QAAAa,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE,SAAE,MAAI;AAAC,cAAIO;AAAE,WAACA,KAAEd,GAAEO,GAAE,UAAU,MAAI,QAAMO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEP,GAAE,MAAM,SAAOA,GAAE,WAAWiB,GAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASf,GAAEI,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKb,GAAE;AAAM,QAAAa,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASH,GAAEG,IAAE;AAAC,IAAAN,GAAE,SAAS,UAAQA,GAAE,aAAa,UAAQ,KAAGA,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,UAAIO;AAAE,cAAOA,KAAEd,GAAEO,GAAE,SAAS,MAAI,OAAK,SAAOO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAID,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE,GAAG,MAAI;AAAC,UAAIO;AAAE,cAAOA,KAAEd,GAAEO,GAAE,UAAU,MAAI,OAAK,SAAOO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAG;AAAC,MAAIH,KAAED,GAAE,SAAE,OAAK,EAAC,IAAGV,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEM,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIa,IAAEC;AAAE,QAAIR,KAAE,EAAC,MAAKN,GAAE,aAAa,UAAQ,GAAE,UAASA,GAAE,SAAS,OAAM,OAAMA,GAAE,MAAM,MAAK,GAAE,EAAC,GAAGO,GAAC,IAAEd,IAAE,IAAE,EAAC,KAAIO,GAAE,WAAU,IAAGD,IAAE,MAAKK,GAAE,OAAM,iBAAgB,WAAU,kBAAiBS,KAAEpB,GAAEO,GAAE,UAAU,MAAI,OAAK,SAAOa,GAAE,IAAG,iBAAgBb,GAAE,aAAa,UAAQ,GAAE,mBAAkBA,GAAE,SAAS,QAAM,EAAEc,KAAErB,GAAEO,GAAE,QAAQ,MAAI,OAAK,SAAOc,GAAE,IAAGf,EAAC,EAAE,KAAK,GAAG,IAAE,QAAO,UAASC,GAAE,SAAS,UAAQ,OAAG,OAAG,QAAO,WAAUC,IAAE,SAAQC,IAAE,SAAQC,GAAC;AAAE,WAAO,EAAE,EAAC,UAAS,GAAE,YAAWI,IAAE,MAAKD,IAAE,OAAMZ,IAAE,OAAMC,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAv6K,IAAy6K8B,MAAG,gBAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMhC,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOG,GAAC,GAAE;AAAC,MAAIO;AAAE,MAAIN,MAAGM,KAAEZ,GAAE,OAAK,OAAKY,KAAE,8BAA8BI,GAAE,CAAC,IAAGT,KAAEH,GAAE,gBAAgB,GAAEI,KAAE,IAAE,IAAI;AAAE,EAAAH,GAAE,EAAC,IAAGE,GAAE,YAAW,KAAIA,GAAE,WAAU,CAAC;AAAE,WAASE,GAAEI,IAAE;AAAC,YAAOL,GAAE,SAAO,aAAaA,GAAE,KAAK,GAAEK,GAAE,KAAI;AAAA,MAAC,KAAKb,GAAE;AAAM,YAAGO,GAAE,YAAY,UAAQ,GAAG,QAAOM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,OAAOM,GAAE,GAAG;AAAA,MAAE,KAAKb,GAAE;AAAM,YAAGa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,kBAAkB,UAAQ,MAAK;AAAC,cAAIO,KAAEP,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK;AAAE,UAAAA,GAAE,OAAOO,GAAE,QAAQ,KAAK;AAAA,QAAC;AAAC,QAAAP,GAAE,KAAK,UAAQ,MAAIA,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,cAAIO;AAAE,kBAAOA,KAAEd,GAAEO,GAAE,SAAS,MAAI,OAAK,SAAOO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAG;AAAA,MAAM,KAAK,EAAEP,GAAE,YAAY,OAAM,EAAC,UAASP,GAAE,WAAU,YAAWA,GAAE,WAAU,CAAC;AAAE,eAAOa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWiB,GAAE,IAAI;AAAA,MAAE,KAAK,EAAEjB,GAAE,YAAY,OAAM,EAAC,UAASP,GAAE,SAAQ,YAAWA,GAAE,UAAS,CAAC;AAAE,eAAOa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWiB,GAAE,QAAQ;AAAA,MAAE,KAAKxB,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWiB,GAAE,KAAK;AAAA,MAAE,KAAKxB,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWiB,GAAE,IAAI;AAAA,MAAE,KAAKxB,GAAE;AAAO,QAAAa,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,cAAIO;AAAE,kBAAOA,KAAEd,GAAEO,GAAE,SAAS,MAAI,OAAK,SAAOO,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKd,GAAE;AAAI,QAAAa,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,IAAI,WAAS,MAAIN,GAAE,OAAOM,GAAE,GAAG,GAAEL,GAAE,QAAM,WAAW,MAAID,GAAE,YAAY,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIG,KAAE,EAAG,GAAEC,KAAE,SAAE,MAAID,OAAI,QAAMA,GAAE,QAAMM,GAAE,UAAQA,GAAE,OAAKT,GAAE,aAAa,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIa,IAAEC;AAAE,QAAIR,KAAE,EAAC,MAAKN,GAAE,aAAa,UAAQ,EAAC,GAAE,EAAC,GAAGO,GAAC,IAAEd,IAAE,IAAE,EAAC,yBAAwBO,GAAE,kBAAkB,UAAQ,SAAOa,KAAEb,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOa,GAAE,IAAG,wBAAuBb,GAAE,KAAK,UAAQ,IAAE,OAAG,QAAO,oBAAmBc,KAAErB,GAAEO,GAAE,SAAS,MAAI,OAAK,SAAOc,GAAE,IAAG,oBAAmBd,GAAE,YAAY,OAAM,IAAGD,IAAE,WAAUG,IAAE,MAAK,WAAU,UAAS,GAAE,KAAIF,GAAE,WAAU;AAAE,WAAO,EAAE,EAAC,UAAS,GAAE,YAAWO,IAAE,MAAKD,IAAE,OAAMZ,IAAE,OAAMC,IAAE,UAAS+B,GAAE,iBAAeA,GAAE,QAAO,SAAQtB,GAAE,OAAM,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAljP,IAAojP,KAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMX,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOG,GAAC,GAAE;AAAC,MAAIsB;AAAE,MAAIrB,MAAGqB,KAAE3B,GAAE,OAAK,OAAK2B,KAAE,6BAA6BX,GAAE,CAAC,IAAGT,KAAEH,GAAE,eAAe,GAAEI,KAAE,IAAE,IAAI;AAAE,EAAAH,GAAE,EAAC,IAAGG,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIC,KAAE,SAAE,MAAIF,GAAE,kBAAkB,UAAQ,OAAKA,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,EAAE,OAAKD,KAAE,KAAE,GAAEI,KAAE,SAAE,MAAI,EAAEH,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,QAAQ,MAAEA,GAAE,MAAM,KAAK,GAAE,MAAEP,GAAE,KAAK,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,MAAEO,GAAE,MAAM,KAAK,EAAE,KAAK,CAAAQ,OAAGR,GAAE,QAAQ,MAAEQ,EAAC,GAAE,MAAEf,GAAE,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAEW,KAAE,SAAE,MAAI,EAAEJ,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIS;AAAE,QAAID,KAAE,MAAER,GAAE,MAAM,KAAK;AAAE,aAAQS,KAAET,GAAE,QAAQ,MAAM,KAAK,CAAAU,OAAGF,GAAE,KAAK,CAAAI,OAAGZ,GAAE,QAAQ,MAAEY,EAAC,GAAE,MAAEF,GAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,MAAI,OAAK,SAAOD,GAAE,QAAMV;AAAA,EAAC,GAAE,CAAC,CAAC,GAAE,MAAII,GAAE,MAAK,CAAC,CAAC,GAAEE,KAAEA,GAAEJ,EAAC,GAAEK,KAAE,SAAE,OAAK,EAAC,UAASb,GAAE,UAAS,OAAMA,GAAE,OAAM,IAAI,YAAW;AAAC,WAAOY,GAAE;AAAA,EAAC,GAAE,QAAOJ,GAAC,EAAE;AAAE,YAAE,MAAID,GAAE,eAAeD,IAAEO,EAAC,CAAC,GAAE,YAAE,MAAIN,GAAE,iBAAiBD,EAAC,CAAC,GAAE,UAAE,MAAI;AAAC,UAAE,CAACC,GAAE,cAAaG,EAAC,GAAE,MAAI;AAAC,MAAAH,GAAE,aAAa,UAAQ,KAAGG,GAAE,SAAO,EAAEH,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAAI,GAAE,SAAOJ,GAAE,WAAWiB,GAAE,UAASlB,EAAC;AAAA,MAAC,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,QAAAC,GAAE,WAAWiB,GAAE,UAASlB,EAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAC,GAAE,aAAa,UAAQ,KAAGE,GAAE,SAAOF,GAAE,kBAAkB,UAAQ,KAAG,SAAE,MAAI;AAAC,UAAIQ,IAAEC;AAAE,cAAOA,MAAGD,KAAEf,GAAEQ,EAAC,MAAI,OAAK,SAAOO,GAAE,mBAAiB,OAAK,SAAOC,GAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASD,GAAEC,IAAE;AAAC,QAAGf,GAAE,SAAS,QAAOe,GAAE,eAAe;AAAE,IAAAR,GAAE,OAAOP,GAAE,KAAK,GAAEO,GAAE,KAAK,UAAQ,MAAIA,GAAE,aAAa,GAAE,SAAE,MAAI;AAAC,UAAIS;AAAE,cAAOA,KAAEhB,GAAEO,GAAE,SAAS,MAAI,OAAK,SAAOS,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,WAAS,IAAG;AAAC,QAAGhB,GAAE,SAAS,QAAOO,GAAE,WAAWiB,GAAE,OAAO;AAAE,IAAAjB,GAAE,WAAWiB,GAAE,UAASlB,EAAC;AAAA,EAAC;AAAC,MAAIc,KAAEN,GAAG;AAAE,WAASO,GAAEN,IAAE;AAAC,IAAAK,GAAE,OAAOL,EAAC;AAAA,EAAC;AAAC,WAASO,GAAEP,IAAE;AAAC,IAAAK,GAAE,SAASL,EAAC,MAAIf,GAAE,YAAUS,GAAE,SAAOF,GAAE,WAAWiB,GAAE,UAASlB,IAAE,CAAC;AAAA,EAAE;AAAC,WAASiB,GAAER,IAAE;AAAC,IAAAK,GAAE,SAASL,EAAC,MAAIf,GAAE,YAAUS,GAAE,SAAOF,GAAE,WAAWiB,GAAE,OAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAAST,GAAC,IAAEf,IAAEgB,KAAE,EAAC,QAAOP,GAAE,OAAM,UAASC,GAAE,OAAM,UAASK,GAAC,GAAE,EAAC,OAAME,IAAE,UAASE,IAAE,GAAGD,GAAC,IAAElB,IAAEyB,KAAE,EAAC,IAAGnB,IAAE,KAAIE,IAAE,MAAK,UAAS,UAASO,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,iBAAgBL,GAAE,OAAM,UAAS,QAAO,SAAQI,IAAE,SAAQ,GAAE,gBAAeO,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC;AAAE,WAAO,EAAE,EAAC,UAASE,IAAE,YAAWP,IAAE,MAAKF,IAAE,OAAMd,IAAE,OAAMD,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA5zU,IAAI,KAAG,CAAAiC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,KAAG,CAAC,CAAC;AAA9D,IAAgE,MAAI,CAAAA,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAE,SAAS,GAAGC,IAAE;AAAC,wBAAsB,MAAI,sBAAsBA,EAAC,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,aAAa;AAAE,SAASC,GAAEF,IAAE;AAAC,MAAIG,KAAE,OAAEF,IAAE,IAAI;AAAE,MAAGE,OAAI,MAAK;AAAC,QAAIJ,KAAE,IAAI,MAAM,IAAIC,EAAC,6CAA6C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBD,IAAEG,EAAC,GAAEH;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAI,KAAG,gBAAE,EAAC,MAAK,QAAO,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,EAAC,GAAE,MAAMH,IAAE,EAAC,OAAMG,IAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIK,KAAE,IAAE,CAAC,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,IAAE,EAAE,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,CAAC;AAAE,WAASC,GAAEC,KAAE,CAAAC,OAAGA,IAAE;AAAC,QAAIA,KAAEJ,GAAE,UAAQ,OAAKF,GAAE,MAAME,GAAE,KAAK,IAAE,MAAKK,KAAE,EAAEF,GAAEL,GAAE,MAAM,MAAM,CAAC,GAAE,CAAAQ,OAAGf,GAAEe,GAAE,QAAQ,MAAM,CAAC,GAAEC,KAAEH,KAAEC,GAAE,QAAQD,EAAC,IAAE;AAAK,WAAOG,OAAI,OAAKA,KAAE,OAAM,EAAC,OAAMF,IAAE,iBAAgBE,GAAC;AAAA,EAAC;AAAC,MAAIC,KAAE,EAAC,WAAUb,IAAE,WAAUC,IAAE,UAASC,IAAE,OAAMC,IAAE,aAAYC,IAAE,iBAAgBC,IAAE,mBAAkBC,IAAE,WAAU,MAAI;AAAC,IAAAN,GAAE,QAAM,GAAEK,GAAE,QAAM;AAAA,EAAI,GAAE,UAAS,MAAIL,GAAE,QAAM,GAAE,SAASQ,IAAEC,IAAEC,IAAE;AAAC,QAAIE,KAAEL,GAAE,GAAEI,KAAEP,GAAEI,OAAIM,GAAE,WAAS,EAAC,OAAMA,GAAE,UAAS,IAAGL,GAAC,IAAE,EAAC,OAAMD,GAAC,GAAE,EAAC,cAAa,MAAII,GAAE,OAAM,oBAAmB,MAAIA,GAAE,iBAAgB,WAAU,CAAAG,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,SAAQ,CAAC;AAAE,IAAAX,GAAE,QAAM,IAAGC,GAAE,QAAMM,IAAEL,GAAE,QAAMI,MAAG,OAAKA,KAAE,GAAEP,GAAE,QAAMS,GAAE;AAAA,EAAK,GAAE,OAAOJ,IAAE;AAAC,QAAIE,KAAEN,GAAE,UAAQ,KAAG,IAAE;AAAE,IAAAA,GAAE,SAAOI,GAAE,YAAY;AAAE,QAAIG,MAAGN,GAAE,UAAQ,OAAKF,GAAE,MAAM,MAAME,GAAE,QAAMK,EAAC,EAAE,OAAOP,GAAE,MAAM,MAAM,GAAEE,GAAE,QAAMK,EAAC,CAAC,IAAEP,GAAE,OAAO,KAAK,CAAAa,OAAGA,GAAE,QAAQ,UAAU,WAAWZ,GAAE,KAAK,KAAG,CAACY,GAAE,QAAQ,QAAQ,GAAED,KAAEJ,KAAER,GAAE,MAAM,QAAQQ,EAAC,IAAE;AAAG,IAAAI,OAAI,MAAIA,OAAIV,GAAE,UAAQA,GAAE,QAAMU,IAAET,GAAE,QAAM;AAAA,EAAE,GAAE,cAAa;AAAC,IAAAF,GAAE,QAAM;AAAA,EAAE,GAAE,aAAaI,IAAEC,IAAE;AAAC,QAAIC,KAAEH,GAAE,CAAAK,OAAG,CAAC,GAAGA,IAAE,EAAC,IAAGJ,IAAE,SAAQC,GAAC,CAAC,CAAC;AAAE,IAAAN,GAAE,QAAMO,GAAE,OAAML,GAAE,QAAMK,GAAE,iBAAgBJ,GAAE,QAAM;AAAA,EAAC,GAAE,eAAeE,IAAE;AAAC,QAAIC,KAAEF,GAAE,CAAAG,OAAG;AAAC,UAAIE,KAAEF,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKH,EAAC;AAAE,aAAOI,OAAI,MAAIF,GAAE,OAAOE,IAAE,CAAC,GAAEF;AAAA,IAAC,CAAC;AAAE,IAAAP,GAAE,QAAMM,GAAE,OAAMJ,GAAE,QAAMI,GAAE,iBAAgBH,GAAE,QAAM;AAAA,EAAC,EAAC;AAAE,SAAOW,GAAE,CAAChB,IAAEC,EAAC,GAAE,CAACM,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAG,GAAE,UAAU,GAAE,EAAEJ,IAAEO,GAAE,KAAK,MAAIR,GAAE,eAAe,IAAGE,KAAEd,GAAEK,EAAC,MAAI,QAAMS,GAAE,MAAM;AAAA,EAAE,GAAE,SAAE,MAAIV,GAAE,UAAQ,CAAC,CAAC,GAAE,QAAEH,IAAEgB,EAAC,GAAEL,GAAE,SAAE,MAAI,EAAER,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEL,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIa,KAAE,EAAC,MAAKR,GAAE,UAAQ,GAAE,OAAMa,GAAE,UAAS;AAAE,WAAO,EAAE,EAAC,UAAS,CAAC,GAAE,YAAWjB,IAAE,MAAKY,IAAE,OAAMT,IAAE,OAAMJ,IAAE,MAAK,OAAM,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA9mD,IAAgnD,KAAG,gBAAE,EAAC,MAAK,cAAa,OAAM,EAAC,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMC,IAAE,EAAC,OAAMG,IAAE,OAAMJ,IAAE,QAAOK,GAAC,GAAE;AAAC,MAAIO;AAAE,MAAIN,MAAGM,KAAEX,GAAE,OAAK,OAAKW,KAAE,0BAA0BZ,GAAE,CAAC,IAAGO,KAAEJ,GAAE,YAAY;AAAE,EAAAE,GAAE,EAAC,IAAGE,GAAE,WAAU,KAAIA,GAAE,UAAS,CAAC;AAAE,WAASC,GAAEU,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKjB,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAU,QAAAiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,SAAS,GAAE,SAAE,MAAI;AAAC,cAAIM;AAAE,WAACA,KAAEZ,GAAEM,GAAE,QAAQ,MAAI,QAAMM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEN,GAAE,SAASY,GAAE,KAAK;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKlB,GAAE;AAAQ,QAAAiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,SAAS,GAAE,SAAE,MAAI;AAAC,cAAIM;AAAE,WAACA,KAAEZ,GAAEM,GAAE,QAAQ,MAAI,QAAMM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASV,GAAES,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKjB,GAAE;AAAM,QAAAiB,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASR,GAAEQ,IAAE;AAAC,IAAAjB,GAAE,aAAWM,GAAE,UAAU,UAAQ,KAAGA,GAAE,UAAU,GAAE,SAAE,MAAI;AAAC,UAAIM;AAAE,cAAOA,KAAEZ,GAAEM,GAAE,SAAS,MAAI,OAAK,SAAOM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAIK,GAAE,eAAe,GAAEX,GAAE,SAAS,GAAE,GAAG,MAAI;AAAC,UAAIM;AAAE,cAAOA,KAAEZ,GAAEM,GAAE,QAAQ,MAAI,OAAK,SAAOM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAG;AAAC,MAAIF,KAAEM,GAAE,SAAE,OAAK,EAAC,IAAGhB,GAAE,IAAG,MAAKG,GAAE,KAAI,EAAE,GAAEG,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIQ;AAAE,QAAIG,KAAE,EAAC,MAAKX,GAAE,UAAU,UAAQ,EAAC,GAAE,EAAC,GAAGM,GAAC,IAAEZ,IAAEa,KAAE,EAAC,KAAIP,GAAE,WAAU,IAAGD,IAAE,MAAKK,GAAE,OAAM,iBAAgB,QAAO,kBAAiBI,KAAEd,GAAEM,GAAE,QAAQ,MAAI,OAAK,SAAOQ,GAAE,IAAG,iBAAgBR,GAAE,UAAU,UAAQ,GAAE,WAAUC,IAAE,SAAQC,IAAE,SAAQC,GAAC;AAAE,WAAO,EAAE,EAAC,UAASI,IAAE,YAAWD,IAAE,MAAKK,IAAE,OAAMd,IAAE,OAAMJ,IAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA78F,IAA+8F,KAAG,gBAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMC,IAAE,EAAC,OAAMG,IAAE,OAAMJ,IAAE,QAAOK,GAAC,GAAE;AAAC,MAAIa;AAAE,MAAIZ,MAAGY,KAAEjB,GAAE,OAAK,OAAKiB,KAAE,yBAAyBlB,GAAE,CAAC,IAAGO,KAAEJ,GAAE,WAAW,GAAEK,KAAE,IAAE,IAAI;AAAE,EAAAH,GAAE,EAAC,IAAGE,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC,GAAEP,GAAE,EAAC,WAAU,SAAE,MAAIC,GAAEM,GAAE,QAAQ,CAAC,GAAE,SAAQ,SAAE,MAAIA,GAAE,UAAU,UAAQ,CAAC,GAAE,OAAOM,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,aAAW,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASJ,GAAEI,IAAE;AAAC,QAAIC;AAAE,YAAON,GAAE,SAAO,aAAaA,GAAE,KAAK,GAAEK,GAAE,KAAI;AAAA,MAAC,KAAKZ,GAAE;AAAM,YAAGM,GAAE,YAAY,UAAQ,GAAG,QAAOM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,OAAOM,GAAE,GAAG;AAAA,MAAE,KAAKZ,GAAE;AAAM,YAAGY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,gBAAgB,UAAQ,MAAK;AAAC,cAAIU,KAAEV,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK;AAAE,WAACO,KAAEb,GAAEgB,GAAE,QAAQ,MAAM,MAAI,QAAMH,GAAE,MAAM;AAAA,QAAC;AAAC,QAAAP,GAAE,UAAU,GAAE,EAAEN,GAAEM,GAAE,SAAS,CAAC;AAAE;AAAA,MAAM,KAAKN,GAAE;AAAU,eAAOY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,MAAE,KAAKlB,GAAE;AAAQ,eAAOY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,QAAQ;AAAA,MAAE,KAAKlB,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,KAAK;AAAA,MAAE,KAAKlB,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,MAAE,KAAKlB,GAAE;AAAO,QAAAY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,UAAU,GAAE,SAAE,MAAI;AAAC,cAAIQ;AAAE,kBAAOA,KAAEd,GAAEM,GAAE,SAAS,MAAI,OAAK,SAAOQ,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKd,GAAE;AAAI,QAAAY,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,UAAU,GAAE,SAAE,MAAI,EAAEN,GAAEM,GAAE,SAAS,GAAEM,GAAE,WAAS,EAAE,WAAS,EAAE,IAAI,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,IAAI,WAAS,MAAIN,GAAE,OAAOM,GAAE,GAAG,GAAEL,GAAE,QAAM,WAAW,MAAID,GAAE,YAAY,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC;AAAC,WAASG,GAAEG,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKZ,GAAE;AAAM,QAAAY,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIF,KAAE,EAAE,GAAEC,KAAE,SAAE,MAAID,OAAI,QAAMA,GAAE,QAAMX,GAAE,UAAQA,GAAE,OAAKO,GAAE,UAAU,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIU,IAAED;AAAE,QAAIH,KAAE,EAAC,MAAKN,GAAE,UAAU,UAAQ,EAAC,GAAE,EAAC,GAAGO,GAAC,IAAEb,IAAEc,KAAE,EAAC,yBAAwBR,GAAE,gBAAgB,UAAQ,SAAOU,KAAEV,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK,MAAI,OAAK,SAAOU,GAAE,IAAG,oBAAmBD,KAAEf,GAAEM,GAAE,SAAS,MAAI,OAAK,SAAOS,GAAE,IAAG,IAAGV,IAAE,WAAUG,IAAE,SAAQC,IAAE,MAAK,QAAO,UAAS,GAAE,KAAIH,GAAE,SAAQ;AAAE,WAAO,EAAE,EAAC,UAASQ,IAAE,YAAWD,IAAE,MAAKD,IAAE,OAAMT,IAAE,OAAMJ,IAAE,UAASuB,GAAE,iBAAeA,GAAE,QAAO,SAAQX,GAAE,OAAM,MAAK,YAAW,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAxqK,IAA0qKY,MAAG,gBAAE,EAAC,MAAK,YAAW,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMvB,IAAE,EAAC,OAAMG,IAAE,OAAMJ,IAAE,QAAOK,GAAC,GAAE;AAAC,MAAIW;AAAE,MAAIV,MAAGU,KAAEf,GAAE,OAAK,OAAKe,KAAE,wBAAwBhB,GAAE,CAAC,IAAGO,KAAEJ,GAAE,UAAU,GAAEK,KAAE,IAAE,IAAI;AAAE,EAAAH,GAAE,EAAC,IAAGG,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIC,KAAE,SAAE,MAAIF,GAAE,gBAAgB,UAAQ,OAAKA,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK,EAAE,OAAKD,KAAE,KAAE,GAAEI,KAAEJ,GAAEE,EAAC,GAAEG,KAAE,SAAE,OAAK,EAAC,UAASV,GAAE,UAAS,IAAI,YAAW;AAAC,WAAOS,GAAE;AAAA,EAAC,GAAE,QAAOF,GAAC,EAAE;AAAE,YAAE,MAAID,GAAE,aAAaD,IAAEK,EAAC,CAAC,GAAE,YAAE,MAAIJ,GAAE,eAAeD,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,IAAAC,GAAE,UAAU,UAAQ,KAAGE,GAAE,SAAOF,GAAE,kBAAkB,UAAQ,KAAG,SAAE,MAAI;AAAC,UAAIa,IAAEC;AAAE,cAAOA,MAAGD,KAAEnB,GAAEO,EAAC,MAAI,OAAK,SAAOY,GAAE,mBAAiB,OAAK,SAAOC,GAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASR,GAAEQ,IAAE;AAAC,QAAGnB,GAAE,SAAS,QAAOmB,GAAE,eAAe;AAAE,IAAAb,GAAE,UAAU,GAAE,EAAEN,GAAEM,GAAE,SAAS,CAAC;AAAA,EAAC;AAAC,WAASW,KAAG;AAAC,QAAGjB,GAAE,SAAS,QAAOM,GAAE,SAASY,GAAE,OAAO;AAAE,IAAAZ,GAAE,SAASY,GAAE,UAASb,EAAC;AAAA,EAAC;AAAC,MAAIO,KAAEO,GAAE;AAAE,WAASN,GAAEM,IAAE;AAAC,IAAAP,GAAE,OAAOO,EAAC;AAAA,EAAC;AAAC,WAASL,GAAEK,IAAE;AAAC,IAAAP,GAAE,SAASO,EAAC,MAAInB,GAAE,YAAUQ,GAAE,SAAOF,GAAE,SAASY,GAAE,UAASb,IAAE,CAAC;AAAA,EAAE;AAAC,WAASW,GAAEG,IAAE;AAAC,IAAAP,GAAE,SAASO,EAAC,MAAInB,GAAE,YAAUQ,GAAE,SAAOF,GAAE,SAASY,GAAE,OAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAASC,IAAE,GAAGC,GAAC,IAAEpB,IAAEwB,KAAE,EAAC,QAAOhB,GAAE,OAAM,UAASW,IAAE,OAAMb,GAAE,UAAS;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGD,IAAE,KAAIE,IAAE,MAAK,YAAW,UAASY,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,SAAQR,IAAE,SAAQM,IAAE,gBAAeJ,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeE,IAAE,cAAaA,GAAC,GAAE,YAAW,EAAC,GAAGjB,IAAE,GAAGqB,GAAC,GAAE,MAAKI,IAAE,OAAMzB,IAAE,OAAMI,IAAE,MAAK,WAAU,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACAzvN,IAAIsB,OAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,OAAI,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,gBAAgB;AAAE,SAASE,GAAEC,IAAE;AAAC,MAAIC,KAAE,OAAE,IAAG,IAAI;AAAE,MAAGA,OAAI,MAAK;AAAC,QAAIH,KAAE,IAAI,MAAM,IAAIE,EAAC,4BAA4B,GAAG,IAAI,gBAAgB;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBF,IAAEC,EAAC,GAAED;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,IAAI,KAAG,OAAO,qBAAqB;AAAE,SAAS,KAAI;AAAC,SAAO,OAAE,IAAG,IAAI;AAAC;AAAC,IAAIC,MAAG,OAAO,qBAAqB;AAAE,SAASC,MAAI;AAAC,SAAO,OAAED,KAAG,IAAI;AAAC;AAAC,IAAI,KAAG,gBAAE,EAAC,MAAK,WAAU,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMF,IAAE,EAAC,OAAMC,IAAE,OAAMH,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,KAAE,IAAE,IAAI;AAAE,EAAAF,GAAE,EAAC,IAAGE,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIC,KAAE,IAAE,CAAC,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,IAAI,GAAEC,KAAE,SAAE,MAAIC,GAAEP,EAAC,CAAC,GAAEQ,KAAE,SAAE,MAAI;AAAC,QAAIC,IAAEC;AAAE,QAAG,CAACR,GAAEA,EAAC,KAAG,CAACA,GAAEG,EAAC,EAAE,QAAM;AAAG,aAAQM,MAAK,SAAS,iBAAiB,UAAU,EAAE,KAAG,OAAOA,MAAG,OAAK,SAAOA,GAAE,SAAST,GAAEA,EAAC,CAAC,CAAC,IAAE,OAAOS,MAAG,OAAK,SAAOA,GAAE,SAAST,GAAEG,EAAC,CAAC,CAAC,EAAE,QAAM;AAAG,QAAIO,KAAE,EAAE,GAAEC,KAAED,GAAE,QAAQV,GAAEA,EAAC,CAAC,GAAEY,MAAGD,KAAED,GAAE,SAAO,KAAGA,GAAE,QAAOG,MAAGF,KAAE,KAAGD,GAAE,QAAOI,KAAEJ,GAAEE,EAAC,GAAEG,KAAEL,GAAEG,EAAC;AAAE,WAAM,GAAGN,KAAEP,GAAEG,EAAC,MAAI,QAAMI,GAAE,SAASO,EAAC,MAAI,GAAGN,KAAER,GAAEG,EAAC,MAAI,QAAMK,GAAE,SAASO,EAAC;AAAA,EAAE,CAAC,GAAEC,KAAE,EAAC,cAAajB,IAAE,UAAS,IAAE,IAAI,GAAE,SAAQ,IAAE,IAAI,GAAE,OAAMI,IAAE,QAAOH,IAAE,aAAYM,IAAE,qBAAoBL,IAAE,oBAAmBC,IAAE,gBAAe;AAAC,IAAAH,GAAE,QAAM,EAAEA,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC;AAAA,EAAC,GAAE,eAAc;AAAC,IAAAA,GAAE,UAAQ,MAAIA,GAAE,QAAM;AAAA,EAAE,GAAE,MAAMW,IAAE;AAAC,IAAAM,GAAE,aAAa;AAAE,QAAIL,MAAG,MAAID,KAAEA,cAAa,cAAYA,KAAEA,GAAE,iBAAiB,cAAYV,GAAEU,EAAC,IAAEV,GAAEgB,GAAE,MAAM,IAAEhB,GAAEgB,GAAE,MAAM,GAAG;AAAE,IAAAL,MAAG,QAAMA,GAAE,MAAM;AAAA,EAAC,EAAC;AAAE,UAAE,IAAGK,EAAC,GAAEjB,GAAG,SAAE,MAAI,EAAEA,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEM,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIY,KAAE,EAAC,UAASD,GAAE,UAAS,SAAQA,GAAE,SAAQ,QAAO;AAAC,IAAAA,GAAE,aAAa;AAAA,EAAC,EAAC,GAAEE,KAAE,GAAG,GAAEC,KAAED,MAAG,OAAK,SAAOA,GAAE,iBAAgB,CAACE,IAAEC,EAAC,IAAE,EAAG,GAAEhB,KAAEiB,GAAG,EAAC,iBAAgBJ,MAAG,OAAK,SAAOA,GAAE,iBAAgB,SAAQE,IAAE,mBAAkB,CAACpB,IAAEG,EAAC,EAAC,CAAC;AAAE,WAASoB,KAAG;AAAC,QAAIb,IAAEC,IAAEC,IAAEC;AAAE,YAAOA,KAAEK,MAAG,OAAK,SAAOA,GAAE,0BAA0B,MAAI,OAAKL,OAAIH,KAAEN,GAAE,UAAQ,OAAK,SAAOM,GAAE,qBAAmBC,KAAEX,GAAEA,EAAC,MAAI,OAAK,SAAOW,GAAE,SAASP,GAAE,MAAM,aAAa,QAAMQ,KAAEZ,GAAEG,EAAC,MAAI,OAAK,SAAOS,GAAE,SAASR,GAAE,MAAM,aAAa;AAAA,EAAG;AAAC,SAAO,YAAE,MAAIe,MAAG,OAAK,SAAOA,GAAEF,EAAC,CAAC,GAAEX,IAAIT,KAAEO,GAAE,UAAQ,OAAK,SAAOP,GAAE,aAAY,SAAQ,CAAAa,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAF,GAAE,WAAS,UAAQA,GAAE,kBAAkB,eAAaX,GAAE,UAAQ,MAAIwB,GAAE,KAAGvB,MAAGG,OAAIE,GAAE,SAASK,GAAE,MAAM,MAAIC,KAAEX,GAAEgB,GAAE,mBAAmB,MAAI,QAAML,GAAE,SAASD,GAAE,MAAM,MAAIE,KAAEZ,GAAEgB,GAAE,kBAAkB,MAAI,QAAMJ,GAAE,SAASF,GAAE,MAAM,KAAGM,GAAE,aAAa;AAAA,EAAG,GAAE,IAAE,GAAEK,GAAGhB,GAAE,mBAAkB,CAACK,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAI,GAAE,aAAa,GAAE,EAAGL,IAAEf,GAAG,KAAK,MAAIc,GAAE,eAAe,IAAGE,KAAEZ,GAAEA,EAAC,MAAI,QAAMY,GAAE,MAAM;AAAA,EAAE,GAAE,SAAE,MAAIb,GAAE,UAAQ,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIW,KAAE,EAAC,MAAKX,GAAE,UAAQ,GAAE,OAAMiB,GAAE,MAAK;AAAE,WAAO,EAAE,UAAE,CAAC,EAAEK,IAAE,CAAC,GAAE,MAAI,EAAE,EAAC,YAAW,EAAC,GAAG7B,IAAE,GAAGF,GAAC,GAAE,UAAS,EAAC,KAAIQ,GAAC,GAAE,MAAKY,IAAE,OAAMjB,IAAE,OAAMH,IAAE,MAAK,UAAS,CAAC,CAAC,GAAE,EAAEe,GAAE,YAAY,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1jE,IAA4jEmB,MAAG,gBAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMhC,IAAE,EAAC,OAAMC,IAAE,OAAMH,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,MAAGD,KAAEL,GAAE,OAAK,OAAKK,KAAE,6BAA6BQ,GAAE,CAAC,IAAGN,KAAER,GAAE,eAAe,GAAES,KAAE,SAAE,MAAIK,GAAEN,GAAE,MAAM,CAAC;AAAE,EAAAH,GAAE,EAAC,IAAGG,GAAE,QAAO,KAAIA,GAAE,OAAM,CAAC,GAAE,UAAG,MAAI;AAAC,IAAAA,GAAE,SAAS,QAAMD;AAAA,EAAC,CAAC,GAAE,YAAG,MAAI;AAAC,IAAAC,GAAE,SAAS,QAAM;AAAA,EAAI,CAAC;AAAE,MAAIE,KAAE,GAAG,GAAEC,KAAED,MAAG,OAAK,SAAOA,GAAE,aAAYE,KAAER,IAAG,GAAES,KAAE,SAAE,MAAID,OAAI,OAAK,QAAGA,GAAE,UAAQJ,GAAE,QAAQ,KAAK,GAAEO,KAAE,IAAE,IAAI,GAAEU,KAAE,6BAA6BX,GAAE,CAAC;AAAG,EAAAD,GAAE,SAAO,YAAE,MAAI;AAAC,IAAAL,GAAE,OAAO,QAAMC,GAAEM,EAAC;AAAA,EAAC,CAAC;AAAE,MAAIW,KAAE3B,GAAG,SAAE,OAAK,EAAC,IAAGE,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEa,EAAC;AAAE,WAASY,GAAER,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,QAAGX,GAAE,OAAM;AAAC,UAAGL,GAAE,aAAa,UAAQ,EAAE;AAAO,cAAOW,GAAE,KAAI;AAAA,QAAC,KAAKV,GAAE;AAAA,QAAM,KAAKA,GAAE;AAAM,UAAAU,GAAE,eAAe,IAAGE,MAAGD,KAAED,GAAE,QAAQ,UAAQ,QAAME,GAAE,KAAKD,EAAC,GAAEZ,GAAE,aAAa,IAAGc,KAAEb,GAAED,GAAE,MAAM,MAAI,QAAMc,GAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,SAAOH,GAAE,KAAI;AAAA,MAAC,KAAKV,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAU,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,aAAa,UAAQ,MAAIG,MAAG,QAAMA,GAAEH,GAAE,SAAS,KAAK,IAAGA,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKC,GAAE;AAAO,YAAGD,GAAE,aAAa,UAAQ,EAAE,QAAOG,MAAG,OAAK,SAAOA,GAAEH,GAAE,SAAS,KAAK;AAAE,YAAG,CAACC,GAAED,GAAE,MAAM,MAAIe,KAAEd,GAAE,UAAQ,QAAMc,GAAE,iBAAe,GAAGC,KAAEf,GAAED,GAAE,MAAM,MAAI,QAAMgB,GAAE,SAASf,GAAE,MAAM,aAAa,GAAG;AAAO,QAAAU,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,aAAa;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASoB,GAAET,IAAE;AAAC,IAAAN,GAAE,SAAOM,GAAE,QAAMV,GAAE,SAAOU,GAAE,eAAe;AAAA,EAAC;AAAC,WAASU,GAAEV,IAAE;AAAC,QAAIC,IAAEC;AAAE,IAAApB,GAAE,aAAWY,GAAE,SAAOL,GAAE,aAAa,IAAGY,KAAEX,GAAED,GAAE,MAAM,MAAI,QAAMY,GAAE,MAAM,MAAID,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,aAAa,UAAQ,MAAIG,MAAG,QAAMA,GAAEH,GAAE,SAAS,KAAK,IAAGA,GAAE,cAAc,IAAGa,KAAEZ,GAAED,GAAE,MAAM,MAAI,QAAMa,GAAE,MAAM;AAAA,EAAG;AAAC,WAASS,GAAEX,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAA,EAAC;AAAC,MAAIL,KAAEoB,GAAG;AAAE,WAASF,KAAG;AAAC,QAAIb,KAAEV,GAAED,GAAE,KAAK;AAAE,QAAG,CAACW,GAAE;AAAO,aAASC,KAAG;AAAC,QAAEN,GAAE,OAAM,EAAC,CAACb,GAAE,QAAQ,GAAE,MAAI,EAAEkB,IAAE,EAAE,KAAK,GAAE,CAAClB,GAAE,SAAS,GAAE,MAAI,EAAEkB,IAAE,EAAE,IAAI,EAAC,CAAC,MAAI,EAAE,SAAO,EAAE,EAAE,EAAE,OAAO,CAAAG,OAAGA,GAAE,QAAQ,yBAAuB,MAAM,GAAE,EAAER,GAAE,OAAM,EAAC,CAACb,GAAE,QAAQ,GAAE,EAAE,MAAK,CAACA,GAAE,SAAS,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAC,YAAWQ,GAAED,GAAE,MAAM,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAY,GAAE;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAID,KAAEX,GAAE,aAAa,UAAQ,GAAEY,KAAE,EAAC,MAAKD,GAAC,GAAE,EAAC,GAAGE,GAAC,IAAEpB,IAAEqB,KAAET,GAAE,QAAM,EAAC,KAAIE,IAAE,MAAKW,GAAE,OAAM,WAAUC,IAAE,SAAQE,GAAC,IAAE,EAAC,KAAId,IAAE,IAAGR,IAAE,MAAKmB,GAAE,OAAM,iBAAgBlB,GAAE,aAAa,UAAQ,GAAE,iBAAgBC,GAAED,GAAE,KAAK,IAAEA,GAAE,QAAQ,QAAM,QAAO,UAASP,GAAE,WAAS,OAAG,QAAO,WAAU0B,IAAE,SAAQC,IAAE,SAAQC,IAAE,aAAYC,GAAC;AAAE,WAAO,EAAE,UAAE,CAAC,EAAE,EAAC,UAASR,IAAE,YAAW,EAAC,GAAGpB,IAAE,GAAGmB,GAAC,GAAE,MAAKD,IAAE,OAAMlB,IAAE,OAAMH,IAAE,MAAK,gBAAe,CAAC,GAAEoB,MAAG,CAACN,GAAE,SAAOL,GAAE,YAAY,SAAO,EAAE,GAAE,EAAC,IAAGiB,IAAE,UAASnB,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQ0B,GAAC,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAz+I,IAA2+I,KAAG,gBAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,EAAC,GAAE,MAAM/B,IAAE,EAAC,OAAMC,IAAE,OAAMH,GAAC,GAAE;AAAC,MAAIM,KAAEL,GAAE,gBAAgB,GAAEO,KAAE,8BAA8BO,GAAE,CAAC,IAAGN,KAAE,EAAG,GAAEC,KAAE,SAAE,MAAID,OAAI,QAAMA,GAAE,QAAMM,GAAE,UAAQA,GAAE,OAAKT,GAAE,aAAa,UAAQ,CAAC;AAAE,WAASK,KAAG;AAAC,IAAAL,GAAE,aAAa;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIM,KAAE,EAAC,MAAKN,GAAE,aAAa,UAAQ,EAAC;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGE,IAAE,eAAc,MAAG,SAAQG,GAAC,GAAE,YAAWT,IAAE,MAAKU,IAAE,OAAMT,IAAE,OAAMH,IAAE,UAASgC,GAAE,iBAAeA,GAAE,QAAO,SAAQtB,GAAE,OAAM,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthK,IAAwhK0B,MAAG,gBAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,OAAM,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMlC,IAAE,EAAC,OAAMC,IAAE,OAAMH,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIyB;AAAE,MAAIvB,MAAGuB,KAAE7B,GAAE,OAAK,OAAK6B,KAAE,4BAA4BhB,GAAE,CAAC,IAAG,EAAC,OAAMN,GAAC,IAAEP,IAAEQ,KAAET,GAAE,cAAc,GAAEU,KAAE,SAAE,MAAII,GAAEL,GAAE,KAAK,CAAC,GAAEE,KAAE,oCAAoCG,GAAE,CAAC,IAAGF,KAAE,mCAAmCE,GAAE,CAAC;AAAG,EAAAT,GAAE,EAAC,IAAGI,GAAE,OAAM,KAAIA,GAAE,MAAK,CAAC,GAAE,UAAG,MAAI;AAAC,IAAAA,GAAE,QAAQ,QAAMF;AAAA,EAAC,CAAC,GAAE,YAAG,MAAI;AAAC,IAAAE,GAAE,QAAQ,QAAM;AAAA,EAAI,CAAC,GAAE,QAAEN,KAAGM,GAAE,OAAO,GAAE,YAAE,MAAI;AAAC,QAAIuB,IAAE1B;AAAE,QAAG,CAACE,MAAGC,GAAE,aAAa,UAAQ,KAAG,CAACA,GAAE,MAAM;AAAO,QAAIK,MAAGkB,KAAEtB,GAAE,UAAQ,OAAK,SAAOsB,GAAE;AAAc,KAAC1B,KAAEG,GAAEA,GAAE,KAAK,MAAI,QAAMH,GAAE,SAASQ,EAAC,KAAG,EAAEL,GAAEA,GAAE,KAAK,GAAE,EAAE,KAAK;AAAA,EAAC,CAAC;AAAE,MAAII,KAAE,EAAG,GAAEE,KAAE,SAAE,MAAIF,OAAI,QAAMA,GAAE,QAAMC,GAAE,UAAQA,GAAE,OAAKL,GAAE,aAAa,UAAQ,CAAC;AAAE,WAASgB,GAAEX,IAAE;AAAC,QAAIkB,IAAE1B;AAAE,YAAOQ,GAAE,KAAI;AAAA,MAAC,KAAKL,GAAE;AAAO,YAAGA,GAAE,aAAa,UAAQ,KAAG,CAACA,GAAEA,GAAE,KAAK,KAAGC,GAAE,SAAO,GAAGsB,KAAEvB,GAAEA,GAAE,KAAK,MAAI,QAAMuB,GAAE,SAAStB,GAAE,MAAM,aAAa,GAAG;AAAO,QAAAI,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEL,GAAE,aAAa,IAAGH,KAAEG,GAAEA,GAAE,MAAM,MAAI,QAAMH,GAAE,MAAM;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASoB,GAAEZ,IAAE;AAAC,QAAIR,IAAEa,IAAEC,IAAEC,IAAEC;AAAE,QAAIU,KAAElB,GAAE;AAAc,IAAAkB,MAAGvB,GAAEA,GAAE,KAAK,OAAKH,KAAEG,GAAEA,GAAE,KAAK,MAAI,QAAMH,GAAE,SAAS0B,EAAC,MAAIvB,GAAE,aAAa,KAAIW,MAAGD,KAAEV,GAAEA,GAAE,mBAAmB,MAAI,OAAK,SAAOU,GAAE,aAAW,QAAMC,GAAE,KAAKD,IAAEa,EAAC,MAAIV,MAAGD,KAAEZ,GAAEA,GAAE,kBAAkB,MAAI,OAAK,SAAOY,GAAE,aAAW,QAAMC,GAAE,KAAKD,IAAEW,EAAC,MAAIA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAG;AAAC,MAAIL,KAAEO,GAAG;AAAE,WAASN,KAAG;AAAC,QAAId,KAAEL,GAAEA,GAAE,KAAK;AAAE,QAAG,CAACK,GAAE;AAAO,aAASkB,KAAG;AAAC,QAAEL,GAAE,OAAM,EAAC,CAAC1B,GAAE,QAAQ,GAAE,MAAI;AAAC,YAAIkB;AAAE,UAAEL,IAAE,EAAE,KAAK,MAAI,EAAE,WAASK,KAAEV,GAAEA,GAAE,kBAAkB,MAAI,QAAMU,GAAE,MAAM;AAAA,MAAE,GAAE,CAAClB,GAAE,SAAS,GAAE,MAAI;AAAC,YAAIK;AAAE,SAACA,KAAEG,GAAEA,GAAE,MAAM,MAAI,QAAMH,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,IAAA0B,GAAE;AAAA,EAAC;AAAC,WAASH,KAAG;AAAC,QAAIf,KAAEL,GAAEA,GAAE,KAAK;AAAE,QAAG,CAACK,GAAE;AAAO,aAASkB,KAAG;AAAC,QAAEL,GAAE,OAAM,EAAC,CAAC1B,GAAE,QAAQ,GAAE,MAAI;AAAC,YAAIK,KAAEG,GAAEA,GAAE,MAAM,GAAEU,KAAEV,GAAEA,GAAE,KAAK;AAAE,YAAG,CAACH,GAAE;AAAO,YAAIc,KAAE,EAAE,GAAEC,KAAED,GAAE,QAAQd,EAAC,GAAEgB,KAAEF,GAAE,MAAM,GAAEC,KAAE,CAAC,GAAEG,KAAE,CAAC,GAAGJ,GAAE,MAAMC,KAAE,CAAC,GAAE,GAAGC,EAAC;AAAE,iBAAQN,MAAKQ,GAAE,MAAM,EAAE,KAAGR,GAAE,QAAQ,yBAAuB,UAAQG,MAAG,QAAMA,GAAE,SAASH,EAAC,GAAE;AAAC,cAAIC,KAAEO,GAAE,QAAQR,EAAC;AAAE,UAAAC,OAAI,MAAIO,GAAE,OAAOP,IAAE,CAAC;AAAA,QAAC;AAAC,UAAEO,IAAE,EAAE,OAAM,EAAC,QAAO,MAAE,CAAC;AAAA,MAAC,GAAE,CAACvB,GAAE,SAAS,GAAE,MAAI;AAAC,YAAIkB;AAAE,UAAEL,IAAE,EAAE,QAAQ,MAAI,EAAE,WAASK,KAAEV,GAAEA,GAAE,MAAM,MAAI,QAAMU,GAAE,MAAM;AAAA,MAAE,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAa,GAAE;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIlB,KAAE,EAAC,MAAKL,GAAE,aAAa,UAAQ,GAAE,OAAMA,GAAE,MAAK,GAAE,EAAC,OAAMuB,IAAE,GAAG1B,GAAC,IAAEL,IAAEkB,KAAE,EAAC,KAAIV,GAAE,OAAM,IAAGF,IAAE,WAAUkB,IAAE,YAAWjB,MAAGC,GAAE,aAAa,UAAQ,IAAEiB,KAAE,QAAO,UAAS,GAAE;AAAE,WAAO,EAAE,EAAC,UAASP,IAAE,YAAW,EAAC,GAAGjB,IAAE,GAAGI,GAAC,GAAE,OAAMJ,IAAE,MAAKY,IAAE,OAAM,EAAC,GAAGf,IAAE,SAAQ,IAAIqB,OAAI;AAAC,UAAIC;AAAE,aAAM,CAAC,EAAE,UAAE,CAACN,GAAE,SAAON,GAAE,YAAY,SAAO,EAAE,GAAE,EAAC,IAAGE,IAAE,KAAIF,GAAE,qBAAoB,UAASH,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQsB,GAAC,CAAC,IAAGP,KAAEtB,GAAE,YAAU,OAAK,SAAOsB,GAAE,KAAKtB,IAAE,GAAGqB,EAAC,GAAEL,GAAE,SAAON,GAAE,YAAY,SAAO,EAAE,GAAE,EAAC,IAAGG,IAAE,KAAIH,GAAE,oBAAmB,UAASH,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQuB,GAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IAAC,EAAC,GAAE,UAASE,GAAE,iBAAeA,GAAE,QAAO,SAAQhB,GAAE,OAAM,MAAK,eAAc,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1wP,IAA4wPqB,MAAG,gBAAE,EAAC,MAAK,gBAAe,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMnC,IAAE,EAAC,OAAMC,IAAE,OAAMH,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIE,KAAE,IAAE,IAAI,GAAEC,KAAE,WAAG,CAAC,CAAC,GAAEC,KAAE,SAAE,MAAIK,GAAEP,EAAC,CAAC,GAAEG,KAAEC,GAAG;AAAE,EAAAN,GAAE,EAAC,IAAGE,IAAE,KAAIA,GAAC,CAAC;AAAE,WAASI,GAAEc,IAAE;AAAC,QAAIC,KAAElB,GAAE,MAAM,QAAQiB,EAAC;AAAE,IAAAC,OAAI,MAAIlB,GAAE,MAAM,OAAOkB,IAAE,CAAC;AAAA,EAAC;AAAC,WAASd,GAAEa,IAAE;AAAC,WAAOjB,GAAE,MAAM,KAAKiB,EAAC,GAAE,MAAI;AAAC,MAAAd,GAAEc,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAASZ,KAAG;AAAC,QAAIc;AAAE,QAAIF,KAAEhB,GAAE;AAAM,QAAG,CAACgB,GAAE,QAAM;AAAG,QAAIC,KAAED,GAAE;AAAc,YAAOE,KAAElB,GAAEF,EAAC,MAAI,QAAMoB,GAAE,SAASD,EAAC,IAAE,OAAGlB,GAAE,MAAM,KAAK,CAAAoB,OAAG;AAAC,UAAIC,IAAEC;AAAE,eAAQD,KAAEJ,GAAE,eAAeG,GAAE,SAAS,KAAK,MAAI,OAAK,SAAOC,GAAE,SAASH,EAAC,QAAMI,KAAEL,GAAE,eAAeG,GAAE,QAAQ,KAAK,MAAI,OAAK,SAAOE,GAAE,SAASJ,EAAC;AAAA,IAAE,CAAC;AAAA,EAAC;AAAC,WAASX,GAAEU,IAAE;AAAC,aAAQC,MAAKlB,GAAE,MAAM,CAAAkB,GAAE,SAAS,UAAQD,MAAGC,GAAE,MAAM;AAAA,EAAC;AAAC,SAAO,QAAE,IAAG,EAAC,iBAAgBd,IAAE,mBAAkBD,IAAE,2BAA0BE,IAAE,aAAYE,IAAE,iBAAgBL,GAAE,gBAAe,CAAC,GAAE,MAAI,EAAE,UAAE,CAAC,EAAE,EAAC,UAAS,EAAC,KAAIH,GAAC,GAAE,YAAW,EAAC,GAAGN,IAAE,GAAGC,GAAC,GAAE,MAAK,CAAC,GAAE,OAAMA,IAAE,OAAMH,IAAE,MAAK,eAAc,CAAC,GAAE,EAAEW,GAAE,YAAY,CAAC,CAAC;AAAC,EAAC,CAAC;;;ACA3lU,IAAI2B,KAAE,OAAO,cAAc;AAAE,SAASC,KAAG;AAAC,MAAIC,KAAE,OAAEF,IAAE,IAAI;AAAE,MAAGE,OAAI,MAAK;AAAC,QAAIC,KAAE,IAAI,MAAM,gEAAgE;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBA,IAAEF,EAAC,GAAEE;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAASE,GAAE,EAAC,MAAKF,KAAE,CAAC,GAAE,MAAKC,KAAE,SAAQ,OAAME,KAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,CAAC,CAAC;AAAE,WAASC,GAAEC,IAAE;AAAC,WAAOF,GAAE,MAAM,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,KAAEH,GAAE,MAAM,QAAQE,EAAC;AAAE,MAAAC,OAAI,MAAIH,GAAE,MAAM,OAAOG,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,QAAET,IAAE,EAAC,UAASO,IAAE,MAAKL,IAAE,MAAKC,IAAE,OAAME,GAAC,CAAC,GAAE,SAAE,MAAIC,GAAE,MAAM,SAAO,IAAEA,GAAE,MAAM,KAAK,GAAG,IAAE,MAAM;AAAC;AAAC,IAAII,KAAE,gBAAE,EAAC,MAAK,SAAQ,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMR,IAAE,EAAC,OAAMC,IAAE,OAAME,GAAC,GAAE;AAAC,MAAIG;AAAE,MAAIF,MAAGE,KAAEN,GAAE,OAAK,OAAKM,KAAE,oBAAoBH,GAAE,CAAC,IAAGE,KAAEN,GAAE;AAAE,SAAO,UAAE,MAAI,YAAEM,GAAE,SAASD,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKG,KAAE,SAAQ,MAAKE,KAAE,CAAC,GAAE,OAAMC,KAAE,CAAC,EAAC,IAAEL,IAAE,EAAC,SAAQM,IAAE,GAAGC,GAAC,IAAEZ,IAAEa,KAAE,EAAC,GAAG,OAAO,QAAQH,EAAC,EAAE,OAAO,CAACI,IAAE,CAACC,IAAEC,EAAC,MAAI,OAAO,OAAOF,IAAE,EAAC,CAACC,EAAC,GAAE,MAAEC,EAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,IAAGZ,GAAC;AAAE,WAAOO,OAAI,OAAOE,GAAE,SAAQ,OAAOA,GAAE,SAAQ,OAAOD,GAAE,UAAS,EAAE,EAAC,UAASC,IAAE,YAAWD,IAAE,MAAKH,IAAE,OAAMN,IAAE,OAAMF,IAAE,MAAKM,GAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA1S,SAASU,IAAGC,IAAEC,IAAE;AAAC,SAAOD,OAAIC;AAAC;AAAC,IAAIC,KAAE,OAAO,mBAAmB;AAAE,SAASC,GAAEH,IAAE;AAAC,MAAIC,KAAE,OAAEC,IAAE,IAAI;AAAE,MAAGD,OAAI,MAAK;AAAC,QAAIG,KAAE,IAAI,MAAM,IAAIJ,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBI,IAAED,EAAC,GAAEC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,IAAI,KAAG,gBAAE,EAAC,MAAK,cAAa,OAAM,EAAC,qBAAoB,CAAAD,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQ,MAAID,IAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMC,IAAE,EAAC,MAAKC,IAAE,OAAMG,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,MAAGD,KAAEP,GAAE,OAAK,OAAKO,KAAE,yBAAyBE,GAAE,CAAC,IAAGC,KAAE,IAAE,IAAI,GAAEC,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAEC,GAAE,EAAC,MAAK,kBAAiB,CAAC,GAAEC,KAAE,EAAE,EAAC,MAAK,wBAAuB,CAAC;AAAE,EAAAR,GAAE,EAAC,IAAGI,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAG,CAACK,IAAEC,EAAC,IAAE,EAAE,SAAE,MAAIhB,GAAE,UAAU,GAAE,CAAAiB,OAAGhB,GAAE,qBAAoBgB,EAAC,GAAE,SAAE,MAAIjB,GAAE,YAAY,CAAC,GAAEkB,KAAE,EAAC,SAAQP,IAAE,OAAMI,IAAE,UAAS,SAAE,MAAIf,GAAE,QAAQ,GAAE,aAAY,SAAE,MAAIW,GAAE,MAAM,KAAK,CAAAM,OAAG,CAACA,GAAE,SAAS,QAAQ,CAAC,GAAE,uBAAsB,SAAE,MAAIN,GAAE,MAAM,KAAK,CAAAM,OAAGC,GAAE,QAAQ,MAAED,GAAE,SAAS,KAAK,GAAE,MAAEjB,GAAE,UAAU,CAAC,CAAC,CAAC,GAAE,QAAQiB,IAAEE,IAAE;AAAC,QAAG,OAAOnB,GAAE,MAAI,UAAS;AAAC,UAAIoB,KAAEpB,GAAE;AAAG,cAAOiB,MAAG,OAAK,SAAOA,GAAEG,EAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,EAAC;AAAA,IAAE;AAAC,WAAOpB,GAAE,GAAGiB,IAAEE,EAAC;AAAA,EAAC,GAAE,OAAOF,IAAE;AAAC,QAAIG;AAAE,QAAGpB,GAAE,YAAUkB,GAAE,QAAQ,MAAEH,GAAE,KAAK,GAAE,MAAEE,EAAC,CAAC,EAAE,QAAM;AAAG,QAAIE,MAAGC,KAAET,GAAE,MAAM,KAAK,CAAAF,OAAGS,GAAE,QAAQ,MAAET,GAAE,SAAS,KAAK,GAAE,MAAEQ,EAAC,CAAC,CAAC,MAAI,OAAK,SAAOG,GAAE;AAAS,WAAOD,MAAG,QAAMA,GAAE,WAAS,SAAIH,GAAEC,EAAC,GAAE;AAAA,EAAG,GAAE,eAAeA,IAAE;AAAC,IAAAN,GAAE,MAAM,KAAKM,EAAC,GAAEN,GAAE,QAAM,EAAEA,GAAE,OAAM,CAAAQ,OAAGA,GAAE,OAAO;AAAA,EAAC,GAAE,iBAAiBF,IAAE;AAAC,QAAIE,KAAER,GAAE,MAAM,UAAU,CAAAS,OAAGA,GAAE,OAAKH,EAAC;AAAE,IAAAE,OAAI,MAAIR,GAAE,MAAM,OAAOQ,IAAE,CAAC;AAAA,EAAC,EAAC;AAAE,UAAEjB,IAAEgB,EAAC,GAAET,GAAE,EAAC,WAAU,SAAE,MAAIY,GAAEX,EAAC,CAAC,GAAE,OAAOO,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,UAAQ,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASK,GAAEL,IAAE;AAAC,QAAG,CAACP,GAAE,SAAO,CAACA,GAAE,MAAM,SAASO,GAAE,MAAM,EAAE;AAAO,QAAIE,KAAER,GAAE,MAAM,OAAO,CAAAS,OAAGA,GAAE,SAAS,aAAW,KAAE,EAAE,IAAI,CAAAA,OAAGA,GAAE,OAAO;AAAE,YAAOH,GAAE,KAAI;AAAA,MAAC,KAAKI,GAAE;AAAM,UAAEJ,GAAE,aAAa;AAAE;AAAA,MAAM,KAAKI,GAAE;AAAA,MAAU,KAAKA,GAAE;AAAQ,YAAGJ,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEE,IAAE,EAAE,WAAS,EAAE,UAAU,MAAI,EAAE,SAAQ;AAAC,cAAIV,KAAEE,GAAE,MAAM,KAAK,CAAAY,OAAG;AAAC,gBAAIC;AAAE,mBAAOD,GAAE,cAAYC,KAAEf,GAAEC,EAAC,MAAI,OAAK,SAAOc,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAf,MAAGS,GAAE,OAAOT,GAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAKY,GAAE;AAAA,MAAW,KAAKA,GAAE;AAAU,YAAGJ,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEE,IAAE,EAAE,OAAK,EAAE,UAAU,MAAI,EAAE,SAAQ;AAAC,cAAIV,KAAEE,GAAE,MAAM,KAAK,CAAAY,OAAG;AAAC,gBAAIC;AAAE,mBAAOD,GAAE,cAAYC,KAAEf,GAAEc,GAAE,OAAO,MAAI,OAAK,SAAOC,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAf,MAAGS,GAAE,OAAOT,GAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAKY,GAAE;AAAM;AAAC,UAAAJ,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,cAAIG,KAAET,GAAE,MAAM,KAAK,CAAAF,OAAG;AAAC,gBAAIc;AAAE,mBAAOd,GAAE,cAAYc,KAAEd,GAAEA,GAAE,OAAO,MAAI,OAAK,SAAOc,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAH,MAAGF,GAAE,OAAOE,GAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIK,KAAE,SAAE,MAAI;AAAC,QAAIR;AAAE,YAAOA,KAAEI,GAAEX,EAAC,MAAI,OAAK,SAAOO,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAO,UAAE,MAAI;AAAC,UAAE,CAACQ,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAOzB,GAAE,iBAAe,OAAO;AAAO,eAASiB,KAAG;AAAC,QAAAC,GAAE,OAAOlB,GAAE,YAAY;AAAA,MAAC;AAAC,aAAOyB,GAAE,MAAM,iBAAiB,SAAQR,EAAC,GAAE,MAAI;AAAC,YAAIE;AAAE,SAACA,KAAEM,GAAE,UAAQ,QAAMN,GAAE,oBAAoB,SAAQF,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,UAASA,IAAE,MAAKE,IAAE,MAAKC,IAAE,GAAGX,GAAC,IAAET,IAAEuB,KAAE,EAAC,KAAIb,IAAE,IAAGF,IAAE,MAAK,cAAa,mBAAkBI,GAAE,OAAM,oBAAmBE,GAAE,OAAM,WAAUQ,GAAC;AAAE,WAAO,EAAE,UAAE,CAAC,GAAGH,MAAG,QAAMJ,GAAE,SAAO,OAAK,EAAG,EAAC,CAACI,EAAC,GAAEJ,GAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACS,IAAEE,EAAC,MAAI,EAAE,GAAEb,GAAG,EAAC,UAAST,GAAE,QAAO,KAAIoB,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKJ,IAAE,UAASH,IAAE,MAAKO,IAAE,OAAME,GAAC,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,UAASH,IAAE,YAAW,EAAC,GAAGnB,IAAE,GAAGU,GAAGL,IAAE,CAAC,cAAa,gBAAe,IAAI,CAAC,EAAC,GAAE,MAAK,CAAC,GAAE,OAAML,IAAE,OAAMC,IAAE,MAAK,aAAY,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAE,IAAIsB,OAAI,CAAAvB,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAIuB,OAAI,CAAC,CAAC;AAAE,IAAI,KAAG,gBAAE,EAAC,MAAK,oBAAmB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAM3B,IAAE,EAAC,OAAMC,IAAE,OAAMG,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAII;AAAE,MAAIH,MAAGG,KAAET,GAAE,OAAK,OAAKS,KAAE,gCAAgCA,GAAE,CAAC,IAAGD,KAAEL,GAAE,kBAAkB,GAAEO,KAAEG,GAAE,EAAC,MAAK,kBAAiB,CAAC,GAAEF,KAAE,EAAE,EAAC,MAAK,wBAAuB,CAAC,GAAEC,KAAE,IAAE,IAAI,GAAEE,KAAE,SAAE,OAAK,EAAC,OAAMd,GAAE,OAAM,UAASA,GAAE,SAAQ,EAAE,GAAEe,KAAE,IAAE,CAAC;AAAE,EAAAV,GAAE,EAAC,IAAGO,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAII,KAAE,SAAE,MAAIK,GAAET,EAAC,CAAC;AAAE,YAAE,MAAIJ,GAAE,eAAe,EAAC,IAAGF,IAAE,SAAQU,IAAE,UAASF,GAAC,CAAC,CAAC,GAAE,YAAE,MAAIN,GAAE,iBAAiBF,EAAC,CAAC;AAAE,MAAIY,KAAE,SAAE,MAAI;AAAC,QAAIK;AAAE,aAAQA,KAAEf,GAAE,YAAY,UAAQ,OAAK,SAAOe,GAAE,QAAMjB;AAAA,EAAC,CAAC,GAAEgB,KAAE,SAAE,MAAId,GAAE,SAAS,SAAOR,GAAE,QAAQ,GAAEyB,KAAE,SAAE,MAAIjB,GAAE,QAAQ,MAAEA,GAAE,MAAM,KAAK,GAAE,MAAER,GAAE,KAAK,CAAC,CAAC,GAAEO,KAAE,SAAE,MAAIe,GAAE,QAAM,KAAGG,GAAE,SAAO,CAACjB,GAAE,sBAAsB,SAAOU,GAAE,QAAM,IAAE,EAAE;AAAE,WAASD,KAAG;AAAC,QAAIM;AAAE,IAAAf,GAAE,OAAOR,GAAE,KAAK,MAAIe,GAAE,SAAO,IAAGQ,KAAEF,GAAET,EAAC,MAAI,QAAMW,GAAE,MAAM;AAAA,EAAE;AAAC,WAASJ,KAAG;AAAC,IAAAJ,GAAE,SAAO;AAAA,EAAC;AAAC,WAASK,KAAG;AAAC,IAAAL,GAAE,SAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,OAAMQ,IAAE,UAASC,IAAE,GAAGE,GAAC,IAAE1B,IAAE4B,KAAE,EAAC,SAAQH,GAAE,OAAM,UAASH,GAAE,OAAM,QAAO,QAAQP,GAAE,QAAM,CAAC,EAAC,GAAEc,KAAE,EAAC,IAAGvB,IAAE,KAAIM,IAAE,MAAK,SAAQ,gBAAea,GAAE,QAAM,SAAO,SAAQ,mBAAkBf,GAAE,OAAM,oBAAmBC,GAAE,OAAM,iBAAgBW,GAAE,QAAM,OAAG,QAAO,UAASf,GAAE,OAAM,SAAQe,GAAE,QAAM,SAAOL,IAAE,SAAQK,GAAE,QAAM,SAAOH,IAAE,QAAOG,GAAE,QAAM,SAAOF,GAAC;AAAE,WAAO,EAAE,EAAC,UAASS,IAAE,YAAWH,IAAE,MAAKE,IAAE,OAAM3B,IAAE,OAAMG,IAAE,MAAK,mBAAkB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA/0C,IAAi1C0B,MAAGF;AAAp1C,IAAu1CG,MAAGH;;;ACAr/J,IAAI,IAAE,OAAO,cAAc;AAA3B,IAA6B,KAAG,gBAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,EAAC,GAAE,MAAMI,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIC,KAAE,IAAE,IAAI,GAAEC,KAAEC,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,SAAQ,SAAE,MAAI;AAAC,QAAIC;AAAE,YAAOA,KAAEH,GAAE,UAAQ,OAAK,SAAOG,GAAE;AAAA,EAAE,CAAC,GAAE,QAAQA,IAAE;AAAC,IAAAH,GAAE,UAAQG,GAAE,cAAc,YAAU,WAASA,GAAE,eAAe,GAAEH,GAAE,MAAM,MAAM,GAAEA,GAAE,MAAM,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAE,EAAC,EAAC,CAAC,GAAEI,KAAE,EAAE,EAAC,MAAK,oBAAmB,CAAC;AAAE,SAAO,QAAE,GAAE,EAAC,WAAUJ,IAAE,YAAWC,IAAE,aAAYG,GAAC,CAAC,GAAE,MAAI,EAAE,EAAC,YAAWP,IAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMC,IAAE,OAAMC,IAAE,MAAK,cAAa,CAAC;AAAC,EAAC,CAAC;AAAnhB,IAAqhBM,MAAG,gBAAE,EAAC,MAAK,UAAS,OAAM,EAAC,qBAAoB,CAAAR,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,YAAW,EAAC,MAAK,SAAQ,SAAQ,OAAM,GAAE,gBAAe,EAAC,MAAK,SAAQ,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,OAAM,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,MAAKC,IAAE,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIK;AAAE,MAAIF,MAAGE,KAAET,GAAE,OAAK,OAAKS,KAAE,qBAAqBP,GAAE,CAAC,IAAGQ,KAAE,OAAE,GAAE,IAAI,GAAE,CAACJ,IAAEK,EAAC,IAAE,EAAE,SAAE,MAAIX,GAAE,UAAU,GAAE,CAAAY,OAAGX,GAAE,qBAAoBW,EAAC,GAAE,SAAE,MAAIZ,GAAE,cAAc,CAAC;AAAE,WAASa,KAAG;AAAC,IAAAF,GAAE,CAACL,GAAE,KAAK;AAAA,EAAC;AAAC,MAAID,KAAE,IAAE,IAAI,GAAES,KAAEJ,OAAI,OAAKL,KAAEK,GAAE,WAAUK,KAAEJ,GAAE,SAAE,OAAK,EAAC,IAAGX,GAAE,IAAG,MAAKE,GAAE,KAAI,EAAE,GAAEY,EAAC;AAAE,EAAAV,GAAE,EAAC,IAAGU,IAAE,KAAIA,GAAC,CAAC;AAAE,WAAS,EAAEF,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAEC,GAAE;AAAA,EAAC;AAAC,WAASG,GAAEJ,IAAE;AAAC,IAAAA,GAAE,QAAME,GAAE,SAAOF,GAAE,eAAe,GAAEC,GAAE,KAAGD,GAAE,QAAME,GAAE,SAAO,EAAEF,GAAE,aAAa;AAAA,EAAC;AAAC,WAASK,GAAEL,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIM,KAAE,SAAE,MAAI;AAAC,QAAIN,IAAEO;AAAE,YAAOA,MAAGP,KAAEE,GAAEA,EAAC,MAAI,OAAK,SAAOF,GAAE,YAAU,OAAK,SAAOO,GAAE,KAAKP,IAAE,MAAM;AAAA,EAAC,CAAC;AAAE,SAAO,UAAE,MAAI;AAAC,UAAE,CAACM,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAOlB,GAAE,mBAAiB,OAAO;AAAO,eAASY,KAAG;AAAC,QAAAD,GAAEX,GAAE,cAAc;AAAA,MAAC;AAAC,aAAOkB,GAAE,MAAM,iBAAiB,SAAQN,EAAC,GAAE,MAAI;AAAC,YAAIO;AAAE,SAACA,KAAED,GAAE,UAAQ,QAAMC,GAAE,oBAAoB,SAAQP,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKA,IAAE,OAAMO,IAAE,MAAKC,IAAE,UAASC,IAAE,GAAGC,GAAC,IAAEtB,IAAEuB,KAAE,EAAC,SAAQjB,GAAE,MAAK,GAAEkB,KAAE,EAAC,IAAGjB,IAAE,KAAIO,IAAE,MAAK,UAAS,MAAKC,GAAE,OAAM,UAASM,OAAI,KAAG,IAAEA,IAAE,gBAAef,GAAE,OAAM,mBAAkBI,MAAG,OAAK,SAAOA,GAAE,WAAW,OAAM,oBAAmBA,MAAG,OAAK,SAAOA,GAAE,YAAY,OAAM,SAAQ,GAAE,SAAQM,IAAE,YAAWC,GAAC;AAAE,WAAO,EAAE,UAAE,CAACL,MAAG,QAAMN,GAAE,SAAO,OAAK,EAAE,GAAED,GAAE,EAAC,UAASoB,GAAE,QAAO,IAAG,SAAQ,MAAK,YAAW,QAAO,MAAG,UAAS,MAAG,SAAQnB,GAAE,OAAM,MAAKc,IAAE,UAASE,GAAE,UAAS,MAAKV,IAAE,OAAMO,GAAC,CAAC,CAAC,IAAE,MAAK,EAAE,EAAC,UAASK,IAAE,YAAW,EAAC,GAAGtB,IAAE,GAAGqB,GAAED,IAAE,CAAC,cAAa,gBAAgB,CAAC,EAAC,GAAE,MAAKC,IAAE,OAAMrB,IAAE,OAAMC,IAAE,MAAK,SAAQ,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAArwE,IAAuwE,KAAGiB;AAA1wE,IAA4wEM,MAAGN;;;ACA/3F,IAAIO,KAAE,gBAAE,EAAC,OAAM,EAAC,SAAQ,EAAC,MAAK,UAAS,UAAS,KAAE,EAAC,GAAE,MAAMC,IAAE;AAAC,MAAIC,KAAE,IAAE,IAAE;AAAE,SAAM,MAAIA,GAAE,QAAM,EAAE,GAAE,EAAC,IAAG,UAAS,MAAK,UAAS,UAASC,GAAE,WAAU,QAAQC,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAE,QAAIC,IAAEC,KAAE;AAAG,aAASC,KAAG;AAAC,UAAIJ;AAAE,UAAGG,QAAK,GAAE;AAAC,QAAAD,MAAG,qBAAqBA,EAAC;AAAE;AAAA,MAAM;AAAC,WAAIF,KAAEF,GAAE,YAAU,QAAME,GAAE,KAAKF,EAAC,GAAE;AAAC,QAAAC,GAAE,QAAM,OAAG,qBAAqBG,EAAC;AAAE;AAAA,MAAM;AAAC,MAAAA,KAAE,sBAAsBE,EAAC;AAAA,IAAC;AAAC,IAAAF,KAAE,sBAAsBE,EAAC;AAAA,EAAC,EAAC,CAAC,IAAE;AAAI,EAAC,CAAC;;;ACAsU,IAAIC,OAAI,CAAAC,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,KAAID,OAAI,CAAC,CAAC;AAA9E,IAAgFE,OAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,EAAE,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,OAAI,CAAC,CAAC;AAAE,IAAIE,KAAE,OAAO,aAAa;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAE,OAAEH,IAAE,IAAI;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIN,KAAE,IAAI,MAAM,IAAIK,EAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBL,IAAEI,EAAC,GAAEJ;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,IAAI,IAAE,OAAO,gBAAgB;AAA7B,IAA+BC,MAAG,gBAAE,EAAC,MAAK,YAAW,OAAM,EAAC,QAAO,CAAAF,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,eAAc,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,KAAI,GAAE,cAAa,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,EAAC,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,OAAMC,IAAE,OAAMN,IAAE,MAAKE,GAAC,GAAE;AAAC,MAAIM;AAAE,MAAIC,KAAE,KAAGD,KAAEH,GAAE,kBAAgB,OAAKG,KAAEH,GAAE,YAAY,GAAEK,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,SAAE,MAAIP,GAAE,kBAAgB,IAAI,GAAEQ,KAAE,SAAE,MAAID,GAAE,QAAMP,GAAE,gBAAcI,GAAE,KAAK;AAAE,WAASK,GAAEC,IAAE;AAAC,QAAIC;AAAE,QAAIC,KAAE,EAAEC,GAAE,KAAK,OAAMC,EAAC,GAAEA,KAAE,EAAED,GAAE,OAAO,OAAMC,EAAC,GAAEC,KAAEH,GAAE,OAAO,CAAAI,OAAG;AAAC,UAAIC;AAAE,aAAM,GAAGA,KAAEH,GAAEE,EAAC,MAAI,QAAMC,GAAE,aAAa,UAAU;AAAA,IAAE,CAAC;AAAE,QAAGP,KAAE,KAAGA,KAAEE,GAAE,SAAO,GAAE;AAAC,UAAII,KAAE,EAAEZ,GAAE,UAAQ,OAAK,IAAE,KAAK,KAAKM,KAAEN,GAAE,KAAK,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAE,KAAK,KAAKM,EAAC,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC,GAAEO,KAAE,EAAED,IAAE,EAAC,CAAC,CAAC,GAAE,MAAIJ,GAAE,QAAQG,GAAE,CAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAIH,GAAE,QAAQG,GAAEA,GAAE,SAAO,CAAC,CAAC,EAAC,CAAC;AAAE,MAAAE,OAAI,OAAKb,GAAE,QAAMa,KAAGJ,GAAE,KAAK,QAAMD,IAAEC,GAAE,OAAO,QAAMC;AAAA,IAAC,OAAK;AAAC,UAAIE,KAAEJ,GAAE,MAAM,GAAEF,EAAC,GAAEQ,KAAE,CAAC,GAAGN,GAAE,MAAMF,EAAC,GAAE,GAAGM,EAAC,EAAE,KAAK,CAAAG,OAAGJ,GAAE,SAASI,EAAC,CAAC;AAAE,UAAG,CAACD,GAAE;AAAO,UAAIE,MAAGT,KAAEC,GAAE,QAAQM,EAAC,MAAI,OAAKP,KAAEE,GAAE,cAAc;AAAM,MAAAO,OAAI,OAAKA,KAAEP,GAAE,cAAc,QAAOT,GAAE,QAAMgB,IAAEP,GAAE,KAAK,QAAMD,IAAEC,GAAE,OAAO,QAAMC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAID,KAAE,EAAC,eAAc,SAAE,MAAI;AAAC,QAAIH,IAAEE;AAAE,YAAOA,MAAGF,KAAEN,GAAE,UAAQ,OAAKM,KAAEV,GAAE,iBAAe,OAAKY,KAAE;AAAA,EAAI,CAAC,GAAE,aAAY,SAAE,MAAIZ,GAAE,WAAS,aAAW,YAAY,GAAE,YAAW,SAAE,MAAIA,GAAE,SAAO,WAAS,MAAM,GAAE,MAAKK,IAAE,QAAOC,IAAE,iBAAiBI,IAAE;AAAC,IAAAF,GAAE,UAAQE,MAAGb,GAAE,UAASa,EAAC,GAAEH,GAAE,SAAOE,GAAEC,EAAC;AAAA,EAAC,GAAE,YAAYA,IAAE;AAAC,QAAII;AAAE,QAAGT,GAAE,MAAM,SAASK,EAAC,EAAE;AAAO,QAAIE,KAAEP,GAAE,MAAMD,GAAE,KAAK;AAAE,QAAGC,GAAE,MAAM,KAAKK,EAAC,GAAEL,GAAE,QAAM,EAAEA,GAAE,OAAMS,EAAC,GAAE,CAACP,GAAE,OAAM;AAAC,UAAIQ,MAAGD,KAAET,GAAE,MAAM,QAAQO,EAAC,MAAI,OAAKE,KAAEV,GAAE;AAAM,MAAAW,OAAI,OAAKX,GAAE,QAAMW;AAAA,IAAE;AAAA,EAAC,GAAE,cAAcL,IAAE;AAAC,QAAIE,KAAEP,GAAE,MAAM,QAAQK,EAAC;AAAE,IAAAE,OAAI,MAAIP,GAAE,MAAM,OAAOO,IAAE,CAAC;AAAA,EAAC,GAAE,cAAcF,IAAE;AAAC,IAAAJ,GAAE,MAAM,SAASI,EAAC,MAAIJ,GAAE,MAAM,KAAKI,EAAC,GAAEJ,GAAE,QAAM,EAAEA,GAAE,OAAMQ,EAAC;AAAA,EAAE,GAAE,gBAAgBJ,IAAE;AAAC,QAAIE,KAAEN,GAAE,MAAM,QAAQI,EAAC;AAAE,IAAAE,OAAI,MAAIN,GAAE,MAAM,OAAOM,IAAE,CAAC;AAAA,EAAC,EAAC;AAAE,UAAEd,IAAEe,EAAC;AAAE,MAAIQ,KAAE,IAAE,EAAC,MAAK,CAAC,GAAE,QAAO,CAAC,EAAC,CAAC,GAAEC,KAAE,IAAE,KAAE;AAAE,YAAE,MAAI;AAAC,IAAAA,GAAE,QAAM;AAAA,EAAE,CAAC,GAAE,QAAE,GAAE,SAAE,MAAIA,GAAE,QAAM,OAAKD,GAAE,KAAK,CAAC;AAAE,MAAIE,KAAE,SAAE,MAAIvB,GAAE,aAAa;AAAE,SAAO,UAAE,MAAI;AAAC,UAAE,CAACuB,EAAC,GAAE,MAAI;AAAC,UAAIb;AAAE,aAAOD,IAAGC,KAAEV,GAAE,kBAAgB,OAAKU,KAAEV,GAAE,YAAY;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,QAAG,CAACO,GAAE,SAAOC,GAAE,SAAO,QAAMK,GAAE,KAAK,MAAM,UAAQ,EAAE;AAAO,QAAIH,KAAE,EAAEG,GAAE,KAAK,OAAMC,EAAC;AAAE,IAAAJ,GAAE,KAAK,CAACI,IAAEC,OAAID,GAAED,GAAE,KAAK,MAAME,EAAC,CAAC,MAAID,GAAEA,EAAC,CAAC,KAAGD,GAAE,iBAAiBH,GAAE,UAAU,CAAAI,OAAGA,GAAEA,EAAC,MAAIA,GAAED,GAAE,KAAK,MAAML,GAAE,KAAK,CAAC,CAAC,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAIE,KAAE,EAAC,eAAcN,GAAE,MAAK;AAAE,WAAO,EAAE,UAAE,CAACC,GAAE,MAAM,UAAQ,KAAG,EAAER,IAAE,EAAC,SAAQ,MAAI;AAAC,eAAQe,MAAKP,GAAE,OAAM;AAAC,YAAIS,KAAEA,GAAEF,EAAC;AAAE,aAAIE,MAAG,OAAK,SAAOA,GAAE,cAAY,EAAE,QAAOA,GAAE,MAAM,GAAE;AAAA,MAAE;AAAC,aAAM;AAAA,IAAE,EAAC,CAAC,GAAE,EAAE,EAAC,YAAW,EAAC,GAAGnB,IAAE,GAAG0B,GAAGrB,IAAE,CAAC,iBAAgB,gBAAe,UAAS,YAAW,UAAU,CAAC,EAAC,GAAE,UAAS,CAAC,GAAE,MAAKU,IAAE,OAAMT,IAAE,OAAMN,IAAE,MAAK,WAAU,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAr/E,IAAu/E6B,MAAG,gBAAE,EAAC,MAAK,WAAU,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMxB,IAAE,EAAC,OAAMC,IAAE,OAAMN,GAAC,GAAE;AAAC,MAAIE,KAAEE,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIK,KAAE,EAAC,eAAcP,GAAE,cAAc,MAAK,GAAEQ,KAAE,EAAC,MAAK,WAAU,oBAAmBR,GAAE,YAAY,MAAK;AAAE,WAAO,EAAE,EAAC,UAASQ,IAAE,YAAWL,IAAE,MAAKI,IAAE,OAAMH,IAAE,OAAMN,IAAE,MAAK,UAAS,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvyF,IAAyyF,KAAG,gBAAE,EAAC,MAAK,OAAM,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMK,IAAE,EAAC,OAAMC,IAAE,OAAMN,IAAE,QAAOE,GAAC,GAAE;AAAC,MAAIiB;AAAE,MAAIV,MAAGU,KAAEd,GAAE,OAAK,OAAKc,KAAE,uBAAuBV,GAAE,CAAC,IAAGC,KAAEN,GAAE,KAAK,GAAEO,KAAE,IAAE,IAAI;AAAE,EAAAT,GAAE,EAAC,IAAGS,IAAE,KAAIA,GAAC,CAAC,GAAE,UAAE,MAAID,GAAE,YAAYC,EAAC,CAAC,GAAE,YAAE,MAAID,GAAE,cAAcC,EAAC,CAAC;AAAE,MAAIC,KAAE,OAAE,CAAC,GAAEC,KAAE,SAAE,MAAI;AAAC,QAAGD,GAAE,OAAM;AAAC,UAAIQ,KAAER,GAAE,MAAM,KAAK,QAAQH,EAAC;AAAE,aAAOW,OAAI,KAAGR,GAAE,MAAM,KAAK,KAAKH,EAAC,IAAE,IAAEW;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE,CAAC,GAAEN,KAAE,SAAE,MAAI;AAAC,QAAIM,KAAEV,GAAE,KAAK,MAAM,QAAQC,EAAC;AAAE,WAAOS,OAAI,KAAGP,GAAE,QAAMO;AAAA,EAAC,CAAC,GAAEF,KAAE,SAAE,MAAIJ,GAAE,UAAQJ,GAAE,cAAc,KAAK;AAAE,WAASgB,GAAEN,IAAE;AAAC,QAAIC;AAAE,QAAIL,KAAEI,GAAE;AAAE,QAAGJ,OAAI,EAAE,WAASN,GAAE,WAAW,UAAQ,QAAO;AAAC,UAAIY,MAAGD,KAAEZ,GAAEE,EAAC,MAAI,OAAK,SAAOU,GAAE,eAAcE,KAAEb,GAAE,KAAK,MAAM,UAAU,CAAAe,OAAGN,GAAEM,EAAC,MAAIH,EAAC;AAAE,MAAAC,OAAI,MAAIb,GAAE,iBAAiBa,EAAC;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAC,WAASW,GAAEP,IAAE;AAAC,QAAIJ,KAAEN,GAAE,KAAK,MAAM,IAAI,CAAAY,OAAGH,GAAEG,EAAC,CAAC,EAAE,OAAO,OAAO;AAAE,QAAGF,GAAE,QAAMD,GAAE,SAAOC,GAAE,QAAMD,GAAE,OAAM;AAAC,MAAAC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEV,GAAE,iBAAiBI,GAAE,KAAK;AAAE;AAAA,IAAM;AAAC,YAAOM,GAAE,KAAI;AAAA,MAAC,KAAKD,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEM,GAAE,MAAI,EAAEV,IAAE,EAAE,KAAK,CAAC;AAAA,MAAE,KAAKG,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEM,GAAE,MAAI,EAAEV,IAAE,EAAE,IAAI,CAAC;AAAA,IAAC;AAAC,QAAGU,GAAE,MAAI,EAAEhB,GAAE,YAAY,OAAM,EAAC,WAAU;AAAC,aAAOU,GAAE,QAAMD,GAAE,UAAQ,EAAEH,IAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMD,GAAE,YAAU,EAAEH,IAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,GAAE,aAAY;AAAC,aAAOI,GAAE,QAAMD,GAAE,YAAU,EAAEH,IAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMD,GAAE,aAAW,EAAEH,IAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,EAAC,CAAC,CAAC,MAAI,EAAE,QAAQ,QAAOI,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIQ,KAAE,IAAE,KAAE;AAAE,WAASpB,KAAG;AAAC,QAAIY;AAAE,IAAAQ,GAAE,UAAQA,GAAE,QAAM,MAAG,CAACvB,GAAE,cAAYe,KAAED,GAAER,EAAC,MAAI,QAAMS,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEV,GAAE,iBAAiBI,GAAE,KAAK,GAAE,EAAE,MAAI;AAAC,MAAAc,GAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAG;AAAC,WAASb,GAAEK,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIH,KAAEjB,GAAE,SAAE,OAAK,EAAC,IAAGK,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEK,EAAC;AAAE,SAAM,MAAI;AAAC,QAAIW,IAAEC;AAAE,QAAIH,KAAE,EAAC,UAASF,GAAE,OAAM,WAAUI,KAAEjB,GAAE,aAAW,OAAKiB,KAAE,MAAE,GAAE,EAAC,GAAGN,GAAC,IAAEX,IAAEgB,KAAE,EAAC,KAAIV,IAAE,WAAUgB,IAAE,aAAYZ,IAAE,SAAQP,IAAE,IAAGC,IAAE,MAAK,OAAM,MAAKQ,GAAE,OAAM,kBAAiBM,KAAEJ,GAAET,GAAE,OAAO,MAAMI,GAAE,KAAK,CAAC,MAAI,OAAK,SAAOS,GAAE,IAAG,iBAAgBL,GAAE,OAAM,UAASA,GAAE,QAAM,IAAE,IAAG,UAASb,GAAE,WAAS,OAAG,OAAM;AAAE,WAAO,EAAE,EAAC,UAASgB,IAAE,YAAWL,IAAE,MAAKI,IAAE,OAAMd,IAAE,OAAMN,IAAE,MAAK,MAAK,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvxJ,IAAyxJ8B,MAAG,gBAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMzB,IAAE,EAAC,OAAMC,IAAE,OAAMN,GAAC,GAAE;AAAC,MAAIE,KAAEE,GAAE,WAAW;AAAE,SAAM,MAAI;AAAC,QAAIK,KAAE,EAAC,eAAcP,GAAE,cAAc,MAAK;AAAE,WAAO,EAAE,EAAC,YAAWG,IAAE,UAAS,CAAC,GAAE,MAAKI,IAAE,OAAMT,IAAE,OAAMM,IAAE,MAAK,YAAW,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthK,IAAwhKyB,MAAG,gBAAE,EAAC,MAAK,YAAW,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,MAAM1B,IAAE,EAAC,OAAMC,IAAE,OAAMN,IAAE,QAAOE,GAAC,GAAE;AAAC,MAAIwB;AAAE,MAAIjB,MAAGiB,KAAErB,GAAE,OAAK,OAAKqB,KAAE,yBAAyBjB,GAAE,CAAC,IAAGC,KAAEN,GAAE,UAAU,GAAEO,KAAE,IAAE,IAAI;AAAE,EAAAT,GAAE,EAAC,IAAGS,IAAE,KAAIA,GAAC,CAAC,GAAE,UAAE,MAAID,GAAE,cAAcC,EAAC,CAAC,GAAE,YAAE,MAAID,GAAE,gBAAgBC,EAAC,CAAC;AAAE,MAAIC,KAAE,OAAE,CAAC,GAAEC,KAAE,SAAE,MAAI;AAAC,QAAGD,GAAE,OAAM;AAAC,UAAIe,KAAEf,GAAE,MAAM,OAAO,QAAQH,EAAC;AAAE,aAAOkB,OAAI,KAAGf,GAAE,MAAM,OAAO,KAAKH,EAAC,IAAE,IAAEkB;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE,CAAC,GAAEb,KAAE,SAAE,MAAI;AAAC,QAAIa,KAAEjB,GAAE,OAAO,MAAM,QAAQC,EAAC;AAAE,WAAOgB,OAAI,KAAGd,GAAE,QAAMc;AAAA,EAAC,CAAC,GAAET,KAAE,SAAE,MAAIJ,GAAE,UAAQJ,GAAE,cAAc,KAAK;AAAE,SAAM,MAAI;AAAC,QAAIO;AAAE,QAAIU,KAAE,EAAC,UAAST,GAAE,MAAK,GAAE,EAAC,UAASU,IAAE,GAAGpB,GAAC,IAAEH,IAAEU,KAAE,EAAC,KAAIJ,IAAE,IAAGF,IAAE,MAAK,YAAW,oBAAmBQ,KAAEE,GAAET,GAAE,KAAK,MAAMI,GAAE,KAAK,CAAC,MAAI,OAAK,SAAOG,GAAE,IAAG,UAASC,GAAE,QAAMU,KAAE,GAAE;AAAE,WAAM,CAACV,GAAE,SAAOb,GAAE,WAAS,CAACA,GAAE,SAAO,EAAE,GAAE,EAAC,IAAG,QAAO,eAAc,MAAG,GAAGU,GAAC,CAAC,IAAE,EAAE,EAAC,UAASA,IAAE,YAAWP,IAAE,MAAKmB,IAAE,OAAMrB,IAAE,OAAMN,IAAE,UAASgC,GAAE,SAAOA,GAAE,gBAAe,SAAQd,GAAE,OAAM,MAAK,WAAU,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACAzqO,SAASe,GAAEC,IAAE;AAAC,MAAIC,KAAE,EAAC,QAAO,MAAE;AAAE,SAAM,IAAIC,OAAI;AAAC,QAAG,CAACD,GAAE,OAAO,QAAOA,GAAE,SAAO,MAAGD,GAAE,GAAGE,EAAC;AAAA,EAAC;AAAC;;;ACAmB,SAASC,GAAEC,OAAKC,IAAE;AAAC,EAAAD,MAAGC,GAAE,SAAO,KAAGD,GAAE,UAAU,IAAI,GAAGC,EAAC;AAAC;AAAC,SAASC,GAAEF,OAAKC,IAAE;AAAC,EAAAD,MAAGC,GAAE,SAAO,KAAGD,GAAE,UAAU,OAAO,GAAGC,EAAC;AAAC;AAAC,IAAIE,MAAG,CAAAC,QAAIA,GAAE,WAAS,YAAWA,GAAE,YAAU,aAAYA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEL,IAAEC,IAAE;AAAC,MAAIG,KAAE,EAAE;AAAE,MAAG,CAACJ,GAAE,QAAOI,GAAE;AAAQ,MAAG,EAAC,oBAAmBE,IAAE,iBAAgBC,GAAC,IAAE,iBAAiBP,EAAC,GAAE,CAACQ,IAAEC,EAAC,IAAE,CAACH,IAAEC,EAAC,EAAE,IAAI,CAAAG,OAAG;AAAC,QAAG,CAACC,KAAE,CAAC,IAAED,GAAE,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,IAAI,CAAAE,OAAGA,GAAE,SAAS,IAAI,IAAE,WAAWA,EAAC,IAAE,WAAWA,EAAC,IAAE,GAAG,EAAE,KAAK,CAACA,IAAEC,OAAIA,KAAED,EAAC;AAAE,WAAOD;AAAA,EAAC,CAAC;AAAE,SAAOH,OAAI,IAAEJ,GAAE,WAAW,MAAIH,GAAE,UAAU,GAAEO,KAAEC,EAAC,IAAER,GAAE,UAAU,GAAEG,GAAE,IAAI,MAAIH,GAAE,WAAW,CAAC,GAAEG,GAAE;AAAO;AAAC,SAAS,EAAEJ,IAAEC,IAAEG,IAAEE,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAE,GAAEC,KAAEF,OAAI,SAAOA,GAAEA,EAAC,IAAE,MAAI;AAAA,EAAC;AAAE,SAAON,GAAEF,IAAE,GAAGO,EAAC,GAAER,GAAEC,IAAE,GAAGC,IAAE,GAAGG,EAAC,GAAEK,GAAE,UAAU,MAAI;AAAC,IAAAP,GAAEF,IAAE,GAAGI,EAAC,GAAEL,GAAEC,IAAE,GAAGM,EAAC,GAAEG,GAAE,IAAIJ,GAAEL,IAAE,CAAAW,QAAIT,GAAEF,IAAE,GAAGM,IAAE,GAAGL,EAAC,GAAEF,GAAEC,IAAE,GAAGO,EAAC,GAAEG,GAAEC,EAAC,EAAE,CAAC;AAAA,EAAC,CAAC,GAAEF,GAAE,IAAI,MAAIP,GAAEF,IAAE,GAAGC,IAAE,GAAGG,IAAE,GAAGE,IAAE,GAAGC,EAAC,CAAC,GAAEE,GAAE,IAAI,MAAIC,GAAE,WAAW,CAAC,GAAED,GAAE;AAAO;;;ACAxQ,SAASK,GAAEC,KAAE,IAAG;AAAC,SAAOA,GAAE,MAAM,KAAK,EAAE,OAAO,CAAAC,OAAGA,GAAE,SAAO,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,mBAAmB;AAAE,IAAIC,OAAI,CAAAC,QAAIA,GAAE,UAAQ,WAAUA,GAAE,SAAO,UAASA,KAAID,OAAI,CAAC,CAAC;AAAE,SAASE,MAAI;AAAC,SAAO,OAAEH,IAAE,IAAI,MAAI;AAAI;AAAC,SAASI,MAAI;AAAC,MAAIN,KAAE,OAAEE,IAAE,IAAI;AAAE,MAAGF,OAAI,KAAK,OAAM,IAAI,MAAM,8EAA8E;AAAE,SAAOA;AAAC;AAAC,SAASO,MAAI;AAAC,MAAIP,KAAE,OAAEQ,IAAE,IAAI;AAAE,MAAGR,OAAI,KAAK,OAAM,IAAI,MAAM,8EAA8E;AAAE,SAAOA;AAAC;AAAC,IAAIQ,KAAE,OAAO,gBAAgB;AAAE,SAASC,GAAET,IAAE;AAAC,SAAM,cAAaA,KAAES,GAAET,GAAE,QAAQ,IAAEA,GAAE,MAAM,OAAO,CAAC,EAAC,OAAMC,GAAC,MAAIA,OAAI,SAAS,EAAE,SAAO;AAAC;AAAC,SAASS,GAAEV,IAAE;AAAC,MAAIC,KAAE,IAAE,CAAC,CAAC,GAAEG,KAAE,IAAE,KAAE;AAAE,YAAE,MAAIA,GAAE,QAAM,IAAE,GAAE,YAAE,MAAIA,GAAE,QAAM,KAAE;AAAE,WAASO,GAAEC,IAAEC,KAAEC,GAAE,QAAO;AAAC,QAAIC,KAAEd,GAAE,MAAM,UAAU,CAAC,EAAC,IAAGe,GAAC,MAAIA,OAAIJ,EAAC;AAAE,IAAAG,OAAI,OAAK,EAAEF,IAAE,EAAC,CAACC,GAAE,OAAO,IAAG;AAAC,MAAAb,GAAE,MAAM,OAAOc,IAAE,CAAC;AAAA,IAAC,GAAE,CAACD,GAAE,MAAM,IAAG;AAAC,MAAAb,GAAE,MAAMc,EAAC,EAAE,QAAM;AAAA,IAAQ,EAAC,CAAC,GAAE,CAACN,GAAER,EAAC,KAAGG,GAAE,UAAQJ,MAAG,QAAMA,GAAE;AAAA,EAAG;AAAC,WAASiB,GAAEL,IAAE;AAAC,QAAIC,KAAEZ,GAAE,MAAM,KAAK,CAAC,EAAC,IAAGc,GAAC,MAAIA,OAAIH,EAAC;AAAE,WAAOC,KAAEA,GAAE,UAAQ,cAAYA,GAAE,QAAM,aAAWZ,GAAE,MAAM,KAAK,EAAC,IAAGW,IAAE,OAAM,UAAS,CAAC,GAAE,MAAID,GAAEC,IAAEE,GAAE,OAAO;AAAA,EAAC;AAAC,SAAM,EAAC,UAASb,IAAE,UAASgB,IAAE,YAAWN,GAAC;AAAC;AAAC,IAAIO,KAAEV,GAAG;AAAT,IAAwBW,MAAG,gBAAE,EAAC,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAI,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,EAAC,GAAE,OAAM,EAAC,aAAY,MAAI,MAAG,YAAW,MAAI,MAAG,aAAY,MAAI,MAAG,YAAW,MAAI,KAAE,GAAE,MAAMnB,IAAE,EAAC,MAAKC,IAAE,OAAMG,IAAE,OAAMO,IAAE,QAAOM,GAAC,GAAE;AAAC,MAAIL,KAAE,IAAE,CAAC;AAAE,WAASC,KAAG;AAAC,IAAAD,GAAE,SAAOQ,GAAE,SAAQnB,GAAE,aAAa;AAAA,EAAC;AAAC,WAASc,KAAG;AAAC,IAAAH,GAAE,SAAO,CAACQ,GAAE,SAAQnB,GAAE,YAAY;AAAA,EAAC;AAAC,WAASe,KAAG;AAAC,IAAAJ,GAAE,SAAOQ,GAAE,SAAQnB,GAAE,aAAa;AAAA,EAAC;AAAC,WAASa,KAAG;AAAC,IAAAF,GAAE,SAAO,CAACQ,GAAE,SAAQnB,GAAE,YAAY;AAAA,EAAC;AAAC,MAAG,CAACI,IAAG,KAAGM,GAAG,EAAE,QAAM,MAAI,EAAEU,KAAG,EAAC,GAAGrB,IAAE,eAAca,IAAE,cAAaE,IAAE,eAAcC,IAAE,cAAaF,GAAC,GAAEH,EAAC;AAAE,MAAIW,KAAE,IAAE,IAAI,GAAEC,KAAE,SAAE,MAAIvB,GAAE,UAAQc,GAAE,UAAQA,GAAE,MAAM;AAAE,EAAAG,GAAE,EAAC,IAAGK,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAG,EAAC,MAAKE,IAAE,QAAOC,GAAC,IAAEnB,IAAG,GAAE,EAAC,UAAS,GAAE,YAAWoB,GAAC,IAAEnB,IAAG,GAAEa,KAAE,IAAEI,GAAE,QAAM,YAAU,QAAQ,GAAEG,KAAE,EAAC,OAAM,KAAE,GAAEC,KAAER,GAAG,GAAES,KAAE,EAAC,OAAM,MAAE,GAAEC,KAAEpB,GAAE,MAAI;AAAC,KAACmB,GAAE,SAAOT,GAAE,UAAQ,aAAWA,GAAE,QAAM,UAASM,GAAEE,EAAC,GAAEd,GAAE;AAAA,EAAE,CAAC;AAAE,YAAE,MAAI;AAAC,QAAIiB,KAAE,EAAEH,EAAC;AAAE,gBAAEG,EAAC;AAAA,EAAC,CAAC,GAAE,YAAE,MAAI;AAAC,QAAGR,GAAE,UAAQT,GAAE,UAAQc,IAAE;AAAC,UAAGJ,GAAE,SAAOJ,GAAE,UAAQ,WAAU;AAAC,QAAAA,GAAE,QAAM;AAAU;AAAA,MAAM;AAAC,QAAEA,GAAE,OAAM,EAAC,CAAC,QAAQ,GAAE,MAAIM,GAAEE,EAAC,GAAE,CAAC,SAAS,GAAE,MAAI,EAAEA,EAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAII,KAAEjC,GAAEC,GAAE,KAAK,GAAEiC,KAAElC,GAAEC,GAAE,SAAS,GAAE,IAAED,GAAEC,GAAE,OAAO,GAAEkC,KAAEnC,GAAEC,GAAE,OAAO,GAAE,IAAED,GAAEC,GAAE,KAAK,GAAEmC,KAAEpC,GAAEC,GAAE,SAAS,GAAEoC,MAAGrC,GAAEC,GAAE,OAAO;AAAE,YAAE,MAAI;AAAC,gBAAE,MAAI;AAAC,UAAGoB,GAAE,UAAQ,WAAU;AAAC,YAAIW,KAAEA,GAAET,EAAC;AAAE,YAAGS,cAAa,WAASA,GAAE,SAAO,GAAG,OAAM,IAAI,MAAM,iEAAiE;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASM,IAAGN,IAAE;AAAC,QAAIO,KAAEX,GAAE,SAAO,CAACF,GAAE,OAAMc,KAAER,GAAET,EAAC;AAAE,KAACiB,MAAG,EAAEA,cAAa,gBAAcD,OAAIT,GAAE,QAAM,MAAGL,GAAE,SAAOX,GAAE,GAAEW,GAAE,SAAOR,GAAE,GAAEe,GAAEP,GAAE,QAAM,EAAEe,IAAEP,IAAEC,IAAE,GAAEC,IAAE,CAAAM,OAAG;AAAC,MAAAX,GAAE,QAAM,OAAGW,OAAIzC,GAAE,YAAUgB,GAAE;AAAA,IAAC,CAAC,IAAE,EAAEwB,IAAE,GAAEJ,IAAEC,KAAGF,IAAE,CAAAM,OAAG;AAAC,MAAAX,GAAE,QAAM,OAAGW,OAAIzC,GAAE,aAAWU,GAAEqB,EAAC,MAAIV,GAAE,QAAM,UAASM,GAAEE,EAAC,GAAEd,GAAE;AAAA,IAAG,CAAC,CAAC;AAAA,EAAE;AAAC,SAAO,UAAE,MAAI;AAAC,UAAG,CAACU,EAAC,GAAE,CAACO,IAAEO,IAAEC,OAAI;AAAC,MAAAF,IAAGE,EAAC,GAAEZ,GAAE,QAAM;AAAA,IAAE,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,QAAEnB,IAAEsB,EAAC,GAAE7B,GAAG,SAAE,MAAI,EAAEmB,GAAE,OAAM,EAAC,CAAC,SAAS,GAAEA,GAAE,MAAK,CAAC,QAAQ,GAAEA,GAAE,OAAM,CAAC,IAAER,GAAE,KAAK,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,QAAOmB,IAAE,MAAKO,IAAE,OAAMC,IAAE,WAAUC,IAAE,SAAQ,IAAG,SAAQC,KAAG,OAAMC,KAAG,WAAUC,KAAG,SAAQC,KAAG,GAAGC,GAAC,IAAE7C,IAAE8C,MAAG,EAAC,KAAIxB,GAAC,GAAEyB,MAAG,EAAC,GAAGF,IAAE,GAAGpB,GAAE,SAAOD,GAAE,SAAO,EAAG,WAAS,EAAC,OAAM,eAAG,CAACpB,GAAE,OAAMyC,GAAE,OAAM,GAAGb,IAAE,GAAGC,EAAC,CAAC,EAAC,IAAE,CAAC,EAAC;AAAE,WAAO,EAAE,EAAC,YAAWc,KAAG,UAASD,KAAG,MAAK,CAAC,GAAE,OAAMnC,IAAE,OAAMP,IAAE,UAASc,IAAE,SAAQE,GAAE,UAAQ,WAAU,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA9wE,IAAgxE4B,MAAG7B;AAAnxE,IAAsxEE,MAAG,gBAAE,EAAC,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAI,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,EAAC,GAAE,OAAM,EAAC,aAAY,MAAI,MAAG,YAAW,MAAI,MAAG,aAAY,MAAI,MAAG,YAAW,MAAI,KAAE,GAAE,MAAMrB,IAAE,EAAC,MAAKC,IAAE,OAAMG,IAAE,OAAMO,GAAC,GAAE;AAAC,MAAIM,KAAE,EAAG,GAAEL,KAAE,SAAE,MAAIZ,GAAE,SAAO,QAAMiB,OAAI,QAAMA,GAAE,QAAMG,GAAE,UAAQA,GAAE,OAAKpB,GAAE,IAAI;AAAE,cAAE,MAAI;AAAC,QAAG,CAAC,CAAC,MAAG,KAAE,EAAE,SAASY,GAAE,KAAK,EAAE,OAAM,IAAI,MAAM,2EAA2E;AAAA,EAAC,CAAC;AAAE,MAAIC,KAAE,IAAED,GAAE,QAAM,YAAU,QAAQ,GAAEG,KAAEL,GAAE,MAAI;AAAC,IAAAG,GAAE,QAAM;AAAA,EAAQ,CAAC,GAAEG,KAAE,IAAE,IAAE,GAAEF,KAAE,EAAC,MAAKF,IAAE,QAAO,SAAE,MAAIZ,GAAE,UAAQ,CAACgB,GAAE,KAAK,EAAC;AAAE,SAAO,UAAE,MAAI;AAAC,gBAAE,MAAI;AAAC,MAAAA,GAAE,QAAM,OAAGJ,GAAE,QAAMC,GAAE,QAAM,YAAUJ,GAAEM,EAAC,MAAIF,GAAE,QAAM;AAAA,IAAS,CAAC;AAAA,EAAC,CAAC,GAAE,QAAEL,IAAEO,EAAC,GAAE,QAAEb,IAAEY,EAAC,GAAE,MAAI;AAAC,QAAIQ,KAAE2B,GAAGjD,IAAE,CAAC,QAAO,UAAS,WAAU,iBAAgB,iBAAgB,gBAAe,cAAc,CAAC,GAAEuB,KAAE,EAAC,SAAQvB,GAAE,QAAO;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,GAAGuB,IAAE,IAAG,WAAU,GAAE,YAAW,CAAC,GAAE,MAAK,CAAC,GAAE,OAAM,EAAC,GAAGZ,IAAE,SAAQ,MAAI,CAAC,EAAEqC,KAAG,EAAC,eAAc,MAAI/C,GAAE,aAAa,GAAE,cAAa,MAAIA,GAAE,YAAY,GAAE,eAAc,MAAIA,GAAE,aAAa,GAAE,cAAa,MAAIA,GAAE,YAAY,GAAE,GAAGG,IAAE,GAAGmB,IAAE,GAAGD,GAAC,GAAEX,GAAE,OAAO,CAAC,EAAC,GAAE,OAAM,CAAC,GAAE,UAASO,IAAE,SAAQL,GAAE,UAAQ,WAAU,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;", "names": ["a", "b", "i", "opts", "d", "m", "a", "b", "i", "k", "options", "u", "e", "r", "i", "f", "t", "e", "o", "a", "s", "e", "t", "r", "i", "o", "t", "e", "o", "o", "i", "t", "o", "e", "l", "n", "r", "n", "a", "e", "t", "i", "d", "t", "e", "r", "n", "s", "i", "r", "n", "o", "c", "e", "n", "o", "t", "r", "h", "i", "l", "m", "s", "x", "p", "L", "a", "d", "u", "t", "i", "n", "u", "e", "t", "n", "o", "w", "e", "n", "t", "o", "w", "f", "m", "l", "a", "e", "r", "t", "c", "o", "n", "h", "u", "r", "t", "e", "n", "s", "u", "o", "r", "e", "u", "n", "t", "i", "e", "t", "d", "o", "r", "l", "c", "f", "n", "N", "o", "S", "e", "r", "t", "i", "a", "n", "l", "y", "d", "m", "h", "T", "u", "c", "p", "f", "v", "s", "g", "R", "E", "u", "e", "t", "n", "i", "r", "d", "o", "n", "i", "e", "s", "t", "o", "o", "r", "f", "t", "n", "e", "t", "e", "n", "r", "u", "l", "c", "i", "f", "n", "t", "r", "s", "e", "i", "s", "t", "r", "n", "o", "f", "a", "h", "r", "y", "c", "o", "f", "u", "e", "S", "v", "d", "E", "w", "t", "n", "s", "b", "C", "A", "O", "M", "$", "B", "p", "R", "V", "i", "I", "l", "T", "j", "q", "g", "ue", "N", "E", "n", "e", "o", "r", "t", "d", "r", "n", "o", "w", "e", "t", "n", "r", "l", "o", "A", "e", "i", "m", "f", "a", "u", "T", "w", "d", "s", "F", "H", "N", "Q", "O", "E", "t", "e", "o", "r", "t", "n", "e", "s", "i", "c", "c", "o", "e", "l", "n", "t", "r", "w", "t", "r", "n", "c", "a", "o", "l", "s", "e", "f", "i", "l", "e", "o", "m", "e", "n", "t", "a", "o", "c", "w", "l", "r", "d", "t", "a", "n", "i", "l", "e", "m", "r", "o", "f", "i", "t", "E", "d", "f", "o", "a", "e", "u", "r", "n", "l", "N", "o", "i", "H", "t", "r", "u", "l", "f", "a", "n", "e", "M", "v", "e", "s", "u", "o", "t", "r", "f", "n", "u", "s", "e", "y", "o", "r", "i", "a", "t", "n", "d", "u", "w", "t", "o", "s", "e", "r", "n", "i", "K", "l", "d", "c", "f", "a", "g", "m", "e", "t", "i", "l", "r", "f", "$", "o", "s", "u", "H", "n", "a", "c", "v", "d", "g", "b", "P", "T", "L", "z", "l", "H", "T", "t", "i", "A", "p", "s", "q", "W", "n", "u", "r", "g", "S", "R", "m", "E", "c", "k", "w", "N", "Q", "B", "K", "Z", "e", "h", "ee", "U", "te", "le", "a", "d", "f", "o", "ae", "oe", "re", "M", "ne", "ie", "ue", "L", "x", "$", "z", "Ve", "$", "o", "T", "O", "t", "r", "k", "U", "N", "s", "e", "i", "n", "l", "a", "c", "u", "S", "v", "C", "K", "M", "a", "o", "e", "r", "i", "n", "t", "u", "f", "l", "g", "p", "a", "t", "r", "e", "o", "l", "u", "g", "o", "b", "r", "$", "A", "w", "n", "e", "f", "v", "s", "m", "p", "a", "u", "t", "i", "l", "O", "d", "y", "L", "M", "k", "c", "h", "I", "C", "Q", "E", "T", "Ee", "Ae", "N", "i", "o", "A", "O", "M", "I", "p", "e", "r", "f", "d", "g", "b", "t", "a", "n", "v", "s", "l", "c", "u", "h", "w", "N", "be", "C", "Se", "s", "U", "d", "P", "ue", "ge", "h", "u", "f", "t", "o", "y", "v", "m", "b", "i", "E", "L", "$", "x", "e", "r", "l", "g", "G", "C", "a", "S", "c", "I", "F", "w", "N", "p", "Ge", "n", "je", "Ae", "a", "d", "t", "n", "E", "i", "e", "o", "r", "l", "K", "p", "c", "f", "s", "u", "b", "g", "m", "le", "t", "m", "H", "N", "u", "S", "g", "O", "d", "i", "p", "l", "R", "E", "T", "f", "G", "e", "s", "a", "n", "o", "v", "r", "c", "b", "L", "ie", "K", "M", "ke", "Ee", "l", "c", "i", "r", "f", "E", "t", "p", "ue", "h", "n", "s", "e", "m", "o", "L", "R", "x", "d", "a", "K", "y", "b", "T", "B", "u", "ce", "d", "t", "n", "u", "o", "e", "a", "r", "te", "s", "le", "d", "U", "C", "a", "b", "me", "E", "i", "l", "r", "p", "R", "y", "t", "c", "n", "u", "o", "e", "I", "m", "h", "W", "O", "T", "x", "w", "pe", "Ie", "ye", "N", "l", "r", "e", "t", "m", "e", "t", "d", "g", "i", "F", "n", "a", "l", "s", "o", "u", "r", "c", "g", "e", "t", "R", "pe", "a", "me", "Te", "ge", "N", "L", "Q", "s", "n", "r", "S", "l", "f", "h", "W", "he", "i", "Se", "d", "y", "v", "A", "H", "I", "c", "b", "P", "o", "j", "M", "_", "Z", "ee", "te", "E", "p", "V", "ye", "be", "Ee", "Ve", "U", "ne", "re", "ce", "T"]}