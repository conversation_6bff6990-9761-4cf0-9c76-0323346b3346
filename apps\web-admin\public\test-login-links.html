<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面链接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 500;
            color: #374151;
        }
        .test-status {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-pass {
            background: #d1fae5;
            color: #065f46;
        }
        .status-fail {
            background: #fee2e2;
            color: #991b1b;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .log {
            background: #f3f4f6;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        .summary {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }
        .summary-item {
            flex: 1;
            text-align: center;
            padding: 16px;
            border-radius: 8px;
        }
        .summary-total {
            background: #e0e7ff;
            color: #3730a3;
        }
        .summary-pass {
            background: #d1fae5;
            color: #065f46;
        }
        .summary-fail {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <h1>🧪 登录页面链接测试</h1>
    
    <div class="summary">
        <div class="summary-item summary-total">
            <div style="font-size: 24px; font-weight: bold;" id="total-count">0</div>
            <div>总测试数</div>
        </div>
        <div class="summary-item summary-pass">
            <div style="font-size: 24px; font-weight: bold;" id="pass-count">0</div>
            <div>通过</div>
        </div>
        <div class="summary-item summary-fail">
            <div style="font-size: 24px; font-weight: bold;" id="fail-count">0</div>
            <div>失败</div>
        </div>
    </div>

    <div class="test-container">
        <h2>🔗 链接功能测试</h2>
        
        <div class="test-item">
            <span class="test-name">登录页面可访问性</span>
            <span class="test-status status-pending" id="status-1">待测试</span>
            <button class="test-button" onclick="testLoginPageAccess()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">忘记密码链接存在</span>
            <span class="test-status status-pending" id="status-2">待测试</span>
            <button class="test-button" onclick="testForgotPasswordLink()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">注册链接导航</span>
            <span class="test-status status-pending" id="status-3">待测试</span>
            <button class="test-button" onclick="testRegisterLink()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">服务条款链接</span>
            <span class="test-status status-pending" id="status-4">待测试</span>
            <button class="test-button" onclick="testTermsLink()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">隐私政策链接</span>
            <span class="test-status status-pending" id="status-5">待测试</span>
            <button class="test-button" onclick="testPrivacyLink()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">表单提交功能</span>
            <span class="test-status status-pending" id="status-6">待测试</span>
            <button class="test-button" onclick="testFormSubmission()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">密码可见性切换</span>
            <span class="test-status status-pending" id="status-7">待测试</span>
            <button class="test-button" onclick="testPasswordToggle()">测试</button>
        </div>
        
        <div class="test-item">
            <span class="test-name">响应式设计</span>
            <span class="test-status status-pending" id="status-8">待测试</span>
            <button class="test-button" onclick="testResponsiveDesign()">测试</button>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 测试结果</h2>
        <button class="test-button" onclick="runAllTests()" style="margin-bottom: 16px;">运行所有测试</button>
        <div class="log" id="test-log">点击"运行所有测试"开始测试...</div>
    </div>

    <script>
        let testResults = {};
        let logElement = document.getElementById('test-log');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(testId, status, message = '') {
            const statusElement = document.getElementById(`status-${testId}`);
            statusElement.className = `test-status status-${status}`;
            statusElement.textContent = status === 'pass' ? '✅ 通过' : status === 'fail' ? '❌ 失败' : '⏳ 测试中';
            
            testResults[testId] = status;
            updateSummary();
            
            if (message) {
                log(`测试 ${testId}: ${message}`);
            }
        }

        function updateSummary() {
            const total = Object.keys(testResults).length;
            const pass = Object.values(testResults).filter(r => r === 'pass').length;
            const fail = Object.values(testResults).filter(r => r === 'fail').length;
            
            document.getElementById('total-count').textContent = total;
            document.getElementById('pass-count').textContent = pass;
            document.getElementById('fail-count').textContent = fail;
        }

        async function testLoginPageAccess() {
            log('开始测试登录页面可访问性...');
            try {
                // 清除localStorage
                localStorage.clear();
                
                // 在新窗口中打开登录页面
                const loginWindow = window.open('/auth/login', '_blank');
                
                // 等待页面加载
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                if (loginWindow) {
                    updateStatus(1, 'pass', '登录页面可以正常访问');
                    loginWindow.close();
                } else {
                    updateStatus(1, 'fail', '无法打开登录页面');
                }
            } catch (error) {
                updateStatus(1, 'fail', `错误: ${error.message}`);
            }
        }

        async function testForgotPasswordLink() {
            log('开始测试忘记密码链接...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查忘记密码链接是否存在
                const hasLink = loginWindow.document.querySelector('a[href*="forgot"], a:contains("忘记密码")');
                
                if (hasLink || loginWindow.document.body.innerHTML.includes('忘记密码')) {
                    updateStatus(2, 'pass', '忘记密码链接存在');
                } else {
                    updateStatus(2, 'fail', '未找到忘记密码链接');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(2, 'fail', `错误: ${error.message}`);
            }
        }

        async function testRegisterLink() {
            log('开始测试注册链接导航...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查注册链接
                const hasRegisterLink = loginWindow.document.body.innerHTML.includes('立即注册') || 
                                       loginWindow.document.body.innerHTML.includes('注册');
                
                if (hasRegisterLink) {
                    updateStatus(3, 'pass', '注册链接存在');
                } else {
                    updateStatus(3, 'fail', '未找到注册链接');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(3, 'fail', `错误: ${error.message}`);
            }
        }

        async function testTermsLink() {
            log('开始测试服务条款链接...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const hasTermsLink = loginWindow.document.body.innerHTML.includes('服务条款');
                
                if (hasTermsLink) {
                    updateStatus(4, 'pass', '服务条款链接存在');
                } else {
                    updateStatus(4, 'fail', '未找到服务条款链接');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(4, 'fail', `错误: ${error.message}`);
            }
        }

        async function testPrivacyLink() {
            log('开始测试隐私政策链接...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const hasPrivacyLink = loginWindow.document.body.innerHTML.includes('隐私政策');
                
                if (hasPrivacyLink) {
                    updateStatus(5, 'pass', '隐私政策链接存在');
                } else {
                    updateStatus(5, 'fail', '未找到隐私政策链接');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(5, 'fail', `错误: ${error.message}`);
            }
        }

        async function testFormSubmission() {
            log('开始测试表单提交功能...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const hasForm = loginWindow.document.querySelector('form');
                const hasEmailInput = loginWindow.document.querySelector('input[type="email"]');
                const hasPasswordInput = loginWindow.document.querySelector('input[type="password"]');
                const hasSubmitButton = loginWindow.document.querySelector('button[type="submit"]') || 
                                       loginWindow.document.body.innerHTML.includes('登录账户');
                
                if (hasForm && hasEmailInput && hasPasswordInput && hasSubmitButton) {
                    updateStatus(6, 'pass', '表单元素完整');
                } else {
                    updateStatus(6, 'fail', '表单元素不完整');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(6, 'fail', `错误: ${error.message}`);
            }
        }

        async function testPasswordToggle() {
            log('开始测试密码可见性切换...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const hasPasswordInput = loginWindow.document.querySelector('input[type="password"]');
                const hasToggleButton = loginWindow.document.querySelector('button svg') || 
                                       loginWindow.document.body.innerHTML.includes('eye');
                
                if (hasPasswordInput && hasToggleButton) {
                    updateStatus(7, 'pass', '密码切换功能存在');
                } else {
                    updateStatus(7, 'fail', '密码切换功能不完整');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(7, 'fail', `错误: ${error.message}`);
            }
        }

        async function testResponsiveDesign() {
            log('开始测试响应式设计...');
            try {
                const loginWindow = window.open('/auth/login', '_blank');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 检查是否有响应式类名
                const hasResponsiveClasses = loginWindow.document.body.innerHTML.includes('lg:') || 
                                            loginWindow.document.body.innerHTML.includes('md:') ||
                                            loginWindow.document.body.innerHTML.includes('sm:');
                
                if (hasResponsiveClasses) {
                    updateStatus(8, 'pass', '包含响应式设计类名');
                } else {
                    updateStatus(8, 'fail', '未发现响应式设计');
                }
                
                loginWindow.close();
            } catch (error) {
                updateStatus(8, 'fail', `错误: ${error.message}`);
            }
        }

        async function runAllTests() {
            log('开始运行所有测试...');
            logElement.innerHTML = '';
            
            const tests = [
                testLoginPageAccess,
                testForgotPasswordLink,
                testRegisterLink,
                testTermsLink,
                testPrivacyLink,
                testFormSubmission,
                testPasswordToggle,
                testResponsiveDesign
            ];
            
            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            log('所有测试完成！');
        }

        // 初始化
        log('测试工具已准备就绪');
    </script>
</body>
</html>
