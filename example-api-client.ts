/**
 * Dify API 客户端示例
 * 保持与现有后端 API 的完全兼容性
 */

import ky, { type KyInstance } from 'ky'
import type {
  App,
  CreateAppData,
  UpdateAppData,
  Dataset,
  CreateDatasetData,
  Model,
  ModelConfig,
  Tool,
  User,
  ApiResponse,
  PaginatedResponse
} from '@dify/types'

// API 配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1'
const API_TIMEOUT = 30000

// 创建 HTTP 客户端
const createHttpClient = (): KyInstance => {
  return ky.create({
    prefixUrl: API_BASE_URL,
    timeout: API_TIMEOUT,
    retry: {
      limit: 2,
      methods: ['get'],
      statusCodes: [408, 413, 429, 500, 502, 503, 504]
    },
    hooks: {
      beforeRequest: [
        (request) => {
          // 添加认证头
          const token = getAuthToken()
          if (token) {
            request.headers.set('Authorization', `Bearer ${token}`)
          }

          // 添加内容类型
          if (request.body && !(request.body instanceof FormData)) {
            request.headers.set('Content-Type', 'application/json')
          }

          // 添加请求ID用于追踪
          request.headers.set('X-Request-ID', generateRequestId())
        }
      ],
      beforeRetry: [
        ({ request, options, error, retryCount }) => {
          console.warn(`Retrying request to ${request.url} (attempt ${retryCount + 1})`, error)
        }
      ],
      afterResponse: [
        async (request, options, response) => {
          // 处理认证失败
          if (response.status === 401) {
            handleAuthError()
            throw new Error('Authentication failed')
          }

          // 处理服务器错误
          if (response.status >= 500) {
            const errorData = await response.json().catch(() => ({}))
            throw new Error(errorData.message || 'Server error')
          }

          return response
        }
      ],
      beforeError: [
        (error) => {
          const { request, response } = error

          if (response) {
            // 增强错误信息
            error.name = 'HTTPError'
            error.message = `${response.status} ${response.statusText} for ${request.url}`
          }

          return error
        }
      ]
    }
  })
}

// HTTP 客户端实例
const http = createHttpClient()

// 工具函数
const getAuthToken = (): string | null => {
  return localStorage.getItem('dify_auth_token')
}

const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const handleAuthError = (): void => {
  localStorage.removeItem('dify_auth_token')
  window.location.href = '/login'
}

// 应用管理 API
export const appsApi = {
  // 获取应用列表
  async list(params?: {
    page?: number
    limit?: number
    category?: string
    status?: string
    search?: string
  }): Promise<PaginatedResponse<App>> {
    const searchParams = new URLSearchParams()

    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.category) searchParams.set('category', params.category)
    if (params?.status) searchParams.set('status', params.status)
    if (params?.search) searchParams.set('search', params.search)

    const response = await http.get('apps', { searchParams })
    return response.json()
  },

  // 获取单个应用
  async get(appId: string): Promise<App> {
    const response = await http.get(`apps/${appId}`)
    const data: ApiResponse<App> = await response.json()
    return data.data
  },

  // 创建应用
  async create(data: CreateAppData): Promise<App> {
    const response = await http.post('apps', { json: data })
    const result: ApiResponse<App> = await response.json()
    return result.data
  },

  // 更新应用
  async update(appId: string, data: UpdateAppData): Promise<App> {
    const response = await http.patch(`apps/${appId}`, { json: data })
    const result: ApiResponse<App> = await response.json()
    return result.data
  },

  // 删除应用
  async delete(appId: string): Promise<void> {
    await http.delete(`apps/${appId}`)
  },

  // 发布应用
  async publish(appId: string): Promise<App> {
    const response = await http.post(`apps/${appId}/publish`)
    const result: ApiResponse<App> = await response.json()
    return result.data
  },

  // 获取应用统计
  async getStats(appId: string): Promise<any> {
    const response = await http.get(`apps/${appId}/stats`)
    return response.json()
  }
}

// 数据集管理 API
export const datasetsApi = {
  // 获取数据集列表
  async list(): Promise<Dataset[]> {
    const response = await http.get('datasets')
    const data: ApiResponse<Dataset[]> = await response.json()
    return data.data
  },

  // 创建数据集
  async create(data: CreateDatasetData): Promise<Dataset> {
    const response = await http.post('datasets', { json: data })
    const result: ApiResponse<Dataset> = await response.json()
    return result.data
  },

  // 上传文档
  async uploadDocument(datasetId: string, file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await http.post(`datasets/${datasetId}/documents`, {
      body: formData
    })
    return response.json()
  },

  // 处理文档
  async processDocument(datasetId: string, documentId: string): Promise<any> {
    const response = await http.post(`datasets/${datasetId}/documents/${documentId}/process`)
    return response.json()
  }
}

// 模型管理 API
export const modelsApi = {
  // 获取模型列表
  async list(): Promise<Model[]> {
    const response = await http.get('models')
    const data: ApiResponse<Model[]> = await response.json()
    return data.data
  },

  // 配置模型
  async configure(config: ModelConfig): Promise<void> {
    await http.post('models/configure', { json: config })
  },

  // 测试模型连接
  async testConnection(modelId: string): Promise<boolean> {
    const response = await http.post(`models/${modelId}/test`)
    const data: ApiResponse<{ success: boolean }> = await response.json()
    return data.data.success
  }
}

// 工具管理 API
export const toolsApi = {
  // 获取工具列表
  async list(): Promise<Tool[]> {
    const response = await http.get('tools')
    const data: ApiResponse<Tool[]> = await response.json()
    return data.data
  },

  // 获取内置工具
  async getBuiltinTools(): Promise<Tool[]> {
    const response = await http.get('tools/builtin')
    const data: ApiResponse<Tool[]> = await response.json()
    return data.data
  }
}

// 用户管理 API
export const usersApi = {
  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await http.get('user/profile')
    const data: ApiResponse<User> = await response.json()
    return data.data
  },

  // 更新用户信息
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await http.patch('user/profile', { json: data })
    const result: ApiResponse<User> = await response.json()
    return result.data
  }
}

// 认证 API
export const authApi = {
  // 登录
  async login(email: string, password: string): Promise<{ token: string; user: User }> {
    const response = await http.post('auth/login', {
      json: { email, password }
    })
    const data: ApiResponse<{ token: string; user: User }> = await response.json()

    // 保存 token
    localStorage.setItem('dify_auth_token', data.data.token)

    return data.data
  },

  // 注册
  async register(userData: {
    email: string
    password: string
    name: string
  }): Promise<{ token: string; user: User }> {
    const response = await http.post('auth/register', { json: userData })
    const data: ApiResponse<{ token: string; user: User }> = await response.json()

    // 保存 token
    localStorage.setItem('dify_auth_token', data.data.token)

    return data.data
  },

  // 登出
  async logout(): Promise<void> {
    try {
      await http.post('auth/logout')
    } finally {
      localStorage.removeItem('dify_auth_token')
    }
  },

  // 刷新 token
  async refreshToken(): Promise<string> {
    const response = await http.post('auth/refresh')
    const data: ApiResponse<{ token: string }> = await response.json()

    localStorage.setItem('dify_auth_token', data.data.token)

    return data.data.token
  }
}

// 聊天 API
export const chatApi = {
  // 发送消息
  async sendMessage(appId: string, message: string, conversationId?: string): Promise<any> {
    const response = await http.post(`apps/${appId}/chat`, {
      json: {
        message,
        conversation_id: conversationId
      }
    })
    return response.json()
  },

  // 获取对话历史
  async getConversationHistory(conversationId: string): Promise<any> {
    const response = await http.get(`conversations/${conversationId}/messages`)
    return response.json()
  }
}

// 工作流 API
export const workflowApi = {
  // 获取工作流列表
  async list(): Promise<any[]> {
    const response = await http.get('workflows')
    const data: ApiResponse<any[]> = await response.json()
    return data.data
  },

  // 创建工作流
  async create(data: any): Promise<any> {
    const response = await http.post('workflows', { json: data })
    const result: ApiResponse<any> = await response.json()
    return result.data
  },

  // 运行工作流
  async run(workflowId: string, inputs: any): Promise<any> {
    const response = await http.post(`workflows/${workflowId}/run`, { json: inputs })
    return response.json()
  }
}

// 监控 API
export const monitoringApi = {
  // 获取应用统计
  async getAppStats(appId: string, timeRange: string): Promise<any> {
    const response = await http.get(`apps/${appId}/stats`, {
      searchParams: { time_range: timeRange }
    })
    return response.json()
  },

  // 获取对话日志
  async getConversationLogs(params: any): Promise<any> {
    const searchParams = new URLSearchParams(params)
    const response = await http.get('logs/conversations', { searchParams })
    return response.json()
  },

  // 获取成本分析
  async getCostAnalysis(timeRange: string): Promise<any> {
    const response = await http.get('analytics/costs', {
      searchParams: { time_range: timeRange }
    })
    return response.json()
  }
}

// 标注 API
export const annotationApi = {
  // 获取标注列表
  async list(appId: string): Promise<any[]> {
    const response = await http.get(`apps/${appId}/annotations`)
    const data: ApiResponse<any[]> = await response.json()
    return data.data
  },

  // 创建标注
  async create(appId: string, data: any): Promise<any> {
    const response = await http.post(`apps/${appId}/annotations`, { json: data })
    const result: ApiResponse<any> = await response.json()
    return result.data
  },

  // 导出标注数据
  async export(appId: string): Promise<Blob> {
    const response = await http.get(`apps/${appId}/annotations/export`)
    return response.blob()
  }
}

// 插件 API
export const pluginsApi = {
  // 获取插件列表
  async list(): Promise<any[]> {
    const response = await http.get('plugins')
    const data: ApiResponse<any[]> = await response.json()
    return data.data
  },

  // 安装插件
  async install(pluginId: string): Promise<any> {
    const response = await http.post(`plugins/${pluginId}/install`)
    return response.json()
  },

  // 配置插件
  async configure(pluginId: string, config: any): Promise<any> {
    const response = await http.patch(`plugins/${pluginId}/config`, { json: config })
    return response.json()
  }
}

// 设置 API
export const settingsApi = {
  // 获取系统设置
  async getSystemSettings(): Promise<any> {
    const response = await http.get('settings/system')
    const data: ApiResponse<any> = await response.json()
    return data.data
  },

  // 更新系统设置
  async updateSystemSettings(settings: any): Promise<any> {
    const response = await http.patch('settings/system', { json: settings })
    const result: ApiResponse<any> = await response.json()
    return result.data
  },

  // 获取安全设置
  async getSecuritySettings(): Promise<any> {
    const response = await http.get('settings/security')
    const data: ApiResponse<any> = await response.json()
    return data.data
  }
}

// 统一导出 API 客户端
export const api = {
  apps: appsApi,
  datasets: datasetsApi,
  models: modelsApi,
  tools: toolsApi,
  users: usersApi,
  auth: authApi,
  chat: chatApi,
  workflow: workflowApi,
  monitoring: monitoringApi,
  annotation: annotationApi,
  plugins: pluginsApi,
  settings: settingsApi
}

// 默认导出
export default api

// 类型导出
export type ApiClient = typeof api
