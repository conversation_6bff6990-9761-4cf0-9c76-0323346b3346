/**
 * Dify 应用管理状态示例
 * 展示如何将 Zustand 状态管理迁移到 Pinia
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import { api } from '@dify/api'
import type { App, CreateAppData, UpdateAppData, AppStatus } from '@dify/types'

// 应用状态管理
export const useAppsStore = defineStore('apps', () => {
  // ===== 状态定义 =====
  
  // 应用列表
  const apps = ref<App[]>([])
  
  // 当前选中的应用
  const currentApp = ref<App | null>(null)
  
  // 加载状态
  const isLoading = ref(false)
  const isCreating = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  
  // 错误状态
  const error = ref<string | null>(null)
  
  // 筛选和搜索状态
  const searchQuery = ref('')
  const selectedCategory = ref<string>('all')
  const selectedStatus = ref<AppStatus | 'all'>('all')
  
  // ===== 计算属性 =====
  
  // 过滤后的应用列表
  const filteredApps = computed(() => {
    let filtered = apps.value
    
    // 按分类筛选
    if (selectedCategory.value !== 'all') {
      filtered = filtered.filter(app => app.category === selectedCategory.value)
    }
    
    // 按状态筛选
    if (selectedStatus.value !== 'all') {
      filtered = filtered.filter(app => app.status === selectedStatus.value)
    }
    
    // 按搜索关键词筛选
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(app => 
        app.name.toLowerCase().includes(query) ||
        app.description?.toLowerCase().includes(query) ||
        app.tags?.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    return filtered
  })
  
  // 应用统计
  const appsStats = computed(() => {
    const total = apps.value.length
    const active = apps.value.filter(app => app.status === 'active').length
    const draft = apps.value.filter(app => app.status === 'draft').length
    const archived = apps.value.filter(app => app.status === 'archived').length
    
    return {
      total,
      active,
      draft,
      archived
    }
  })
  
  // 按分类分组的应用
  const appsByCategory = computed(() => {
    const grouped = apps.value.reduce((acc, app) => {
      const category = app.category || 'uncategorized'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(app)
      return acc
    }, {} as Record<string, App[]>)
    
    return grouped
  })
  
  // 最近创建的应用
  const recentApps = computed(() => {
    return apps.value
      .slice()
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5)
  })
  
  // ===== 操作方法 =====
  
  // 获取应用列表
  const fetchApps = async (force = false) => {
    if (isLoading.value && !force) return
    
    try {
      isLoading.value = true
      error.value = null
      
      const data = await api.apps.list()
      apps.value = data
      
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch apps'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // 获取单个应用详情
  const fetchApp = async (appId: string) => {
    try {
      const app = await api.apps.get(appId)
      
      // 更新列表中的应用数据
      const index = apps.value.findIndex(a => a.id === appId)
      if (index !== -1) {
        apps.value[index] = app
      }
      
      // 如果是当前应用，更新当前应用数据
      if (currentApp.value?.id === appId) {
        currentApp.value = app
      }
      
      return app
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch app'
      throw err
    }
  }
  
  // 创建应用
  const createApp = async (data: CreateAppData) => {
    try {
      isCreating.value = true
      error.value = null
      
      const newApp = await api.apps.create(data)
      
      // 添加到应用列表
      apps.value.unshift(newApp)
      
      return newApp
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create app'
      throw err
    } finally {
      isCreating.value = false
    }
  }
  
  // 更新应用
  const updateApp = async (appId: string, data: UpdateAppData) => {
    try {
      isUpdating.value = true
      error.value = null
      
      const updatedApp = await api.apps.update(appId, data)
      
      // 更新列表中的应用
      const index = apps.value.findIndex(app => app.id === appId)
      if (index !== -1) {
        apps.value[index] = updatedApp
      }
      
      // 如果是当前应用，更新当前应用数据
      if (currentApp.value?.id === appId) {
        currentApp.value = updatedApp
      }
      
      return updatedApp
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update app'
      throw err
    } finally {
      isUpdating.value = false
    }
  }
  
  // 删除应用
  const deleteApp = async (appId: string) => {
    try {
      isDeleting.value = true
      error.value = null
      
      await api.apps.delete(appId)
      
      // 从列表中移除
      apps.value = apps.value.filter(app => app.id !== appId)
      
      // 如果删除的是当前应用，清空当前应用
      if (currentApp.value?.id === appId) {
        currentApp.value = null
      }
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete app'
      throw err
    } finally {
      isDeleting.value = false
    }
  }
  
  // 复制应用
  const duplicateApp = async (appId: string) => {
    try {
      const originalApp = apps.value.find(app => app.id === appId)
      if (!originalApp) {
        throw new Error('App not found')
      }
      
      const duplicateData: CreateAppData = {
        name: `${originalApp.name} (Copy)`,
        description: originalApp.description,
        category: originalApp.category,
        config: originalApp.config,
        tags: originalApp.tags
      }
      
      return await createApp(duplicateData)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to duplicate app'
      throw err
    }
  }
  
  // 设置当前应用
  const setCurrentApp = (app: App | null) => {
    currentApp.value = app
  }
  
  // 更新搜索查询
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }
  
  // 设置分类筛选
  const setSelectedCategory = (category: string) => {
    selectedCategory.value = category
  }
  
  // 设置状态筛选
  const setSelectedStatus = (status: AppStatus | 'all') => {
    selectedStatus.value = status
  }
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  // 重置筛选条件
  const resetFilters = () => {
    searchQuery.value = ''
    selectedCategory.value = 'all'
    selectedStatus.value = 'all'
  }
  
  // ===== 返回状态和方法 =====
  return {
    // 只读状态
    apps: readonly(apps),
    currentApp: readonly(currentApp),
    isLoading: readonly(isLoading),
    isCreating: readonly(isCreating),
    isUpdating: readonly(isUpdating),
    isDeleting: readonly(isDeleting),
    error: readonly(error),
    
    // 筛选状态（可写）
    searchQuery,
    selectedCategory,
    selectedStatus,
    
    // 计算属性
    filteredApps,
    appsStats,
    appsByCategory,
    recentApps,
    
    // 操作方法
    fetchApps,
    fetchApp,
    createApp,
    updateApp,
    deleteApp,
    duplicateApp,
    setCurrentApp,
    setSearchQuery,
    setSelectedCategory,
    setSelectedStatus,
    clearError,
    resetFilters
  }
})

// 应用工作流状态管理
export const useAppWorkflowStore = defineStore('appWorkflow', () => {
  const nodes = ref([])
  const edges = ref([])
  const selectedNode = ref(null)
  const isExecuting = ref(false)
  
  // 工作流相关方法...
  
  return {
    nodes: readonly(nodes),
    edges: readonly(edges),
    selectedNode: readonly(selectedNode),
    isExecuting: readonly(isExecuting),
    // 方法...
  }
})

// 类型定义
export type AppsStore = ReturnType<typeof useAppsStore>
export type AppWorkflowStore = ReturnType<typeof useAppWorkflowStore>
