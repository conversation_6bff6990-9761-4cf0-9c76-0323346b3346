<template>
  <div class="min-h-screen flex">
    <!-- 左侧品牌区域 -->
    <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-600 to-primary-800 relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>

      <!-- 内容 -->
      <div class="relative z-10 flex flex-col justify-center px-12 text-white">
        <div class="mb-8">
          <div class="h-12 flex items-center">
            <div class="text-3xl font-bold text-white">Dify</div>
          </div>
        </div>

        <h1 class="text-4xl font-bold mb-6">
          Build AI Applications with Ease
        </h1>

        <p class="text-xl text-primary-100 mb-8">
          Create, deploy, and manage AI-powered applications using our intuitive platform.
        </p>

        <div class="space-y-4">
          <div class="flex items-center">
            <CheckIcon class="h-5 w-5 mr-3 text-green-300" />
            <span>Visual workflow builder</span>
          </div>
          <div class="flex items-center">
            <CheckIcon class="h-5 w-5 mr-3 text-green-300" />
            <span>Multiple AI model support</span>
          </div>
          <div class="flex items-center">
            <CheckIcon class="h-5 w-5 mr-3 text-green-300" />
            <span>Knowledge base integration</span>
          </div>
          <div class="flex items-center">
            <CheckIcon class="h-5 w-5 mr-3 text-green-300" />
            <span>Real-time monitoring</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧表单区域 -->
    <div class="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8">
      <div class="mx-auto w-full max-w-md">
        <!-- 移动端Logo -->
        <div class="lg:hidden text-center mb-8">
          <div class="h-12 flex items-center justify-center">
            <div class="text-3xl font-bold text-primary-600">Dify</div>
          </div>
        </div>

        <!-- 表单内容 -->
        <RouterView />

        <!-- 底部链接 -->
        <div class="mt-8 text-center text-sm text-gray-600">
          <p>
            By continuing, you agree to our
            <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a>
            and
            <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon } from '@heroicons/vue/24/outline'
</script>
