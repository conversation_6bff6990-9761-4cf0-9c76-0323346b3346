<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- 主容器 -->
    <div class="w-full max-w-6xl mx-auto">
      <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
        <div class="flex min-h-[600px]">
          <!-- 左侧品牌区域 -->
          <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 relative overflow-hidden">
            <!-- 背景装饰元素 -->
            <div class="absolute inset-0">
              <!-- 动态背景圆圈 -->
              <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
              <div class="absolute top-40 right-16 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
              <div class="absolute bottom-32 left-16 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
              <div class="absolute bottom-20 right-20 w-28 h-28 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>

              <!-- 网格背景 -->
              <div class="absolute inset-0 opacity-20">
                <div class="w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot;%3E%3Cpath d=&quot;M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"></div>
              </div>
            </div>

            <!-- 内容 -->
            <div class="relative z-10 flex flex-col justify-center px-12 text-white">
              <!-- Logo区域 -->
              <div class="mb-12">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
                    <div class="w-8 h-8 bg-gradient-to-br from-white to-white/80 rounded-lg flex items-center justify-center">
                      <span class="text-indigo-600 font-bold text-lg">D</span>
                    </div>
                  </div>
                  <div class="text-3xl font-bold">Dify</div>
                </div>
                <div class="w-16 h-1 bg-gradient-to-r from-white to-white/50 rounded-full"></div>
              </div>

              <!-- 主标题 -->
              <h1 class="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                Build AI Applications
                <span class="block text-white/90">with Ease</span>
              </h1>

              <!-- 描述 -->
              <p class="text-xl text-white/80 mb-12 leading-relaxed">
                Create, deploy, and manage AI-powered applications using our intuitive platform with advanced workflow capabilities.
              </p>

              <!-- 特性列表 -->
              <div class="space-y-6">
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">Visual workflow builder</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">Multiple AI model support</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">Knowledge base integration</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">Real-time monitoring</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧表单区域 -->
          <div class="flex-1 flex flex-col justify-center px-8 sm:px-12 lg:px-16 py-12">
            <!-- 移动端Logo -->
            <div class="lg:hidden text-center mb-8">
              <div class="flex items-center justify-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-lg">D</span>
                </div>
                <div class="text-3xl font-bold text-gray-900">Dify</div>
              </div>
            </div>

            <!-- 表单内容 -->
            <div class="w-full max-w-sm mx-auto">
              <RouterView />
            </div>

            <!-- 底部链接 -->
            <div class="mt-8 text-center text-sm text-gray-500 max-w-sm mx-auto">
              <p>
                By continuing, you agree to our
                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">Terms of Service</a>
                and
                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">Privacy Policy</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon } from '@heroicons/vue/24/outline'
</script>
