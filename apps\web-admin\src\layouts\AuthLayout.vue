<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <!-- 测试标记 -->
    <div class="fixed top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50">
      🎨 新AuthLayout已加载
    </div>

    <!-- 主容器 -->
    <div class="w-full max-w-6xl mx-auto">
      <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
        <div class="flex min-h-[600px]">
          <!-- 左侧品牌区域 -->
          <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 relative overflow-hidden">
            <!-- 背景装饰元素 -->
            <div class="absolute inset-0">
              <!-- 动态背景圆圈 -->
              <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
              <div class="absolute top-40 right-16 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
              <div class="absolute bottom-32 left-16 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
              <div class="absolute bottom-20 right-20 w-28 h-28 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>

              <!-- 简化的装饰背景 -->
              <div class="absolute inset-0 opacity-10">
                <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/5 to-transparent"></div>
                <div class="absolute bottom-0 right-0 w-2/3 h-2/3 bg-gradient-to-tl from-white/5 to-transparent rounded-full"></div>
              </div>
            </div>

            <!-- 内容 -->
            <div class="relative z-10 flex flex-col justify-center px-12 text-white">
              <!-- Logo区域 -->
              <div class="mb-12">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
                    <div class="w-8 h-8 bg-gradient-to-br from-white to-white/80 rounded-lg flex items-center justify-center">
                      <span class="text-indigo-600 font-bold text-lg">D</span>
                    </div>
                  </div>
                  <div class="text-3xl font-bold">Dify</div>
                </div>
                <div class="w-16 h-1 bg-gradient-to-r from-white to-white/50 rounded-full"></div>
              </div>

              <!-- 主标题 -->
              <h1 class="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                轻松构建
                <span class="block text-white/90">AI 应用</span>
              </h1>

              <!-- 描述 -->
              <p class="text-xl text-white/80 mb-12 leading-relaxed">
                使用我们直观的平台创建、部署和管理 AI 驱动的应用程序，具备先进的工作流功能。
              </p>

              <!-- 特性列表 -->
              <div class="space-y-6">
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">可视化工作流构建器</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">多种 AI 模型支持</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">知识库集成</span>
                </div>
                <div class="flex items-center group">
                  <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                    <CheckIcon class="h-5 w-5 text-white" />
                  </div>
                  <span class="text-lg">实时监控</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧表单区域 -->
          <div class="flex-1 flex flex-col justify-center px-8 sm:px-12 lg:px-16 py-12">
            <!-- 移动端Logo -->
            <div class="lg:hidden text-center mb-8">
              <div class="flex items-center justify-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-lg">D</span>
                </div>
                <div class="text-3xl font-bold text-gray-900">Dify</div>
              </div>
            </div>

            <!-- 表单内容 -->
            <div class="w-full max-w-sm mx-auto">
              <RouterView />
            </div>

            <!-- 底部链接 -->
            <div class="mt-8 text-center text-sm text-gray-500 max-w-sm mx-auto">
              <p>
                继续使用即表示您同意我们的
                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">服务条款</a>
                和
                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">隐私政策</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon } from '@heroicons/vue/24/outline'
</script>
