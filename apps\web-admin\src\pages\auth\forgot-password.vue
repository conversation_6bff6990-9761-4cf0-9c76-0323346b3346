<template>
  <div class="w-full max-w-sm mx-auto">
    <!-- 标题 -->
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-2">重置密码</h2>
      <p class="text-gray-600">
        输入您的邮箱地址，我们将发送重置密码的链接
      </p>
    </div>

    <!-- 重置密码表单 -->
    <form @submit.prevent="handleForgotPassword" class="space-y-6">
      <!-- 邮箱输入 -->
      <div class="space-y-2">
        <label for="email" class="block text-sm font-semibold text-gray-700">
          邮箱地址
        </label>
        <input
          id="email"
          v-model="form.email"
          name="email"
          type="email"
          autocomplete="email"
          required
          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors placeholder-gray-400 text-gray-900"
          placeholder="请输入您的邮箱地址"
        />
      </div>

      <!-- 提交按钮 -->
      <div>
        <button
          type="submit"
          :disabled="isLoading"
          class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <span v-if="isLoading" class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            发送中...
          </span>
          <span v-else>发送重置链接</span>
        </button>
      </div>
    </form>

    <!-- 成功消息 -->
    <div v-if="emailSent" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-xl">
      <div class="flex">
        <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <div class="ml-3">
          <p class="text-sm text-green-800">
            重置密码的链接已发送到您的邮箱，请查收邮件并按照说明操作。
          </p>
        </div>
      </div>
    </div>

    <!-- 返回登录链接 -->
    <div class="mt-8 text-center">
      <p class="text-sm text-gray-600">
        想起密码了？
        <RouterLink to="/auth/login" class="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors">
          返回登录
        </RouterLink>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const form = ref({
  email: ''
})

const isLoading = ref(false)
const emailSent = ref(false)

const handleForgotPassword = async () => {
  isLoading.value = true
  
  try {
    // 模拟发送重置邮件
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emailSent.value = true
    console.log('Password reset email sent to:', form.value.email)
  } catch (error) {
    console.error('Failed to send reset email:', error)
  } finally {
    isLoading.value = false
  }
}
</script>
