#!/bin/bash

# Dify Vue 项目初始化脚本
# 基于 vue-vben-admin v5.5.7 架构

set -e

echo "🚀 开始初始化 Dify Vue 项目..."

# 检查必要工具
check_requirements() {
    echo "📋 检查环境要求..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请安装 Node.js 20+"
        exit 1
    fi
    
    if ! command -v pnpm &> /dev/null; then
        echo "❌ pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    echo "✅ 环境检查完成"
}

# 创建项目结构
create_project_structure() {
    echo "📁 创建项目结构..."
    
    # 根目录文件
    mkdir -p {apps,packages,internal,scripts,docs}
    
    # 应用目录
    mkdir -p apps/{web-admin,web-chat}/src/{api,components,layouts,pages,stores,utils,types,assets}
    mkdir -p apps/{web-admin,web-chat}/public
    
    # 共享包目录
    mkdir -p packages/{components,utils,types,api,constants}/src
    
    # 内部工具目录
    mkdir -p internal/{eslint-config,tailwind-config,vite-config,tsconfig}/src
    
    # 脚本目录
    mkdir -p scripts/{build,deploy,dev}
    
    echo "✅ 项目结构创建完成"
}

# 创建根配置文件
create_root_config() {
    echo "⚙️ 创建根配置文件..."
    
    # package.json
    cat > package.json << 'EOF'
{
  "name": "dify-vue-monorepo",
  "version": "1.5.1",
  "private": true,
  "type": "module",
  "engines": {
    "node": ">=20.10.0",
    "pnpm": ">=9.12.0"
  },
  "packageManager": "pnpm@10.12.4",
  "scripts": {
    "dev": "turbo dev",
    "build": "turbo build",
    "lint": "turbo lint",
    "test": "turbo test",
    "clean": "turbo clean",
    "dev:admin": "pnpm -F @dify/web-admin dev",
    "dev:chat": "pnpm -F @dify/web-chat dev",
    "build:admin": "pnpm -F @dify/web-admin build",
    "build:chat": "pnpm -F @dify/web-chat build"
  },
  "devDependencies": {
    "@changesets/cli": "^2.27.0",
    "@types/node": "^20.10.0",
    "cross-env": "^7.0.3",
    "eslint": "^9.0.0",
    "prettier": "^3.0.0",
    "turbo": "^2.0.0",
    "typescript": "^5.0.0",
    "vitest": "^2.0.0"
  },
  "pnpm": {
    "overrides": {
      "vue": "^3.5.0"
    }
  }
}
EOF

    # pnpm-workspace.yaml
    cat > pnpm-workspace.yaml << 'EOF'
packages:
  - 'apps/*'
  - 'packages/*'
  - 'internal/*'
EOF

    # turbo.json
    cat > turbo.json << 'EOF'
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**", "!.next/cache/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "outputs": []
    },
    "test": {
      "outputs": []
    },
    "clean": {
      "cache": false
    }
  }
}
EOF

    # .gitignore
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
.next/
.nuxt/
.output/
.vite/

# Environment files
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache
.cache/
.turbo/
.eslintcache

# Testing
coverage/
.nyc_output/

# Temporary
tmp/
temp/
EOF

    echo "✅ 根配置文件创建完成"
}

# 创建管理后台应用
create_admin_app() {
    echo "🎨 创建管理后台应用..."
    
    cd apps/web-admin
    
    # package.json
    cat > package.json << 'EOF'
{
  "name": "@dify/web-admin",
  "version": "1.5.1",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "test": "vitest"
  },
  "dependencies": {
    "vue": "^3.5.0",
    "vue-router": "^4.4.0",
    "pinia": "^2.2.0",
    "@tanstack/vue-query": "^5.0.0",
    "@vueuse/core": "^11.0.0",
    "@headlessui/vue": "^1.7.0",
    "@heroicons/vue": "^2.0.0",
    "tailwindcss": "^3.4.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "dayjs": "^1.11.0",
    "ky": "^1.7.0",
    "@vue-flow/core": "^1.0.0",
    "@vue-flow/controls": "^1.0.0",
    "@vue-flow/minimap": "^1.0.0",
    "@vue-flow/background": "^1.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.0",
    "@vitejs/plugin-vue-jsx": "^4.0.0",
    "vite": "^5.0.0",
    "vue-tsc": "^2.0.0",
    "typescript": "^5.0.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  }
}
EOF

    # vite.config.ts
    cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue(), vueJsx()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@dify/components': resolve(__dirname, '../../packages/components/src'),
      '@dify/utils': resolve(__dirname, '../../packages/utils/src'),
      '@dify/types': resolve(__dirname, '../../packages/types/src'),
      '@dify/api': resolve(__dirname, '../../packages/api/src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'ui-vendor': ['@headlessui/vue', '@heroicons/vue'],
          'flow-vendor': ['@vue-flow/core', '@vue-flow/controls']
        }
      }
    }
  }
})
EOF

    # tailwind.config.js
    cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "../../packages/components/src/**/*.{vue,js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8'
        }
      }
    }
  },
  plugins: []
}
EOF

    # index.html
    cat > index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dify Admin</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
EOF

    # src/main.ts
    cat > src/main.ts << 'EOF'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { VueQueryPlugin } from '@tanstack/vue-query'
import App from './App.vue'
import router from './router'
import './style.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(VueQueryPlugin)

app.mount('#app')
EOF

    # src/App.vue
    cat > src/App.vue << 'EOF'
<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>
EOF

    # src/style.css
    cat > src/style.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}
EOF

    cd ../..
    echo "✅ 管理后台应用创建完成"
}

# 创建路由配置
create_router() {
    echo "🛣️ 创建路由配置..."
    
    cat > apps/web-admin/src/router/index.ts << 'EOF'
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/apps'
  },
  {
    path: '/apps',
    name: 'Apps',
    component: () => import('../pages/apps/index.vue')
  },
  {
    path: '/datasets',
    name: 'Datasets',
    component: () => import('../pages/datasets/index.vue')
  },
  {
    path: '/models',
    name: 'Models',
    component: () => import('../pages/models/index.vue')
  },
  {
    path: '/tools',
    name: 'Tools',
    component: () => import('../pages/tools/index.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
EOF

    echo "✅ 路由配置创建完成"
}

# 主函数
main() {
    check_requirements
    create_project_structure
    create_root_config
    create_admin_app
    create_router
    
    echo "🎉 Dify Vue 项目初始化完成！"
    echo ""
    echo "📝 下一步操作："
    echo "1. cd 到项目目录"
    echo "2. 运行 'pnpm install' 安装依赖"
    echo "3. 运行 'pnpm dev:admin' 启动开发服务器"
    echo ""
    echo "🔗 相关链接："
    echo "- 管理后台: http://localhost:3000"
    echo "- 技术方案: ./DIFY_VUE_MIGRATION_PLAN.md"
}

# 执行主函数
main "$@"
