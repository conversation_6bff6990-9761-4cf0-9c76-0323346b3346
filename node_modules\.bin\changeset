#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules/@changesets/cli/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules/@changesets/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules/@changesets/cli/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules/@changesets/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/@changesets+cli@2.29.5/node_modules:/mnt/f/AI/dify-vue/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@changesets/cli/bin.js" "$@"
else
  exec node  "$basedir/../@changesets/cli/bin.js" "$@"
fi
