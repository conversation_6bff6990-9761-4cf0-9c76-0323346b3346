{"lastValidatedTimestamp": 1751528541991, "projects": {"F:\\AI\\dify-vue": {"name": "dify-vue-monorepo", "version": "1.5.1"}, "F:\\AI\\dify-vue\\apps\\web-admin": {"name": "@dify/web-admin", "version": "1.5.1"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*", "internal/*"]}, "filteredInstall": false}