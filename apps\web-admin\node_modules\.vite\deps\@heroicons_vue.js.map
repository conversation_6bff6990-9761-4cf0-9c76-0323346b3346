{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@heroicons+vue@2.2.0_vue@3.5.17_typescript@5.8.3_/node_modules/@heroicons/vue/index.esm.js"], "sourcesContent": ["// The only reason this file exists is to appease V<PERSON>'s optimizeDeps feature which requires a root-level import.\n\nexport default new Proxy(\n  {},\n  {\n    get: (_, property) => {\n      if (property === '__esModule') {\n        return {}\n      }\n\n      throw new Error(\n        `Importing from \\`@heroicons/vue\\` directly is not supported. Please import from either \\`@heroicons/vue/16/solid\\`, \\`@heroicons/vue/20/solid\\`, \\`@heroicons/vue/24/solid\\`, or \\`@heroicons/vue/24/outline\\` instead.`\n      )\n    },\n  }\n)\n"], "mappings": ";;;AAEA,IAAO,oBAAQ,IAAI;AAAA,EACjB,CAAC;AAAA,EACD;AAAA,IACE,KAAK,CAAC,GAAG,aAAa;AACpB,UAAI,aAAa,cAAc;AAC7B,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}