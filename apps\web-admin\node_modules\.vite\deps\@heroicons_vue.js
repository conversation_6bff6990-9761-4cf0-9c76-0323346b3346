import "./chunk-X34TS6PR.js";

// ../../node_modules/.pnpm/@heroicons+vue@2.2.0_vue@3.5.17_typescript@5.8.3_/node_modules/@heroicons/vue/index.esm.js
var index_esm_default = new Proxy(
  {},
  {
    get: (_, property) => {
      if (property === "__esModule") {
        return {};
      }
      throw new Error(
        `Importing from \`@heroicons/vue\` directly is not supported. Please import from either \`@heroicons/vue/16/solid\`, \`@heroicons/vue/20/solid\`, \`@heroicons/vue/24/solid\`, or \`@heroicons/vue/24/outline\` instead.`
      );
    }
  }
);
export {
  index_esm_default as default
};
//# sourceMappingURL=@heroicons_vue.js.map
