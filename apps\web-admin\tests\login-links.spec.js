const { test, expect } = require('@playwright/test');

test.describe('登录页面超链接测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清除localStorage确保能访问登录页面
    await page.goto('http://localhost:3000/clear-storage.html');
    await page.waitForTimeout(1000);
  });

  test('应该能访问登录页面', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 验证页面标题
    await expect(page.locator('h2')).toContainText('欢迎回来');
    
    // 验证页面描述
    await expect(page.locator('p')).toContainText('登录您的账户以继续使用');
  });

  test('忘记密码链接应该存在且可点击', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 查找忘记密码链接
    const forgotPasswordLink = page.locator('a:has-text("忘记密码？")');
    
    // 验证链接存在
    await expect(forgotPasswordLink).toBeVisible();
    
    // 验证链接样式
    await expect(forgotPasswordLink).toHaveClass(/text-indigo-600/);
    
    // 点击链接（注意：这里只是测试点击，实际可能没有实现页面）
    await forgotPasswordLink.click();
  });

  test('注册链接应该导航到注册页面', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 查找注册链接
    const registerLink = page.locator('a:has-text("立即注册")');
    
    // 验证链接存在
    await expect(registerLink).toBeVisible();
    
    // 验证链接样式
    await expect(registerLink).toHaveClass(/text-indigo-600/);
    
    // 点击注册链接
    await registerLink.click();
    
    // 验证导航到注册页面
    await expect(page).toHaveURL(/\/auth\/register/);
    
    // 验证注册页面内容
    await expect(page.locator('h2')).toContainText('创建账户');
  });

  test('服务条款链接应该存在且可点击', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 查找服务条款链接
    const termsLink = page.locator('a:has-text("服务条款")');
    
    // 验证链接存在
    await expect(termsLink).toBeVisible();
    
    // 验证链接样式
    await expect(termsLink).toHaveClass(/text-indigo-600/);
    
    // 点击链接
    await termsLink.click();
  });

  test('隐私政策链接应该存在且可点击', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 查找隐私政策链接
    const privacyLink = page.locator('a:has-text("隐私政策")');
    
    // 验证链接存在
    await expect(privacyLink).toBeVisible();
    
    // 验证链接样式
    await expect(privacyLink).toHaveClass(/text-indigo-600/);
    
    // 点击链接
    await privacyLink.click();
  });

  test('登录表单应该正常工作', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 填写表单
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // 勾选记住我
    await page.check('input[type="checkbox"]');
    
    // 点击登录按钮
    const loginButton = page.locator('button:has-text("登录账户")');
    await expect(loginButton).toBeVisible();
    await loginButton.click();
    
    // 验证按钮状态变化（加载状态）
    await expect(page.locator('button:has-text("登录中...")')).toBeVisible();
  });

  test('密码可见性切换应该正常工作', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 找到密码输入框
    const passwordInput = page.locator('input[type="password"]');
    await expect(passwordInput).toBeVisible();
    
    // 找到密码可见性切换按钮
    const toggleButton = page.locator('button').filter({ hasText: /^$/ }).nth(0);
    
    // 点击切换按钮
    await toggleButton.click();
    
    // 验证密码输入框类型变为text
    await expect(page.locator('input[type="text"]')).toBeVisible();
    
    // 再次点击切换回密码类型
    await toggleButton.click();
    await expect(passwordInput).toBeVisible();
  });

  test('页面响应式设计应该正常', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:3000/auth/login');
    
    // 验证左侧品牌区域在桌面端可见
    const brandSection = page.locator('.lg\\:flex').first();
    await expect(brandSection).toBeVisible();
    
    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 验证移动端Logo可见
    const mobileLogo = page.locator('.lg\\:hidden');
    await expect(mobileLogo).toBeVisible();
  });

  test('表单验证应该正常工作', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 尝试提交空表单
    const loginButton = page.locator('button:has-text("登录账户")');
    await loginButton.click();
    
    // 验证HTML5验证消息（邮箱必填）
    const emailInput = page.locator('input[type="email"]');
    const validationMessage = await emailInput.evaluate(el => el.validationMessage);
    expect(validationMessage).toBeTruthy();
  });

  test('所有交互元素应该有正确的样式状态', async ({ page }) => {
    await page.goto('http://localhost:3000/auth/login');
    
    // 测试输入框聚焦状态
    const emailInput = page.locator('input[type="email"]');
    await emailInput.focus();
    await expect(emailInput).toHaveClass(/focus:ring-2/);
    
    // 测试按钮悬停状态
    const loginButton = page.locator('button:has-text("登录账户")');
    await loginButton.hover();
    await expect(loginButton).toHaveClass(/hover:from-indigo-700/);
    
    // 测试链接悬停状态
    const registerLink = page.locator('a:has-text("立即注册")');
    await registerLink.hover();
    await expect(registerLink).toHaveClass(/hover:text-indigo-500/);
  });
});

test.describe('注册页面测试', () => {
  test('从登录页面导航到注册页面应该正常', async ({ page }) => {
    // 清除localStorage
    await page.goto('http://localhost:3000/clear-storage.html');
    await page.waitForTimeout(1000);
    
    // 访问登录页面
    await page.goto('http://localhost:3000/auth/login');
    
    // 点击注册链接
    await page.click('a:has-text("立即注册")');
    
    // 验证导航到注册页面
    await expect(page).toHaveURL(/\/auth\/register/);
    
    // 验证注册页面内容
    await expect(page.locator('h2')).toContainText('创建账户');
    
    // 验证返回登录链接存在
    const loginLink = page.locator('a:has-text("登录")');
    await expect(loginLink).toBeVisible();
    
    // 点击返回登录
    await loginLink.click();
    
    // 验证返回登录页面
    await expect(page).toHaveURL(/\/auth\/login/);
    await expect(page.locator('h2')).toContainText('欢迎回来');
  });
});
