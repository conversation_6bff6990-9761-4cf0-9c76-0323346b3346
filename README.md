# Dify Vue 迁移项目

将 Dify v1.5.1 前端从 React/Next.js 迁移到 Vue 3 技术栈的完整解决方案。

## 📋 项目概述

本项目旨在将 Dify AI 平台的前端技术栈从 React 迁移到 Vue 3，同时保持所有现有功能和后端 API 兼容性。迁移基于 vue-vben-admin v5.5.7 的成熟架构设计。

## 🎯 迁移目标

- ✅ 保持所有现有功能完整性
- ✅ 保持后端 API 接口不变
- ✅ 提升前端开发体验
- ✅ 采用现代化 Vue 3 生态
- ✅ 优化性能和用户体验

## 🛠 技术栈对比

| 功能 | 当前 (React) | 目标 (Vue) |
|------|-------------|-----------|
| 框架 | Next.js 15.2.4 + React 19 | Vue 3.5 + Vite |
| 语言 | TypeScript 4.9.5 | TypeScript 5.0+ |
| 状态管理 | Zustand + SWR | Pinia + TanStack Query |
| UI 组件 | Headless UI + Tailwind | Headless UI Vue + Tailwind |
| 构建工具 | Next.js | Vite + Turbo |
| 包管理 | pnpm | pnpm |

## 📁 项目结构

```
dify-vue/
├── apps/                          # 应用目录
│   ├── web-admin/                 # 管理后台
│   └── web-chat/                  # 聊天应用
├── packages/                      # 共享包
│   ├── components/               # 通用组件库
│   ├── utils/                    # 工具函数库
│   ├── types/                    # 类型定义库
│   ├── api/                      # API 客户端库
│   └── constants/                # 常量定义
├── internal/                     # 内部工具
│   ├── eslint-config/           # ESLint 配置
│   ├── tailwind-config/         # Tailwind 配置
│   └── vite-config/             # Vite 配置
└── docs/                        # 文档
```

## 🚀 快速开始

### 1. 环境要求

- Node.js >= 20.10.0
- pnpm >= 9.12.0

### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd dify-vue

# 运行初始化脚本
chmod +x setup-project.sh
./setup-project.sh

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev:admin
```

### 3. 访问应用

- 管理后台: http://localhost:3000
- 聊天应用: http://localhost:3001

## 📚 核心文件说明

### 技术方案文档
- `DIFY_VUE_MIGRATION_PLAN.md` - 完整的迁移技术方案
- `DIFY_COMPLETE_FEATURES_CHECKLIST.md` - 功能完整性检查清单

### 示例代码
- `example-apps-page.vue` - 应用管理页面示例
- `example-apps-store.ts` - Pinia 状态管理示例
- `example-api-client.ts` - API 客户端示例 (包含所有模块)

### 配置文件
- `setup-project.sh` - 项目初始化脚本

## 🔄 迁移进度

### 阶段一：基础架构搭建 ✅
- [x] 项目骨架创建
- [x] Monorepo 配置
- [x] 开发环境配置
- [x] 基础依赖安装

### 阶段二：核心组件迁移 🚧
- [ ] 基础组件库
- [ ] 业务组件
- [ ] 布局组件

### 阶段三：页面功能迁移 ⏳
- [ ] 应用管理页面 (4种应用类型)
- [ ] 工作流设计器页面 (8种节点类型)
- [ ] 数据集管理页面 (知识库功能)
- [ ] 模型管理页面 (多模型支持)
- [ ] 工具管理页面 (40+工具)
- [ ] 监控分析页面
- [ ] 标注管理页面
- [ ] 用户管理页面
- [ ] 系统设置页面

### 阶段四：状态管理与API集成 ⏳
- [ ] Pinia 状态管理
- [ ] API 客户端集成
- [ ] TanStack Query 集成

### 阶段五：特殊功能迁移 ⏳
- [ ] 工作流设计器 (可视化编排)
- [ ] 聊天功能 (多模态支持)
- [ ] 代码编辑器 (Monaco Editor)
- [ ] 富文本编辑器 (Prompt编辑)
- [ ] 文件处理功能
- [ ] 国际化 (多语言支持)

### 阶段六：测试与优化 ⏳
- [ ] 单元测试
- [ ] 性能优化

## 🎨 组件迁移示例

### React 组件
```jsx
// React 版本
const AppsPage = () => {
  const [apps, setApps] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchApps()
  }, [])

  return (
    <div>
      {loading ? <Spinner /> : <AppList apps={apps} />}
    </div>
  )
}
```

### Vue 组件
```vue
<!-- Vue 版本 -->
<template>
  <div>
    <Spinner v-if="isLoading" />
    <AppList v-else :apps="apps" />
  </div>
</template>

<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { api } from '@dify/api'

const { data: apps, isLoading } = useQuery({
  queryKey: ['apps'],
  queryFn: () => api.apps.list()
})
</script>
```

## 🔧 开发指南

### 组件开发规范

1. 使用 Vue 3 Composition API
2. TypeScript 严格模式
3. 遵循 ESLint 规则
4. 使用 Tailwind CSS 样式

### 状态管理规范

1. 使用 Pinia 进行状态管理
2. 使用 TanStack Query 处理服务端状态
3. 保持状态结构清晰

### API 集成规范

1. 保持与现有后端 API 完全兼容
2. 使用 ky 作为 HTTP 客户端
3. 统一错误处理机制

## 🧪 测试

```bash
# 运行单元测试
pnpm test

# 运行 E2E 测试
pnpm test:e2e

# 代码覆盖率
pnpm test:coverage
```

## 📦 构建部署

```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview

# Docker 构建
pnpm build:docker
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙋‍♂️ 支持

如有问题或建议，请：

1. 查看 [技术方案文档](DIFY_VUE_MIGRATION_PLAN.md)
2. 提交 [Issue](../../issues)
3. 参与 [Discussions](../../discussions)

## 🎉 致谢

- [Dify](https://github.com/langgenius/dify) - 原始项目
- [vue-vben-admin](https://github.com/vbenjs/vue-vben-admin) - 架构参考
- Vue.js 生态系统的所有贡献者

---

**注意**: 这是一个迁移项目的技术方案和示例代码，实际实施时需要根据具体需求进行调整。
