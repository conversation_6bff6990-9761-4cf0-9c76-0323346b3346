<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Dify</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
</head>
<body>
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <!-- 主容器 -->
        <div class="w-full max-w-6xl mx-auto">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <div class="flex min-h-[600px]">
                    <!-- 左侧品牌区域 -->
                    <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 relative overflow-hidden">
                        <!-- 背景装饰元素 -->
                        <div class="absolute inset-0">
                            <!-- 动态背景圆圈 -->
                            <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                            <div class="absolute top-40 right-16 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
                            <div class="absolute bottom-32 left-16 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
                            <div class="absolute bottom-20 right-20 w-28 h-28 bg-white/10 rounded-full blur-xl animate-pulse-slow"></div>
                        </div>

                        <!-- 内容 -->
                        <div class="relative z-10 flex flex-col justify-center px-12 text-white">
                            <!-- Logo区域 -->
                            <div class="mb-12">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
                                        <div class="w-8 h-8 bg-gradient-to-br from-white to-white/80 rounded-lg flex items-center justify-center">
                                            <span class="text-indigo-600 font-bold text-lg">D</span>
                                        </div>
                                    </div>
                                    <div class="text-3xl font-bold">Dify</div>
                                </div>
                                <div class="w-16 h-1 bg-gradient-to-r from-white to-white/50 rounded-full"></div>
                            </div>

                            <!-- 主标题 -->
                            <h1 class="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                                轻松构建
                                <span class="block text-white/90">AI 应用</span>
                            </h1>

                            <!-- 描述 -->
                            <p class="text-xl text-white/80 mb-12 leading-relaxed">
                                使用我们直观的平台创建、部署和管理 AI 驱动的应用程序，具备先进的工作流功能。
                            </p>

                            <!-- 特性列表 -->
                            <div class="space-y-6">
                                <div class="flex items-center group">
                                    <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <span class="text-lg">可视化工作流构建器</span>
                                </div>
                                <div class="flex items-center group">
                                    <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <span class="text-lg">多种 AI 模型支持</span>
                                </div>
                                <div class="flex items-center group">
                                    <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <span class="text-lg">知识库集成</span>
                                </div>
                                <div class="flex items-center group">
                                    <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4 group-hover:bg-white/30 transition-colors">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <span class="text-lg">实时监控</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧表单区域 -->
                    <div class="flex-1 flex flex-col justify-center px-8 sm:px-12 lg:px-16 py-12">
                        <!-- 移动端Logo -->
                        <div class="lg:hidden text-center mb-8">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-lg">D</span>
                                </div>
                                <div class="text-3xl font-bold text-gray-900">Dify</div>
                            </div>
                        </div>

                        <!-- 表单内容 -->
                        <div class="w-full max-w-sm mx-auto">
                            <!-- 欢迎标题 -->
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-2">欢迎回来</h2>
                                <p class="text-gray-600">
                                    登录您的账户以继续使用
                                </p>
                            </div>

                            <!-- 登录表单 -->
                            <form class="space-y-6">
                                <!-- 邮箱输入 -->
                                <div class="space-y-2">
                                    <label for="email" class="block text-sm font-semibold text-gray-700">
                                        邮箱地址
                                    </label>
                                    <input
                                        id="email"
                                        name="email"
                                        type="email"
                                        autocomplete="email"
                                        required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors placeholder-gray-400 text-gray-900"
                                        placeholder="请输入您的邮箱地址"
                                    />
                                </div>

                                <!-- 密码输入 -->
                                <div class="space-y-2">
                                    <label for="password" class="block text-sm font-semibold text-gray-700">
                                        密码
                                    </label>
                                    <input
                                        id="password"
                                        name="password"
                                        type="password"
                                        autocomplete="current-password"
                                        required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors placeholder-gray-400 text-gray-900"
                                        placeholder="请输入您的密码"
                                    />
                                </div>

                                <!-- 记住我和忘记密码 -->
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <input
                                            id="remember-me"
                                            name="remember-me"
                                            type="checkbox"
                                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors"
                                        />
                                        <label for="remember-me" class="ml-3 block text-sm text-gray-700">
                                            记住我
                                        </label>
                                    </div>

                                    <div class="text-sm">
                                        <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors">
                                            忘记密码？
                                        </a>
                                    </div>
                                </div>

                                <!-- 登录按钮 -->
                                <div>
                                    <button
                                        type="submit"
                                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-lg hover:shadow-xl"
                                    >
                                        登录账户
                                    </button>
                                </div>
                            </form>

                            <!-- 注册链接 -->
                            <div class="mt-8 text-center">
                                <p class="text-sm text-gray-600">
                                    还没有账户？
                                    <a href="#" class="font-semibold text-indigo-600 hover:text-indigo-500 transition-colors">
                                        立即注册
                                    </a>
                                </p>
                            </div>
                        </div>

                        <!-- 底部链接 -->
                        <div class="mt-8 text-center text-sm text-gray-500 max-w-sm mx-auto">
                            <p>
                                继续使用即表示您同意我们的
                                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">服务条款</a>
                                和
                                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors">隐私政策</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
